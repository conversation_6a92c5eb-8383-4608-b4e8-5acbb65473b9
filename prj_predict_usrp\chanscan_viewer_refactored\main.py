#!/usr/bin/env python3
"""
信道扫描分段查看工具 - 重构版本

这是原有chanscan_viewer.py的重构版本，将7502行的单体文件拆分为多个模块化文件。

主要改进：
1. 模块化设计：将功能拆分为独立的模块
2. 可维护性：每个模块都有单一职责
3. 可扩展性：新功能可以轻松添加
4. 可读性：代码结构清晰，注释完整
5. 向后兼容：保持与原有代码完全一致的功能

模块结构：
- core/: 核心功能模块（应用程序、配置管理）
- ui/: 用户界面模块（窗口、面板、对话框、样式）
- data/: 数据处理模块（文件管理、信号处理、数据库管理）
- visualization/: 可视化模块（图表管理、绘图渲染）
- channel_scan/: 信道扫描模块（扫描器、分段管理）
- utils/: 工具模块（国际化、字体工具、消息框）
- existing_modules/: 现有模块（保持不变）

使用方法：
    python main.py

作者：chanscan_viewer team
版本：2.0.0 (重构版本)
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 检查Python版本
if sys.version_info < (3, 6):
    print("错误: 需要Python 3.6或更高版本")
    sys.exit(1)

# 导入必要的模块
try:
    import tkinter as tk
    from tkinter import messagebox
except ImportError:
    print("错误: 无法导入tkinter，请确保已安装Python的tkinter模块")
    sys.exit(1)

try:
    import numpy as np
    import matplotlib
    matplotlib.use('TkAgg')  # 设置matplotlib后端
    import matplotlib.pyplot as plt
except ImportError as e:
    print(f"错误: 无法导入必要的科学计算库: {e}")
    print("请安装必要的依赖包：pip install numpy matplotlib scipy")
    sys.exit(1)

# 导入主应用程序类
try:
    from core.application import ChannelScanViewerApp
except ImportError as e:
    print(f"错误: 无法导入应用程序模块: {e}")
    print("请确保所有模块文件都在正确的位置")
    sys.exit(1)


def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'scipy': 'scipy',
        'tkinter': 'tkinter (Python内置)'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(install_name)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请安装缺少的包：")
        print("pip install numpy matplotlib scipy")
        return False
    
    return True


def setup_environment():
    """设置运行环境"""
    # 设置matplotlib字体和后端
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        
        # 配置matplotlib以避免警告
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Liberation Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        
    except Exception as e:
        print(f"警告: matplotlib配置失败: {e}")
    
    # 设置工作目录
    try:
        os.chdir(project_root)
    except Exception as e:
        print(f"警告: 无法设置工作目录: {e}")


def show_startup_info():
    """显示启动信息"""
    print("=" * 60)
    print("信道扫描分段查看工具 - 重构版本 v2.0.0")
    print("=" * 60)
    print("模块化设计，提高可维护性和可扩展性")
    print("保持与原有代码完全一致的功能")
    print()
    print("模块结构:")
    print("  ├── core/           # 核心功能模块")
    print("  ├── ui/             # 用户界面模块")
    print("  ├── data/           # 数据处理模块")
    print("  ├── visualization/  # 可视化模块")
    print("  ├── channel_scan/   # 信道扫描模块")
    print("  ├── utils/          # 工具模块")
    print("  └── existing_modules/ # 现有模块")
    print()
    print("正在启动应用程序...")
    print("=" * 60)


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 用户中断，正常退出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 其他异常，显示错误信息
    import traceback
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    
    print(f"\n=== 应用程序异常 ===")
    print(error_msg)
    print("=" * 50)
    
    # 尝试显示GUI错误对话框
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        messagebox.showerror(
            "应用程序错误",
            f"应用程序遇到未处理的异常:\n\n{exc_type.__name__}: {exc_value}\n\n"
            f"详细信息已输出到控制台。"
        )
        root.destroy()
    except Exception:
        pass  # 如果GUI也失败了，就只输出到控制台


def main():
    """主函数"""
    try:
        # 设置全局异常处理
        sys.excepthook = handle_exception
        
        # 显示启动信息
        show_startup_info()
        
        # 检查依赖包
        if not check_dependencies():
            return 1
        
        # 设置运行环境
        setup_environment()
        
        # 创建并运行应用程序
        print("创建应用程序实例...")
        app = ChannelScanViewerApp()
        
        print("启动用户界面...")
        app.run()
        
        print("应用程序正常退出")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断，退出应用程序")
        return 0
        
    except Exception as e:
        print(f"\n启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
