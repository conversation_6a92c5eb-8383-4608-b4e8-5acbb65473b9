# 设计文档

## 概述

chanscan_viewer.py 是一个包含 7502 行代码的大型单体文件，包含了信道扫描分段查看工具的所有功能。为了提高代码的可维护性和可扩展性，需要将其重构为多个模块化的文件。

通过分析现有代码，发现主要包含以下功能模块：
- 用户界面管理
- 数据处理和可视化
- 数据库操作
- 配置管理
- 多语言支持
- 字体和样式管理
- 信道扫描功能

## 架构

### 模块划分策略

基于单一职责原则，将现有的大型文件拆分为以下模块：

```
chanscan_viewer/
├── __init__.py                    # 包初始化文件
├── main.py                        # 主程序入口
├── core/                          # 核心功能模块
│   ├── __init__.py
│   ├── application.py             # 主应用程序类
│   └── config_manager.py          # 配置管理
├── ui/                            # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py             # 主窗口
│   ├── toolbar.py                 # 工具栏
│   ├── panels.py                  # 面板管理
│   ├── dialogs.py                 # 对话框
│   └── styles.py                  # 样式和字体管理
├── data/                          # 数据处理模块
│   ├── __init__.py
│   ├── file_manager.py            # 文件管理
│   ├── signal_processor.py        # 信号处理
│   └── database_manager.py        # 数据库管理
├── visualization/                 # 可视化模块
│   ├── __init__.py
│   ├── chart_manager.py           # 图表管理
│   └── plot_renderer.py           # 绘图渲染
├── channel_scan/                  # 信道扫描模块
│   ├── __init__.py
│   ├── scanner.py                 # 扫描器
│   └── segment_manager.py         # 分段管理
├── utils/                         # 工具模块
│   ├── __init__.py
│   ├── i18n.py                    # 国际化支持
│   ├── font_utils.py              # 字体工具
│   └── message_box.py             # 消息框工具
└── existing_modules/              # 现有模块（保持不变）
    ├── frequency_dialog.py
    ├── progress_dialog.py
    ├── data_capture_controller.py
    └── adaptive_chart_renderer.py
```

## 组件和接口

### 1. 核心模块 (core/)

#### application.py
- **职责**: 主应用程序逻辑，协调各个模块
- **主要类**: `ChannelScanApplication`
- **接口**:
  - `initialize()`: 初始化应用程序
  - `run()`: 运行应用程序
  - `shutdown()`: 关闭应用程序

#### config_manager.py
- **职责**: 配置文件的加载、保存和管理
- **主要类**: `ConfigManager`
- **接口**:
  - `load_config()`: 加载配置
  - `save_config()`: 保存配置
  - `get_setting(key)`: 获取设置
  - `set_setting(key, value)`: 设置配置

### 2. 用户界面模块 (ui/)

#### main_window.py
- **职责**: 主窗口的创建和管理
- **主要类**: `MainWindow`
- **接口**:
  - `create_window()`: 创建主窗口
  - `setup_layout()`: 设置布局
  - `update_title(title)`: 更新标题

#### toolbar.py
- **职责**: 工具栏的创建和事件处理
- **主要类**: `Toolbar`
- **接口**:
  - `create_toolbar()`: 创建工具栏
  - `update_buttons()`: 更新按钮状态

#### panels.py
- **职责**: 各种面板的创建和管理
- **主要类**: `PanelManager`, `FileListPanel`, `FileInfoPanel`, `SegmentResultsPanel`
- **接口**:
  - `create_panels()`: 创建面板
  - `toggle_panel(panel_id)`: 切换面板状态
  - `update_panel_content(panel_id, content)`: 更新面板内容

#### dialogs.py
- **职责**: 各种对话框的创建和管理
- **主要类**: `DialogManager`
- **接口**:
  - `show_info(title, message)`: 显示信息对话框
  - `show_error(title, message)`: 显示错误对话框
  - `ask_yes_no(title, message)`: 显示确认对话框

#### styles.py
- **职责**: 字体、颜色和样式管理
- **主要类**: `StyleManager`
- **接口**:
  - `setup_fonts()`: 设置字体
  - `get_font(font_type)`: 获取字体
  - `apply_theme(theme_name)`: 应用主题

### 3. 数据处理模块 (data/)

#### file_manager.py
- **职责**: 文件的加载、保存和管理
- **主要类**: `FileManager`
- **接口**:
  - `load_file(file_path)`: 加载文件
  - `get_file_info(file_path)`: 获取文件信息
  - `get_supported_formats()`: 获取支持的格式

#### signal_processor.py
- **职责**: 信号数据的处理和分析
- **主要类**: `SignalProcessor`
- **接口**:
  - `process_signal(data)`: 处理信号数据
  - `calculate_spectrum(data)`: 计算频谱
  - `generate_spectrogram(data)`: 生成时频图

#### database_manager.py
- **职责**: 数据库的操作和管理
- **主要类**: `DatabaseManager`
- **接口**:
  - `connect()`: 连接数据库
  - `add_segment(segment_data)`: 添加分段数据
  - `delete_segment(segment_id)`: 删除分段数据
  - `migrate_database()`: 迁移数据库

### 4. 可视化模块 (visualization/)

#### chart_manager.py
- **职责**: 图表的创建和管理
- **主要类**: `ChartManager`
- **接口**:
  - `create_charts()`: 创建图表
  - `update_chart(chart_type, data)`: 更新图表
  - `clear_charts()`: 清空图表

#### plot_renderer.py
- **职责**: 图表的渲染和绘制
- **主要类**: `PlotRenderer`
- **接口**:
  - `render_time_domain(data)`: 渲染时域图
  - `render_frequency_spectrum(data)`: 渲染频谱图
  - `render_spectrogram(data)`: 渲染时频图

### 5. 信道扫描模块 (channel_scan/)

#### scanner.py
- **职责**: 信道扫描功能的实现
- **主要类**: `ChannelScanner`
- **接口**:
  - `scan_channels(data, params)`: 扫描信道
  - `set_scan_parameters(params)`: 设置扫描参数

#### segment_manager.py
- **职责**: 分段结果的管理
- **主要类**: `SegmentManager`
- **接口**:
  - `add_segment(segment)`: 添加分段
  - `delete_segment(segment_id)`: 删除分段
  - `get_selected_segments()`: 获取选中的分段

### 6. 工具模块 (utils/)

#### i18n.py
- **职责**: 多语言支持
- **主要类**: `I18nManager`
- **接口**:
  - `set_language(language)`: 设置语言
  - `get_text(key)`: 获取文本
  - `get_all_texts()`: 获取所有文本

#### font_utils.py
- **职责**: 字体相关工具函数
- **函数**:
  - `configure_matplotlib_fonts()`: 配置matplotlib字体
  - `get_available_fonts()`: 获取可用字体
  - `find_best_font(candidates)`: 查找最佳字体

#### message_box.py
- **职责**: 自定义消息框
- **主要类**: `ScaledMessageBox`
- **接口**: 保持现有的静态方法接口

## 数据模型

### 配置数据模型
```python
@dataclass
class AppConfig:
    last_file_dir: str
    last_folder_dir: str
    last_model_dir: str
    language: str
    window_geometry: str
    panel_states: Dict[str, bool]
```

### 文件信息数据模型
```python
@dataclass
class FileInfo:
    path: str
    size: int
    format: str
    sample_rate: float
    center_frequency: float
    duration: float
```

### 分段数据模型
```python
@dataclass
class Segment:
    id: str
    start_time: float
    end_time: float
    start_freq: float
    end_freq: float
    confidence: float
    selected: bool
```

## 错误处理

### 错误处理策略
1. **模块级错误处理**: 每个模块内部处理自己的错误
2. **统一错误报告**: 通过事件系统向上层报告错误
3. **用户友好的错误信息**: 将技术错误转换为用户可理解的信息
4. **错误日志记录**: 记录详细的错误信息用于调试

### 错误类型定义
```python
class ChanscanViewerError(Exception):
    """基础异常类"""
    pass

class FileLoadError(ChanscanViewerError):
    """文件加载错误"""
    pass

class DatabaseError(ChanscanViewerError):
    """数据库操作错误"""
    pass

class ScanError(ChanscanViewerError):
    """扫描操作错误"""
    pass
```

## 测试策略

### 单元测试
- 每个模块都应该有对应的单元测试
- 测试覆盖率目标：80%以上
- 使用 pytest 作为测试框架

### 集成测试
- 测试模块之间的交互
- 测试完整的用户操作流程

### 测试文件结构
```
tests/
├── unit/
│   ├── test_config_manager.py
│   ├── test_file_manager.py
│   ├── test_signal_processor.py
│   └── ...
├── integration/
│   ├── test_file_loading_flow.py
│   ├── test_scanning_flow.py
│   └── ...
└── fixtures/
    ├── sample_data/
    └── test_configs/
```

## 迁移计划

### 阶段1: 基础架构
1. 创建新的目录结构
2. 提取工具函数到 utils 模块
3. 创建基础的应用程序框架

### 阶段2: 核心功能迁移
1. 迁移配置管理功能
2. 迁移文件管理功能
3. 迁移数据库管理功能

### 阶段3: 界面模块迁移
1. 迁移主窗口创建逻辑
2. 迁移面板管理功能
3. 迁移对话框功能

### 阶段4: 专业功能迁移
1. 迁移信号处理功能
2. 迁移可视化功能
3. 迁移信道扫描功能

### 阶段5: 测试和优化
1. 编写单元测试
2. 进行集成测试
3. 性能优化和代码清理