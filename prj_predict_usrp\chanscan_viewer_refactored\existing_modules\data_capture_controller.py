#!/usr/bin/env python3
"""
数据采集控制模块

提供用于控制USRP数据采集的功能
"""

import os
import sys
import traceback
from pathlib import Path

# 添加项目路径到系统路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class DataCaptureController:
    """
    数据采集控制类
    
    提供用于控制USRP数据采集的功能
    """
    
    @staticmethod
    def check_usrp_available():
        """
        检查USRP设备是否可用
        
        Returns:
            tuple: (是否可用, 错误信息)
        """
        try:
            # 导入UHD库
            import uhd
            
            # 只检查UHD库是否可用，不实际初始化设备
            # 这样可以避免长时间等待，提高用户体验
            return True, None
        except ImportError:
            return False, "未安装UHD库，无法使用USRP设备"
        except Exception as e:
            return False, f"检查USRP设备时出错: {str(e)}"
    
    @staticmethod
    def initialize_usrp():
        """
        初始化USRP设备
        
        Returns:
            tuple: (是否成功, 设备对象或错误信息)
        """
        try:
            # 导入UHD库
            import uhd
            
            # 尝试创建USRP设备
            try:
                usrp = uhd.usrp.MultiUSRP("")
                
                # 获取设备信息
                device_info = usrp.get_usrp_rx_info()
                
                # 如果成功获取设备信息，则设备可用
                return True, usrp
            except RuntimeError as e:
                if "No devices found" in str(e):
                    return False, "未找到USRP设备，请检查设备连接"
                elif "claimed by other process" in str(e):
                    return False, "USRP设备已被其他进程占用"
                else:
                    return False, f"USRP设备初始化失败: {str(e)}"
            except Exception as e:
                return False, f"USRP设备初始化失败: {str(e)}"
        except ImportError:
            return False, "未安装UHD库，无法使用USRP设备"
        except Exception as e:
            return False, f"检查USRP设备时出错: {str(e)}"
    
    @staticmethod
    def capture_data(folder_path, freq_hz, progress_callback=None, num_files=10):
        """
        采集数据

        Args:
            folder_path: 保存数据的文件夹路径
            freq_hz: 中心频率，单位Hz
            progress_callback: 进度回调函数，接受三个参数：当前文件索引、总文件数、状态消息
            num_files: 要采集的文件个数，默认为10

        Returns:
            tuple: (是否成功, 结果信息, 文件数量)
        """
        try:
            # 导入必要的模块
            try:
                from chanlib.usrp_samp import samp_data2folder
            except ImportError as e:
                return False, f"无法导入USRP采样模块: {str(e)}", 0
            
            # 检查文件夹路径
            if not folder_path or not isinstance(folder_path, str):
                return False, "无效的文件夹路径", 0
            
            # 检查频率
            if not freq_hz or not isinstance(freq_hz, (int, float)) or freq_hz <= 0:
                return False, "无效的频率值", 0
            
            # 初始化USRP设备
            if progress_callback:
                progress_callback(0, 10, "正在初始化USRP设备...")
            
            success, result = DataCaptureController.initialize_usrp()
            if not success:
                return False, f"USRP设备初始化失败: {result}", 0
            
            # 使用初始化好的设备进行数据采集
            if progress_callback:
                progress_callback(0, num_files, "USRP设备初始化成功，开始采集数据...")

            # 调用USRP数据采集函数
            file_count = samp_data2folder(folder_path, freq_hz, progress_callback, result, num_files)
            
            # 检查采集结果
            if file_count is None or file_count <= 0:
                return False, "数据采集失败，未生成任何文件", 0
            
            return True, f"成功采集 {file_count} 个数据文件", file_count
        except Exception as e:
            # 获取详细的错误信息
            error_info = traceback.format_exc()
            print(f"数据采集错误: {error_info}")
            return False, f"数据采集失败: {str(e)}", 0
    
    @staticmethod
    def check_folder_permissions(folder_path):
        """
        检查文件夹权限
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            tuple: (是否有权限, 错误信息)
        """
        try:
            # 检查路径是否为空
            if not folder_path or not isinstance(folder_path, str):
                return False, "无效的文件夹路径"
            
            # 检查路径是否过长
            if len(folder_path) > 260:  # Windows路径长度限制
                return False, f"文件夹路径过长 ({len(folder_path)} 字符)"
            
            # 检查路径是否包含非法字符
            import re
            if os.name == 'nt':  # Windows系统
                # 只检查文件名部分，不检查完整路径（因为路径中的冒号是合法的）
                # 在Windows系统中，文件名不能包含以下字符: < > : " | ? *
                # 但路径中可以包含冒号（如C:），下划线和中文字符是合法的
                path_parts = folder_path.split(os.sep)
                for part in path_parts:
                    # 检查每个路径部分是否包含非法字符，但忽略驱动器号中的冒号（如C:）
                    if part and (len(part) > 1 or not part.endswith(':')) and re.search(r'[<>"|?*]', part):
                        return False, f"文件夹路径包含非法字符: '{part}' (< > \" | ? *)"
            
            # 检查路径是否为文件
            if os.path.exists(folder_path) and not os.path.isdir(folder_path):
                return False, f"'{folder_path}' 是一个文件，不是文件夹"
            
            # 如果文件夹不存在，尝试创建
            if not os.path.exists(folder_path):
                try:
                    os.makedirs(folder_path)
                except PermissionError:
                    return False, f"没有权限创建文件夹 '{folder_path}'"
                except OSError as e:
                    return False, f"创建文件夹失败: {str(e)}"
            
            # 检查磁盘空间
            try:
                import shutil
                free_space = shutil.disk_usage(folder_path).free
                if free_space < 100 * 1024 * 1024:  # 小于100MB
                    return False, f"磁盘空间不足，仅剩 {free_space / (1024*1024):.1f} MB"
            except Exception as e:
                print(f"检查磁盘空间时出错: {str(e)}")
                # 继续执行，不中断流程
            
            # 测试写入权限
            test_file = os.path.join(folder_path, ".test_write_permission")
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except PermissionError:
                return False, f"没有权限写入文件夹 '{folder_path}'"
            except OSError as e:
                return False, f"写入测试文件失败: {str(e)}"
            
            return True, None
        except (PermissionError, OSError) as e:
            return False, f"无法写入文件夹 '{folder_path}': {str(e)}"
        except Exception as e:
            import traceback
            error_detail = traceback.format_exc()
            print(f"检查文件夹权限时出错: {error_detail}")
            return False, f"检查文件夹权限时出错: {str(e)}"