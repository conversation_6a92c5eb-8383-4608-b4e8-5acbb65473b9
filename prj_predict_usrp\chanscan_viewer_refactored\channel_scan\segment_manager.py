"""
分段管理模块

负责信号分段的管理、编辑和操作，包括分段选择、删除、添加到数据库等功能。
保持与原有代码完全一致的分段管理逻辑。
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass

# 添加项目路径以导入相关模块
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from proc_wbsig import extract_and_save_signals
    PROC_WBSIG_AVAILABLE = True
except ImportError:
    PROC_WBSIG_AVAILABLE = False
    print("警告: proc_wbsig不可用，分段信号处理功能受限")


@dataclass
class Segment:
    """分段数据类"""
    start_sample: int
    end_sample: int
    center_freq: float
    bandwidth: float
    confidence: float
    class_name: str = "Unknown"
    snr: float = 0.0
    duration: float = 0.0
    selected: bool = False


class SegmentManager:
    """
    分段管理器
    
    负责信号分段的管理、编辑和操作，保持与原有代码完全一致的分段管理逻辑。
    """
    
    def __init__(self, i18n_manager):
        """
        初始化分段管理器
        
        Args:
            i18n_manager: 国际化管理器
        """
        self.i18n_manager = i18n_manager
        self.segments = []
        self.segment_selected = {}
        
        # 回调函数
        self.update_display_callback = None
        self.update_button_state_callback = None
    
    def set_callbacks(self, update_display_callback: Optional[Callable] = None,
                     update_button_state_callback: Optional[Callable] = None):
        """
        设置回调函数
        
        Args:
            update_display_callback: 更新显示回调函数
            update_button_state_callback: 更新按钮状态回调函数
        """
        self.update_display_callback = update_display_callback
        self.update_button_state_callback = update_button_state_callback
    
    def add_segments(self, segments_data: List[Dict[str, Any]]):
        """
        添加分段数据
        
        Args:
            segments_data: 分段数据列表
        """
        self.segments = []
        self.segment_selected = {}
        
        for i, segment_data in enumerate(segments_data):
            segment = Segment(
                start_sample=segment_data.get('start_sample', 0),
                end_sample=segment_data.get('end_sample', 0),
                center_freq=segment_data.get('center_freq', 0.0),
                bandwidth=segment_data.get('bandwidth', 0.0),
                confidence=segment_data.get('confidence', 0.0),
                class_name=segment_data.get('class_name', 'Unknown'),
                snr=segment_data.get('snr', 0.0),
                duration=segment_data.get('duration', 0.0)
            )
            self.segments.append(segment)
            self.segment_selected[i] = False
        
        # 更新显示
        if self.update_display_callback:
            self.update_display_callback()
        if self.update_button_state_callback:
            self.update_button_state_callback()
    
    def clear_segments(self):
        """清空所有分段"""
        self.segments = []
        self.segment_selected = {}
        
        # 更新显示
        if self.update_display_callback:
            self.update_display_callback()
        if self.update_button_state_callback:
            self.update_button_state_callback()
    
    def get_segments(self) -> List[Segment]:
        """
        获取所有分段
        
        Returns:
            分段列表
        """
        return self.segments.copy()
    
    def get_segment_count(self) -> int:
        """
        获取分段数量
        
        Returns:
            分段数量
        """
        return len(self.segments)
    
    def get_segment_by_index(self, index: int) -> Optional[Segment]:
        """
        根据索引获取分段
        
        Args:
            index: 分段索引
            
        Returns:
            分段对象，如果索引无效则返回None
        """
        if 0 <= index < len(self.segments):
            return self.segments[index]
        return None
    
    def toggle_segment_selection(self, index: int):
        """
        切换分段的选中状态
        
        Args:
            index: 分段索引
        """
        if 0 <= index < len(self.segments):
            current_state = self.segment_selected.get(index, False)
            self.segment_selected[index] = not current_state
            
            # 更新显示
            if self.update_display_callback:
                self.update_display_callback()
            if self.update_button_state_callback:
                self.update_button_state_callback()
    
    def select_all_segments(self):
        """选中所有分段"""
        for i in range(len(self.segments)):
            self.segment_selected[i] = True
        
        # 更新显示
        if self.update_display_callback:
            self.update_display_callback()
        if self.update_button_state_callback:
            self.update_button_state_callback()
    
    def deselect_all_segments(self):
        """取消选中所有分段"""
        for i in range(len(self.segments)):
            self.segment_selected[i] = False
        
        # 更新显示
        if self.update_display_callback:
            self.update_display_callback()
        if self.update_button_state_callback:
            self.update_button_state_callback()
    
    def get_selected_segments(self) -> List[Tuple[int, Segment]]:
        """
        获取选中的分段
        
        Returns:
            (索引, 分段)元组列表
        """
        selected_segments = []
        for i, selected in self.segment_selected.items():
            if selected and i < len(self.segments):
                selected_segments.append((i, self.segments[i]))
        return selected_segments
    
    def get_selected_count(self) -> int:
        """
        获取选中分段的数量
        
        Returns:
            选中分段数量
        """
        return sum(1 for selected in self.segment_selected.values() if selected)
    
    def delete_selected_segments(self) -> int:
        """
        删除选中的分段
        
        Returns:
            删除的分段数量
        """
        selected_indices = [i for i, selected in self.segment_selected.items() if selected]
        
        if not selected_indices:
            return 0
        
        # 按索引倒序删除，避免索引变化问题
        selected_indices.sort(reverse=True)
        
        for index in selected_indices:
            if 0 <= index < len(self.segments):
                del self.segments[index]
        
        # 重新构建选择状态字典
        new_segment_selected = {}
        for i in range(len(self.segments)):
            new_segment_selected[i] = False
        self.segment_selected = new_segment_selected
        
        # 更新显示
        if self.update_display_callback:
            self.update_display_callback()
        if self.update_button_state_callback:
            self.update_button_state_callback()
        
        return len(selected_indices)
    
    def update_segment_position(self, index: int, start_sample: int, end_sample: int) -> bool:
        """
        更新分段位置
        
        Args:
            index: 分段索引
            start_sample: 新的起始采样点
            end_sample: 新的结束采样点
            
        Returns:
            是否更新成功
        """
        if 0 <= index < len(self.segments):
            segment = self.segments[index]
            
            # 验证位置有效性
            if start_sample >= 0 and end_sample > start_sample:
                segment.start_sample = start_sample
                segment.end_sample = end_sample
                
                # 重新计算持续时间（如果有采样率信息）
                # 这里需要从外部传入采样率信息
                
                # 更新显示
                if self.update_display_callback:
                    self.update_display_callback()
                
                return True
        
        return False
    
    def process_segment_signal(self, wb_signal_data, segment_index: int, 
                             start_pos: int, end_pos: int, fs: float) -> Optional[Any]:
        """
        对修改后的分段信号进行与 extract_and_save_signals 相同的窄带信号处理
        
        Args:
            wb_signal_data: 宽带信号数据
            segment_index: 分段索引
            start_pos: 起始位置
            end_pos: 结束位置
            fs: 采样率
            
        Returns:
            处理后的信号数据，失败返回None
        """
        if not PROC_WBSIG_AVAILABLE:
            print("proc_wbsig不可用，无法处理分段信号")
            return None
        
        try:
            # 提取分段信号
            segment_signal = wb_signal_data[start_pos:end_pos]
            
            # 使用与 extract_and_save_signals 相同的处理方法
            # 这里需要根据实际的 extract_and_save_signals 实现来调整
            processed_signal = extract_and_save_signals(
                signal_data=segment_signal,
                fs=fs,
                # 其他必要参数...
            )
            
            return processed_signal
            
        except Exception as e:
            print(f"处理分段信号失败: {e}")
            return None
    
    def export_segments_to_dict(self) -> List[Dict[str, Any]]:
        """
        将分段导出为字典列表
        
        Returns:
            字典格式的分段列表
        """
        segments_data = []
        for segment in self.segments:
            segment_dict = {
                'start_sample': segment.start_sample,
                'end_sample': segment.end_sample,
                'center_freq': segment.center_freq,
                'bandwidth': segment.bandwidth,
                'confidence': segment.confidence,
                'class_name': segment.class_name,
                'snr': segment.snr,
                'duration': segment.duration
            }
            segments_data.append(segment_dict)
        return segments_data
    
    def get_segment_statistics(self) -> Dict[str, Any]:
        """
        获取分段统计信息
        
        Returns:
            统计信息字典
        """
        if not self.segments:
            return {
                'total_segments': 0,
                'selected_segments': 0,
                'avg_confidence': 0.0,
                'max_confidence': 0.0,
                'min_confidence': 0.0,
                'class_distribution': {}
            }
        
        confidences = [segment.confidence for segment in self.segments]
        class_names = [segment.class_name for segment in self.segments]
        
        # 统计类别分布
        class_distribution = {}
        for class_name in class_names:
            class_distribution[class_name] = class_distribution.get(class_name, 0) + 1
        
        return {
            'total_segments': len(self.segments),
            'selected_segments': self.get_selected_count(),
            'avg_confidence': sum(confidences) / len(confidences),
            'max_confidence': max(confidences),
            'min_confidence': min(confidences),
            'class_distribution': class_distribution
        }
    
    def filter_segments_by_confidence(self, min_confidence: float) -> List[Segment]:
        """
        根据置信度过滤分段
        
        Args:
            min_confidence: 最小置信度
            
        Returns:
            过滤后的分段列表
        """
        return [segment for segment in self.segments if segment.confidence >= min_confidence]
    
    def filter_segments_by_class(self, class_name: str) -> List[Segment]:
        """
        根据类别过滤分段
        
        Args:
            class_name: 类别名称
            
        Returns:
            过滤后的分段列表
        """
        return [segment for segment in self.segments if segment.class_name == class_name]
    
    def is_segment_selected(self, index: int) -> bool:
        """
        检查分段是否被选中
        
        Args:
            index: 分段索引
            
        Returns:
            是否被选中
        """
        return self.segment_selected.get(index, False)
