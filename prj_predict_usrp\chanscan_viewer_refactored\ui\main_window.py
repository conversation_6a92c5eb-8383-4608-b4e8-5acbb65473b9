"""
主窗口模块

负责主窗口的创建、配置和管理，包括窗口大小、位置、标题等设置。
严格保持与原有代码相同的窗口配置。
"""

import os
import platform
import tkinter as tk
from typing import Optional, Callable, Dict, Any


class MainWindow:
    """
    主窗口管理器
    
    负责主窗口的创建和配置，保持与原有代码完全一致的窗口设置。
    """
    
    def __init__(self, title: str = "信道扫描分段查看工具", language: str = 'zh'):
        """
        初始化主窗口
        
        Args:
            title: 窗口标题
            language: 当前语言
        """
        self.root = tk.Tk()
        self.title = title
        self.language = language
        self.active_dialogs = []  # 活动对话框列表
        
        # 设置窗口
        self.setup_window()
        self.setup_file_dialog_options()
        
        # 绑定关闭事件
        self.on_closing_callback = None
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def setup_window(self):
        """
        设置主窗口
        
        保持与原有代码完全一致的窗口配置逻辑
        """
        self.root.title(self.title)

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 使用与原版本一致的窗口尺寸设置
        # 特别是通过xshell连接Linux服务器时，屏幕尺寸可能不准确
        if platform.system() == 'Linux':
            # Linux系统：使用固定的合理尺寸
            window_width = 1600
            window_height = 1000
        else:
            # Windows和其他系统：根据屏幕尺寸计算（与原版本一致）
            window_width = int(screen_width * 0.8)
            window_height = int(screen_height * 0.8)

        # 计算窗口位置（居中）
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口几何
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 设置最小尺寸
        self.root.minsize(1000, 700)
        
        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置窗口图标
            pass
        except Exception:
            pass  # 忽略图标设置失败
    
    def setup_file_dialog_options(self):
        """
        设置文件对话框选项，特别是Linux下的大小
        
        保持与原有代码完全一致的对话框配置
        """
        # 设置tkinter文件对话框的选项
        try:
            self.root.option_add('*TkFDialog*foreground', 'black')
            self.root.option_add('*TkFDialog*selectBackground', '#0078d4')
            if platform.system() == 'Linux':
                # 尝试设置Linux下的对话框大小
                self.root.option_add('*TkFDialog*width', '1000')
                self.root.option_add('*TkFDialog*height', '700')
        except Exception as e:
            print(f"设置tkinter对话框选项失败: {e}")
    
    def set_title(self, title: str):
        """
        设置窗口标题
        
        Args:
            title: 新的窗口标题
        """
        self.title = title
        self.root.title(title)
    
    def get_root(self) -> tk.Tk:
        """
        获取根窗口对象
        
        Returns:
            tkinter根窗口对象
        """
        return self.root
    
    def set_on_closing_callback(self, callback: Callable):
        """
        设置窗口关闭回调函数
        
        Args:
            callback: 关闭时调用的回调函数
        """
        self.on_closing_callback = callback
    
    def _on_closing(self):
        """内部窗口关闭处理"""
        if self.on_closing_callback:
            self.on_closing_callback()
        else:
            self.root.quit()
            self.root.destroy()
    
    def show(self):
        """显示窗口"""
        self.root.deiconify()
    
    def hide(self):
        """隐藏窗口"""
        self.root.withdraw()
    
    def center_window(self, width: Optional[int] = None, height: Optional[int] = None):
        """
        将窗口居中显示
        
        Args:
            width: 窗口宽度，如果为None则使用当前宽度
            height: 窗口高度，如果为None则使用当前高度
        """
        if width is None:
            width = self.root.winfo_width()
        if height is None:
            height = self.root.winfo_height()
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_geometry(self, width: int, height: int, x: Optional[int] = None, y: Optional[int] = None):
        """
        设置窗口几何属性
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            x: 窗口x坐标，如果为None则居中
            y: 窗口y坐标，如果为None则居中
        """
        if x is None or y is None:
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - width) // 2 if x is None else x
            y = (screen_height - height) // 2 if y is None else y
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_resizable(self, width: bool = True, height: bool = True):
        """
        设置窗口是否可调整大小
        
        Args:
            width: 是否可调整宽度
            height: 是否可调整高度
        """
        self.root.resizable(width, height)
    
    def set_minimum_size(self, width: int, height: int):
        """
        设置窗口最小尺寸
        
        Args:
            width: 最小宽度
            height: 最小高度
        """
        self.root.minsize(width, height)
    
    def set_maximum_size(self, width: int, height: int):
        """
        设置窗口最大尺寸
        
        Args:
            width: 最大宽度
            height: 最大高度
        """
        self.root.maxsize(width, height)
    
    def set_icon(self, icon_path: str):
        """
        设置窗口图标
        
        Args:
            icon_path: 图标文件路径
        """
        try:
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"设置窗口图标失败: {e}")
    
    def add_active_dialog(self, dialog: tk.Toplevel):
        """
        添加活动对话框到列表
        
        Args:
            dialog: 对话框窗口
        """
        self.active_dialogs.append(dialog)
    
    def remove_active_dialog(self, dialog: tk.Toplevel):
        """
        从活动对话框列表中移除对话框
        
        Args:
            dialog: 对话框窗口
        """
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def ensure_dialogs_on_top(self):
        """确保活动的对话框保持在顶层"""
        try:
            # 清理已关闭的对话框
            self.active_dialogs = [dialog for dialog in self.active_dialogs 
                                 if dialog.winfo_exists()]
            
            # 将活动对话框置于顶层
            for dialog in self.active_dialogs:
                try:
                    dialog.lift()
                    dialog.attributes('-topmost', True)
                    dialog.attributes('-topmost', False)  # 取消置顶，但保持在前面
                except Exception:
                    pass  # 忽略已关闭的对话框
        except Exception as e:
            print(f"确保对话框在顶层时出错: {e}")
    
    def update(self):
        """更新窗口"""
        self.root.update()
    
    def update_idletasks(self):
        """更新空闲任务"""
        self.root.update_idletasks()
    
    def after(self, ms: int, func: Callable, *args):
        """
        延迟执行函数
        
        Args:
            ms: 延迟毫秒数
            func: 要执行的函数
            *args: 函数参数
        """
        return self.root.after(ms, func, *args)
    
    def mainloop(self):
        """启动主事件循环"""
        self.root.mainloop()
    
    def quit(self):
        """退出应用程序"""
        self.root.quit()
    
    def destroy(self):
        """销毁窗口"""
        self.root.destroy()
    
    def get_window_info(self) -> Dict[str, Any]:
        """
        获取窗口信息
        
        Returns:
            包含窗口信息的字典
        """
        return {
            'width': self.root.winfo_width(),
            'height': self.root.winfo_height(),
            'x': self.root.winfo_rootx(),
            'y': self.root.winfo_rooty(),
            'screen_width': self.root.winfo_screenwidth(),
            'screen_height': self.root.winfo_screenheight(),
            'title': self.title
        }
