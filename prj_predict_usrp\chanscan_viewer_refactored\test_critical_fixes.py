#!/usr/bin/env python3
"""
测试关键修复：数据库窗口、信道扫描、文件选择绘图更新
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_database_window_data_format():
    """测试数据库窗口数据格式修复"""
    print("测试数据库窗口数据格式修复...")
    
    try:
        with open('data/database_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据类型转换修复
        format_checks = [
            'sample_rate_val = float(record[4])',
            'bandwidth_val = float(record[5])',
            'sample_rate_display = f"{sample_rate_val/1e6:.2f} MHz"',
            'bandwidth_display = f"{bandwidth_val/1e6:.2f} MHz"',
            'except (ValueError, TypeError):'
        ]
        
        for check in format_checks:
            if check in content:
                print(f"✅ 找到数据格式修复: {check}")
            else:
                print(f"❌ 缺少数据格式修复: {check}")
                return False
        
        # 检查中文表头设置
        chinese_headers = [
            "tree.heading('select', text='选择')",
            "tree.heading('record_id', text='数据编号')",
            "tree.heading('class_id', text='类别ID')",
            "tree.heading('class_name', text='类别名称')",
            "tree.heading('file_path', text='文件路径')",
            "tree.heading('sample_rate', text='采样率')",
            "tree.heading('bandwidth', text='带宽')"
        ]
        
        for header in chinese_headers:
            if header in content:
                print(f"✅ 找到中文表头: {header}")
            else:
                print(f"❌ 缺少中文表头: {header}")
                return False
        
        # 检查查看分段按钮功能
        if 'def show_record_analysis_window(' in content:
            print("✅ 查看分段按钮功能已实现")
        else:
            print("❌ 查看分段按钮功能未实现")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库窗口数据格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_channel_scan_fix():
    """测试信道扫描修复"""
    print("\n测试信道扫描修复...")
    
    try:
        with open('channel_scan/scanner.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查函数调用参数修复
        scan_checks = [
            'success, segments, fs_val_scalar, error_msg = run_channel_scan(',
            'signal_data=signal_data,',
            'metadata=metadata,',
            'progress_callback=internal_progress_callback,',
            'original_file_path=None'
        ]
        
        for check in scan_checks:
            if check in content:
                print(f"✅ 找到扫描参数修复: {check}")
            else:
                print(f"❌ 缺少扫描参数修复: {check}")
                return False
        
        # 检查是否移除了错误的model_path参数
        if 'model_path=model_path' not in content:
            print("✅ 移除了错误的model_path参数")
        else:
            print("❌ 仍然存在错误的model_path参数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 信道扫描修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_segment_manager_callbacks():
    """测试分段管理器回调设置"""
    print("\n测试分段管理器回调设置...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查回调设置方法
        callback_checks = [
            'def setup_segment_manager_callbacks(self):',
            'def update_segment_display():',
            'self.panels.update_segment_list(segments)',
            'self.segment_manager.set_callbacks(',
            'update_display_callback=update_segment_display',
            'update_button_state_callback=update_button_states'
        ]
        
        for check in callback_checks:
            if check in content:
                print(f"✅ 找到回调设置: {check}")
            else:
                print(f"❌ 缺少回调设置: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 分段管理器回调测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_segment_list_update():
    """测试分段列表更新功能"""
    print("\n测试分段列表更新功能...")
    
    try:
        with open('ui/panels.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查update_segment_list方法
        update_checks = [
            'def update_segment_list(self, segments_data):',
            'for item in self.segments_tree.get_children():',
            'self.segments_tree.delete(item)',
            'self.segment_selected = {}',
            'segment_id = f"S{i+1}"',
            'position = f"{segment.get(\'start_sample\', 0)}-{segment.get(\'end_sample\', 0)}"',
            'center_freq = f"{segment.get(\'center_freq\', 0.0)/1e6:.2f}"',
            'self.segments_tree.insert(\'\', \'end\', values=values)'
        ]
        
        for check in update_checks:
            if check in content:
                print(f"✅ 找到分段列表更新: {check}")
            else:
                print(f"❌ 缺少分段列表更新: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 分段列表更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_selection_debug():
    """测试文件选择调试信息"""
    print("\n测试文件选择调试信息...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查调试信息添加
        debug_checks = [
            'print(f"文件选择: 索引={index}, 文件={current_files[index]}")',
            'print(f"开始加载文件: {current_files[index]}")',
            'self.update_status(f"已加载文件: {filename}")',
            'except Exception as e:',
            'traceback.print_exc()'
        ]
        
        for check in debug_checks:
            if check in content:
                print(f"✅ 找到文件选择调试: {check}")
            else:
                print(f"❌ 缺少文件选择调试: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件选择调试测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_axis_fixes():
    """测试绘图坐标轴修复"""
    print("\n测试绘图坐标轴修复...")
    
    try:
        with open('visualization/plot_renderer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查坐标轴设置修复
        axis_checks = [
            'ax.set_xlim(0, x_max_1e5)',  # 时域信号点值图x轴
            'ax.set_xlim(signal_time_start, signal_time_end)',  # 时域信号图x轴
            'ax.set_xlim(freqs_mhz[0], freqs_mhz[-1])',  # 频谱图x轴
            "ax.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)",  # 频谱图绿色线条
            'f\'{texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]\'',  # 频谱图标题
            'f\'{texts["time_frequency"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]\''  # 时频图标题
        ]
        
        for check in axis_checks:
            if check in content:
                print(f"✅ 找到坐标轴修复: {check}")
            else:
                print(f"❌ 缺少坐标轴修复: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 绘图坐标轴修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试关键修复...")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        test_database_window_data_format,
        test_channel_scan_fix,
        test_segment_manager_callbacks,
        test_segment_list_update,
        test_file_selection_debug,
        test_plot_axis_fixes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 80)
    print(f"关键修复测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有关键修复测试通过！")
        print("\n✅ 修复完成的问题：")
        print("  1. 数据库窗口数据类型转换错误 - 修复了字符串转浮点数问题")
        print("  2. 数据库窗口中文表头 - 设置了与原版本一致的中文列标题")
        print("  3. 查看分段按钮功能 - 实现了完整的分析图显示窗口")
        print("  4. 信道扫描参数错误 - 修正了函数调用参数，移除错误的model_path")
        print("  5. 分段列表不显示 - 设置了分段管理器回调，实现了列表更新")
        print("  6. 文件选择调试信息 - 添加了详细的调试输出和状态更新")
        print("  7. 绘图坐标轴设置 - 修复了x轴范围、频谱图颜色和标题格式")
        print("\n🎯 所有关键问题都已修复，可以进行实际测试！")
        return True
    else:
        print("❌ 部分关键修复测试失败，需要进一步修复")
        failed_count = total - passed
        print(f"   失败的测试数量: {failed_count}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
