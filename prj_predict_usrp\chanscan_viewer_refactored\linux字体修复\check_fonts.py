#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速字体检查脚本
用于检查Linux系统上的字体安装情况
"""

import platform
import sys

def check_fonts():
    """检查字体安装情况"""
    if platform.system() != 'Linux':
        print("此脚本仅适用于Linux系统")
        return
    
    try:
        import tkinter as tk
        import tkinter.font as tkFont
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        available_fonts = list(tkFont.families())
        print(f"系统总字体数量: {len(available_fonts)}")
        
        # 检查中文字体
        chinese_fonts = [
            'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN',
            'Liberation Sans', 'DejaVu Sans'
        ]
        
        print("\n=== 预期中文字体检查 ===")
        found_fonts = []
        for font in chinese_fonts:
            if font in available_fonts:
                print(f"✓ {font}")
                found_fonts.append(font)
            else:
                print(f"✗ {font}")
        
        # 搜索可能的中文字体
        print("\n=== 搜索可能的中文字体 ===")
        keywords = ['zh', 'cn', 'chinese', 'cjk', 'han', 'wenquanyi', 'noto', 'droid', 'wqy']
        chinese_related = []
        for font in available_fonts:
            if any(keyword in font.lower() for keyword in keywords):
                chinese_related.append(font)
        
        if chinese_related:
            print("找到可能的中文字体:")
            for font in sorted(chinese_related):
                print(f"  - {font}")
        else:
            print("未找到明显的中文字体")
        
        # 显示一些常用字体
        print("\n=== 常用字体检查 ===")
        common_fonts = ['Arial', 'Times', 'Helvetica', 'Courier', 'Liberation Sans', 'DejaVu Sans']
        for font in common_fonts:
            status = "✓" if font in available_fonts else "✗"
            print(f"{status} {font}")
        
        # 销毁窗口
        root.destroy()
        
        # 总结
        print(f"\n=== 总结 ===")
        print(f"预期中文字体: {len(found_fonts)}/{len(chinese_fonts)}")
        print(f"可能中文字体: {len(chinese_related)}")
        
        if found_fonts:
            print(f"推荐使用: {found_fonts[0]}")
        elif chinese_related:
            print(f"可以尝试: {chinese_related[0]}")
        else:
            print("建议安装中文字体包")
        
    except Exception as e:
        print(f"字体检查失败: {e}")

if __name__ == "__main__":
    check_fonts()
