"""
配置管理模块

负责应用程序配置的加载、保存和管理，包括用户偏好设置、最后选择的目录等。
"""

import os
import json
from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class AppConfig:
    """应用程序配置数据类"""
    last_file_dir: str = ""
    last_folder_dir: str = ""
    last_model_dir: str = ""
    language: str = "zh"
    window_geometry: str = ""
    panel_states: Optional[Dict[str, bool]] = None
    
    def __post_init__(self):
        if self.panel_states is None:
            self.panel_states = {}
        
        # 设置默认目录为当前工作目录
        if not self.last_file_dir:
            self.last_file_dir = os.getcwd()
        if not self.last_folder_dir:
            self.last_folder_dir = os.getcwd()
        if not self.last_model_dir:
            self.last_model_dir = os.getcwd()


class ConfigManager:
    """
    配置管理器
    
    负责应用程序配置的加载、保存和管理。保持与原有配置逻辑完全一致。
    """
    
    def __init__(self, config_file_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_file_path is None:
            # 使用与原代码相同的配置文件路径
            config_file_path = os.path.join(os.path.dirname(__file__), '..', 'viewer_config.json')
        
        self.config_file = os.path.abspath(config_file_path)
        self.config = AppConfig()
        self.load_config()
    
    def load_config(self):
        """
        加载配置文件
        
        保持与原有load_config方法完全一致的逻辑
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.config.last_file_dir = config_data.get('last_file_dir', os.getcwd())
                    self.config.last_folder_dir = config_data.get('last_folder_dir', os.getcwd())
                    self.config.last_model_dir = config_data.get('last_model_dir', os.getcwd())
                    self.config.language = config_data.get('language', 'zh')
                    self.config.window_geometry = config_data.get('window_geometry', '')
                    self.config.panel_states = config_data.get('panel_states', {})
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            # 使用默认配置
            self.config = AppConfig()
    
    def save_config(self):
        """
        保存配置文件
        
        保持与原有save_config方法完全一致的逻辑
        """
        try:
            config_data = {
                'last_file_dir': self.config.last_file_dir,
                'last_folder_dir': self.config.last_folder_dir,
                'last_model_dir': self.config.last_model_dir,
                'language': self.config.language,
                'window_geometry': self.config.window_geometry,
                'panel_states': self.config.panel_states
            }
            
            # 确保配置文件目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置项键名
            default: 默认值
            
        Returns:
            配置项的值
        """
        return getattr(self.config, key, default)
    
    def set_setting(self, key: str, value: Any):
        """
        设置配置项
        
        Args:
            key: 配置项键名
            value: 配置项的值
        """
        if hasattr(self.config, key):
            setattr(self.config, key, value)
        else:
            # 对于不在AppConfig中定义的配置项，存储在panel_states中
            self.config.panel_states[key] = value
    
    def update_last_dir(self, dir_type: str, path: str):
        """
        更新最后选择的目录
        
        Args:
            dir_type: 目录类型 ('file', 'folder', 'model')
            path: 目录路径
        """
        if dir_type == 'file':
            self.config.last_file_dir = path
        elif dir_type == 'folder':
            self.config.last_folder_dir = path
        elif dir_type == 'model':
            self.config.last_model_dir = path
        
        # 自动保存配置
        self.save_config()
    
    def get_last_dir(self, dir_type: str) -> str:
        """
        获取最后选择的目录
        
        Args:
            dir_type: 目录类型 ('file', 'folder', 'model')
            
        Returns:
            最后选择的目录路径
        """
        if dir_type == 'file':
            return self.config.last_file_dir
        elif dir_type == 'folder':
            return self.config.last_folder_dir
        elif dir_type == 'model':
            return self.config.last_model_dir
        else:
            return self.config.last_file_dir
    
    def get_config_dict(self) -> Dict[str, Any]:
        """
        获取配置字典
        
        Returns:
            配置字典
        """
        return {
            'last_file_dir': self.config.last_file_dir,
            'last_folder_dir': self.config.last_folder_dir,
            'last_model_dir': self.config.last_model_dir,
            'language': self.config.language,
            'window_geometry': self.config.window_geometry,
            'panel_states': self.config.panel_states
        }
    
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """
        从字典更新配置
        
        Args:
            config_dict: 配置字典
        """
        for key, value in config_dict.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
