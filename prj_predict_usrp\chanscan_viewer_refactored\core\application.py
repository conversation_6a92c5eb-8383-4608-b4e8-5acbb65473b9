"""
主应用程序模块

整合所有模块管理器，实现完整的应用程序逻辑。
保持与原有代码完全一致的功能和行为。
"""

import os
import sys
import threading
import tkinter as tk
from pathlib import Path
from typing import Optional, Dict, Any, List

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入所有模块管理器
from core.config_manager import ConfigManager
from ui.main_window import MainWindow
from ui.styles import StyleManager
from ui.toolbar import Toolbar
from ui.panels import PanelManager
from data.file_manager import FileManager
from data.database_manager import DatabaseManager
from data.signal_processor import SignalProcessor
from visualization.chart_manager import ChartManager
from visualization.plot_renderer import PlotRenderer
from channel_scan.scanner import ChannelScanner
from channel_scan.segment_manager import SegmentManager
from utils.i18n import I18nManager
from utils.message_box import ScaledMessageBox


class ChannelScanViewerApp:
    """
    信道扫描分段查看工具主应用程序类
    
    整合所有模块管理器，实现完整的应用程序逻辑。
    """
    
    def __init__(self):
        """初始化应用程序"""
        # 初始化核心管理器
        self.config_manager = ConfigManager()
        self.i18n_manager = I18nManager(self.config_manager.get_setting('language', 'zh'))

        # 初始化主窗口（必须先创建，以便字体系统可以工作）
        texts = self.i18n_manager.get_all_texts()
        self.main_window = MainWindow(texts['title'], self.i18n_manager.current_language)
        self.main_window.set_on_closing_callback(self.on_closing)

        # 初始化样式管理器（在主窗口创建后）
        self.style_manager = StyleManager(self.i18n_manager.current_language)
        
        # 初始化数据管理器
        self.file_manager = FileManager()
        self.database_manager = DatabaseManager()
        self.signal_processor = SignalProcessor()
        
        # 创建主容器来正确管理布局层次
        main_container = tk.Frame(self.main_window.get_root())
        main_container.pack(fill=tk.BOTH, expand=True)

        # 初始化UI组件 - 工具栏在主容器顶部
        self.toolbar = Toolbar(main_container, self.style_manager, self.i18n_manager)

        # 面板管理器在工具栏下方
        self.panel_manager = PanelManager(main_container, self.style_manager, self.i18n_manager)

        # 初始化可视化组件
        # 创建图表容器框架（也在主容器中）
        chart_container_frame = tk.Frame(main_container)
        self.chart_manager = ChartManager(chart_container_frame, self.style_manager, self.i18n_manager)
        self.plot_renderer = PlotRenderer(self.signal_processor, self.i18n_manager)

        # 将图表容器添加到面板管理器
        self.panel_manager.add_right_panel(chart_container_frame)

        # 现在按正确顺序pack布局：先工具栏，后面板
        self.toolbar.pack_toolbar()  # 工具栏在顶部
        self.panel_manager.pack_main_layout()  # 面板在工具栏下方

        # 强制重新pack工具栏确保它在最前面（解决布局冲突）
        self.toolbar.pack_toolbar()

        # 强制更新布局，确保所有组件都已正确定位
        self.main_window.get_root().update_idletasks()
        
        # 初始化专业功能组件
        self.channel_scanner = ChannelScanner(self.i18n_manager)
        self.segment_manager = SegmentManager(self.i18n_manager)

        # 设置分段管理器回调（在UI初始化后设置）
        self._setup_segment_manager_callbacks_pending = True
        
        # 应用程序状态
        self.current_signal = None
        self.current_metadata = None
        self.current_file_path = None
        self.model_path = None
        
        # 设置回调函数
        self.setup_callbacks()
        
        # 初始化界面
        self.init_ui()
    
    def setup_callbacks(self):
        """设置所有回调函数"""
        # 工具栏回调
        toolbar_callbacks = {
            'select_file': self.select_file,
            'select_folder': self.select_folder,
            'select_and_load_model': self.select_and_load_model,
            'prev_file': self.prev_file,
            'next_file': self.next_file,
            'capture_data': self.capture_data,
            'run_channel_scan': self.run_channel_scan,
            'view_database': self.view_database,
            'toggle_view_mode': self.toggle_view_mode,
            'toggle_language': self.toggle_language
        }
        self.toolbar.set_callbacks(toolbar_callbacks)
        
        # 面板回调
        panel_callbacks = {
            'on_file_select': self.on_file_select,
            'clear_segment_results': self.clear_segment_results,
            'add_segments_to_db_action': self.add_segments_to_db_action,
            'skip_to_next_file_action': self.skip_to_next_file_action,
            'select_all_segments': self.select_all_segments,
            'deselect_all_segments': self.deselect_all_segments,
            'delete_selected_segments': self.delete_selected_segments,
            'on_segment_click': self.on_segment_click,
            'on_segment_double_click': self.on_segment_double_click,
            'on_segment_right_click': self.on_segment_right_click
        }
        self.panel_manager.set_callbacks(panel_callbacks)
        
        # 信道扫描器回调
        self.channel_scanner.set_callbacks(
            progress_callback=self.on_scan_progress,
            complete_callback=self.on_scan_complete,
            error_callback=self.on_scan_error
        )
        
        # 分段管理器回调
        self.segment_manager.set_callbacks(
            update_display_callback=self.update_segment_display,
            update_button_state_callback=self.update_button_states
        )
    
    def init_ui(self):
        """初始化用户界面"""
        # 配置matplotlib字体
        self.style_manager.setup_fonts()

        # 加载配置
        self.load_window_state()

        # 更新界面文本
        self.update_ui_texts()

        # 延迟初始化图表，避免影响工具栏布局
        self.main_window.get_root().after(100, self.delayed_chart_init)

    def delayed_chart_init(self):
        """延迟初始化图表，确保布局稳定后再初始化"""
        try:
            # 再次确保工具栏在最前面
            self.toolbar.pack_toolbar()

            # 创建状态栏
            self.create_status_bar()

            # 设置分段管理器回调
            if hasattr(self, '_setup_segment_manager_callbacks_pending'):
                self.setup_segment_manager_callbacks()
                delattr(self, '_setup_segment_manager_callbacks_pending')

            # 初始化图表
            self.chart_manager.init_plots()
        except Exception as e:
            print(f"延迟图表初始化失败: {e}")

    def setup_segment_manager_callbacks(self):
        """设置分段管理器回调"""
        try:
            # 设置更新显示回调
            def update_segment_display():
                """更新分段显示"""
                if hasattr(self, 'panels') and self.panels:
                    segments = self.segment_manager.export_segments_to_dict()
                    self.panels.update_segment_list(segments)

            # 设置更新按钮状态回调
            def update_button_states():
                """更新按钮状态"""
                self.update_button_states()

            # 设置回调
            self.segment_manager.set_callbacks(
                update_display_callback=update_segment_display,
                update_button_state_callback=update_button_states
            )
        except Exception as e:
            print(f"设置分段管理器回调失败: {e}")

    def create_status_bar(self):
        """创建状态栏（与原版本一致）"""
        import tkinter as tk
        from tkinter import ttk

        # 创建状态栏框架
        status_frame = tk.Frame(self.main_window.get_root(), relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态标签
        self.status_label = tk.Label(
            status_frame,
            text=self.i18n_manager.get_text('ready'),
            font=('Arial', 10),
            anchor='w'
        )
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2, fill=tk.X, expand=True)

        # 进度条（初始隐藏）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame,
            variable=self.progress_var,
            maximum=100,
            length=200,
            mode='determinate'
        )
        # 进度条初始不显示，需要时才pack

    def show_progress_bar(self, show=True):
        """显示或隐藏进度条"""
        if show:
            self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        else:
            self.progress_bar.pack_forget()

    def update_status(self, message):
        """更新状态栏消息"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def load_window_state(self):
        """加载窗口状态"""
        geometry = self.config_manager.get_setting('window_geometry', '')
        if geometry:
            try:
                # 解析几何字符串，检查是否合理
                if self._is_geometry_reasonable(geometry):
                    self.main_window.get_root().geometry(geometry)
                else:
                    print(f"忽略不合理的窗口几何配置: {geometry}")
            except Exception:
                pass  # 忽略无效的几何配置

    def _is_geometry_reasonable(self, geometry: str) -> bool:
        """检查窗口几何配置是否合理"""
        try:
            # 解析几何字符串 "widthxheight+x+y"
            parts = geometry.split('+')
            if len(parts) < 2:
                parts = geometry.split('-')
                if len(parts) < 2:
                    return False

            size_part = parts[0]
            if 'x' not in size_part:
                return False

            width_str, height_str = size_part.split('x')
            width = int(width_str)
            height = int(height_str)

            # 获取屏幕尺寸
            screen_width = self.main_window.get_root().winfo_screenwidth()
            screen_height = self.main_window.get_root().winfo_screenheight()

            # 检查窗口尺寸是否合理（不超过屏幕尺寸的90%，不小于最小尺寸）
            max_width = int(screen_width * 0.9)
            max_height = int(screen_height * 0.9)
            min_width = 1000
            min_height = 700

            if width > max_width or height > max_height:
                return False
            if width < min_width or height < min_height:
                return False

            return True

        except (ValueError, IndexError):
            return False
    
    def save_window_state(self):
        """保存窗口状态"""
        try:
            geometry = self.main_window.get_root().geometry()
            self.config_manager.set_setting('window_geometry', geometry)
            self.config_manager.save_config()
        except Exception:
            pass  # 忽略保存失败
    
    def update_ui_texts(self):
        """更新界面文本"""
        texts = self.i18n_manager.get_all_texts()
        self.main_window.set_title(texts['title'])
        self.toolbar.update_texts()
        self.panel_manager.update_texts()
        self.chart_manager.update_language()
    
    # 文件操作方法
    def select_file(self):
        """选择文件"""
        from tkinter import filedialog
        
        last_dir = self.config_manager.get_last_dir('file')
        file_path = filedialog.askopenfilename(
            title=self.i18n_manager.get_text('select_file'),
            initialdir=last_dir,
            filetypes=self.file_manager.FILE_TYPES
        )
        
        if file_path:
            self.config_manager.update_last_dir('file', os.path.dirname(file_path))
            self.load_file(file_path)
    
    def select_folder(self):
        """选择文件夹"""
        from tkinter import filedialog
        
        last_dir = self.config_manager.get_last_dir('folder')
        folder_path = filedialog.askdirectory(
            title=self.i18n_manager.get_text('select_folder'),
            initialdir=last_dir
        )
        
        if folder_path:
            self.config_manager.update_last_dir('folder', folder_path)
            self.load_folder(folder_path)
    
    def select_and_load_model(self):
        """选择并加载模型"""
        from tkinter import filedialog

        last_dir = self.config_manager.get_last_dir('model')
        model_path = filedialog.askopenfilename(
            title=self.i18n_manager.get_text('select_model'),
            initialdir=last_dir,
            filetypes=self.file_manager.MODEL_FILE_TYPES
        )

        if model_path:
            self.config_manager.update_last_dir('model', os.path.dirname(model_path))
            self.model_path = model_path

            # 显示模型选择成功的消息
            model_name = os.path.basename(model_path)
            success_msg = f"模型选择成功: {model_name}" if self.i18n_manager.current_language == 'zh' else f"Model selected: {model_name}"

            ScaledMessageBox.showinfo(
                self.i18n_manager.get_text('success') if 'success' in self.i18n_manager.texts_zh else "成功",
                success_msg,
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
    
    def load_file(self, file_path: str):
        """加载文件"""
        try:
            success, signal_data, metadata, error_msg = self.file_manager.load_file(file_path)
            
            if success:
                self.current_signal = signal_data
                self.current_metadata = metadata
                self.current_file_path = file_path
                
                # 更新文件信息显示
                self.update_file_info_display()
                
                # 显示信号图表
                self.display_signal()
                
                # 清空分段结果
                self.segment_manager.clear_segments()
                
            else:
                ScaledMessageBox.showerror(
                    self.i18n_manager.get_text('error'),
                    f"{self.i18n_manager.get_text('load_file_failed')}: {error_msg}",
                    parent=self.main_window.get_root(),
                    language=self.i18n_manager.current_language
                )
                
        except Exception as e:
            ScaledMessageBox.showerror(
                self.i18n_manager.get_text('error'),
                f"{self.i18n_manager.get_text('load_file_failed')}: {str(e)}",
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
    
    def load_folder(self, folder_path: str):
        """加载文件夹"""
        files = self.file_manager.find_files_in_folder(folder_path)
        
        if files:
            self.file_manager.set_current_files(files, 0)
            # 更新文件列表显示
            self.update_file_list_display(files)
            # 加载第一个文件
            if files:
                self.load_file(files[0])
        else:
            ScaledMessageBox.showwarning(
                self.i18n_manager.get_text('warning'),
                self.i18n_manager.get_text('no_files_found'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
    
    def prev_file(self):
        """上一个文件"""
        if self.file_manager.has_previous_file():
            old_index = self.file_manager.current_index
            prev_file_path = self.file_manager.previous_file()
            if prev_file_path:
                # 更新文件列表标记
                self.update_file_list_markers(old_index, self.file_manager.current_index)
                self.load_file(prev_file_path)

    def next_file(self):
        """下一个文件"""
        if self.file_manager.has_next_file():
            old_index = self.file_manager.current_index
            next_file_path = self.file_manager.next_file()
            if next_file_path:
                # 更新文件列表标记
                self.update_file_list_markers(old_index, self.file_manager.current_index)
                self.load_file(next_file_path)
    
    # 界面更新方法
    def update_file_info_display(self):
        """更新文件信息显示"""
        if self.current_file_path and hasattr(self.panel_manager, 'info_text'):
            file_info = self.file_manager.get_file_info(self.current_file_path)
            
            info_text = f"文件路径: {file_info.path}\n"
            info_text += f"文件大小: {file_info.get_size_mb():.2f} MB\n"
            info_text += f"文件格式: {file_info.format}\n"
            
            if file_info.sample_rate:
                info_text += f"采样率: {file_info.sample_rate/1e6:.2f} MHz\n"
            if file_info.center_frequency:
                info_text += f"中心频率: {file_info.center_frequency/1e6:.2f} MHz\n"
            if file_info.signal_length:
                info_text += f"信号长度: {file_info.signal_length} 个采样点\n"
            if file_info.duration:
                info_text += f"持续时间: {file_info.duration*1000:.2f} ms\n"
            
            # 更新文本显示
            info_widget = self.panel_manager.info_text
            info_widget.config(state='normal')
            info_widget.delete(1.0, 'end')
            info_widget.insert(1.0, info_text)
            info_widget.config(state='disabled')
    
    def update_file_list_display(self, files: List[str]):
        """更新文件列表显示"""
        if hasattr(self.panel_manager, 'file_listbox'):
            listbox = self.panel_manager.file_listbox
            listbox.delete(0, 'end')

            # 更新文件管理器的文件列表
            self.file_manager.set_current_files(files, 0)

            for i, file_path in enumerate(files):
                filename = os.path.basename(file_path)
                # 添加选中标记（与原版本一致）
                marker = "● " if i == self.file_manager.current_index else "  "
                listbox.insert('end', f"{marker}{filename}")

    def update_file_list_markers(self, old_index: int, new_index: int):
        """只更新文件列表的标记，避免重新构建整个列表"""
        if hasattr(self.panel_manager, 'file_listbox'):
            listbox = self.panel_manager.file_listbox
            current_files = self.file_manager.current_files

            if not current_files:
                return

            # 更新旧的选中项，移除标记
            if 0 <= old_index < len(current_files):
                old_filename = os.path.basename(current_files[old_index])
                listbox.delete(old_index)
                listbox.insert(old_index, f"  {old_filename}")

            # 更新新的选中项，添加标记
            if 0 <= new_index < len(current_files):
                new_filename = os.path.basename(current_files[new_index])
                listbox.delete(new_index)
                listbox.insert(new_index, f"● {new_filename}")
                # 确保新选中的项可见
                listbox.see(new_index)
    
    def display_signal(self):
        """显示信号图表"""
        if self.current_signal is not None and self.current_metadata:
            fs = self.current_metadata.get('fs', 1.0)
            fc = self.current_metadata.get('fc', 0.0)
            
            if self.chart_manager.is_3d_mode:
                self.plot_renderer.display_signal_3d(
                    self.chart_manager.get_figure(),
                    self.current_signal, fs, fc
                )
            else:
                self.plot_renderer.display_signal_2d(
                    self.chart_manager.get_figure(),
                    self.current_signal, fs, fc, self.current_metadata
                )
            
            self.chart_manager.get_canvas().draw()
    
    def on_closing(self):
        """应用程序关闭处理"""
        # 保存窗口状态
        self.save_window_state()
        
        # 清理资源
        self.signal_processor.cleanup()
        
        # 退出应用程序
        self.main_window.quit()
        self.main_window.destroy()
    
    # 信道扫描相关方法
    def run_channel_scan(self):
        """运行信道扫描"""
        if not self.channel_scanner.is_available():
            ScaledMessageBox.showwarning(
                self.i18n_manager.get_text('warning'),
                self.i18n_manager.get_text('scan_not_available'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
            return

        if self.current_signal is None:
            ScaledMessageBox.showwarning(
                self.i18n_manager.get_text('warning'),
                self.i18n_manager.get_text('no_data_loaded'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
            return

        # 启动扫描
        self.channel_scanner.run_scan(
            self.current_signal,
            self.current_metadata,
            self.model_path
        )

    def on_scan_progress(self, message: str):
        """扫描进度回调"""
        # 更新状态标签（如果有的话）
        print(f"扫描进度: {message}")

    def on_scan_complete(self, scan_results):
        """扫描完成回调"""
        # 转换扫描结果为分段数据
        segments_data = []
        for result in scan_results:
            segment_data = {
                'start_sample': result.start_sample,
                'end_sample': result.end_sample,
                'center_freq': result.center_freq,
                'bandwidth': result.bandwidth,
                'confidence': result.confidence,
                'class_name': result.class_name,
                'snr': result.snr,
                'duration': result.duration
            }
            segments_data.append(segment_data)

        # 添加到分段管理器
        self.segment_manager.add_segments(segments_data)

        # 在图表上添加分段标记
        self.add_segment_markers_to_plots()

        # 更新状态
        num_segments = len(scan_results)
        print(f"扫描完成: 检测到 {num_segments} 个信号段")

    def on_scan_error(self, error_msg: str):
        """扫描错误回调"""
        ScaledMessageBox.showerror(
            self.i18n_manager.get_text('error'),
            f"{self.i18n_manager.get_text('scan_failed')}: {error_msg}",
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        )

    # 分段管理相关方法
    def clear_segment_results(self):
        """清空分段结果"""
        self.segment_manager.clear_segments()
        # 重新显示信号（清除分段标记）
        self.display_signal()

    def select_all_segments(self):
        """选中所有分段"""
        self.segment_manager.select_all_segments()

    def deselect_all_segments(self):
        """取消选中所有分段"""
        self.segment_manager.deselect_all_segments()

    def delete_selected_segments(self):
        """删除选中的分段"""
        selected_count = self.segment_manager.get_selected_count()
        if selected_count == 0:
            ScaledMessageBox.showinfo(
                self.i18n_manager.get_text('warning'),
                self.i18n_manager.get_text('no_segments_selected'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
            return

        # 确认删除
        if ScaledMessageBox.askyesno(
            self.i18n_manager.get_text('warning'),
            self.i18n_manager.get_text('confirm_delete_segments'),
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        ):
            deleted_count = self.segment_manager.delete_selected_segments()
            # 重新显示信号
            self.display_signal()
            self.add_segment_markers_to_plots()

    def add_segments_to_db_action(self):
        """将选中的分段添加到数据库"""
        selected_segments = self.segment_manager.get_selected_segments()
        if not selected_segments:
            ScaledMessageBox.showinfo(
                self.i18n_manager.get_text('warning'),
                self.i18n_manager.get_text('no_segments_selected'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
            return

        # 检查是否有模型
        if not self.model_path:
            if ScaledMessageBox.askyesno(
                self.i18n_manager.get_text('no_model_db_prompt_title'),
                self.i18n_manager.get_text('no_model_db_add_prompt_message'),
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            ):
                self.select_and_load_model()
                if not self.model_path:
                    return
            else:
                return

        # 确认添加
        if ScaledMessageBox.askyesno(
            self.i18n_manager.get_text('warning'),
            self.i18n_manager.get_text('add_segments_to_db_confirm'),
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        ):
            # 这里应该调用数据库管理器的添加方法
            # 由于原代码比较复杂，这里提供接口
            print(f"添加 {len(selected_segments)} 个分段到数据库")

    def skip_to_next_file_action(self):
        """跳过到下一个文件"""
        self.next_file()

    # 事件处理方法
    def on_file_select(self, event):
        """文件选择事件"""
        try:
            if hasattr(self.panel_manager, 'file_listbox'):
                selection = self.panel_manager.file_listbox.curselection()
                if selection:
                    index = selection[0]
                    current_files = self.file_manager.current_files
                    if 0 <= index < len(current_files):
                        # 调试信息
                        print(f"文件选择: 索引={index}, 文件={current_files[index]}")

                        # 保存旧的索引用于更新标记
                        old_index = self.file_manager.current_index

                        # 更新文件管理器的当前索引
                        self.file_manager.current_index = index

                        # 更新文件列表标记
                        self.update_file_list_markers(old_index, index)

                        # 加载选中的文件
                        print(f"开始加载文件: {current_files[index]}")
                        self.load_file(current_files[index])

                        # 更新状态栏
                        if hasattr(self, 'status_label'):
                            filename = os.path.basename(current_files[index])
                            self.update_status(f"已加载文件: {filename}")
        except Exception as e:
            print(f"文件选择处理失败: {e}")
            import traceback
            traceback.print_exc()

    def on_segment_click(self, event):
        """分段点击事件"""
        # 这里可以添加分段点击处理逻辑
        pass

    def on_segment_double_click(self, event):
        """分段双击事件"""
        # 这里可以添加分段双击处理逻辑（如显示详细信息）
        pass

    def on_segment_right_click(self, event):
        """分段右键点击事件"""
        # 这里可以添加右键菜单处理逻辑
        pass

    # 其他功能方法
    def capture_data(self):
        """数据捕获功能

        实现USRP数据采集功能，允许用户选择保存路径和设置中心频率
        """
        try:
            # 导入必要的模块
            from existing_modules.data_capture_controller import DataCaptureController
            from existing_modules.progress_dialog import ProgressDialog
            from utils.message_box import ScaledMessageBox
            import threading

            # 显示USRP设备检查进度
            check_title = self.i18n_manager.get_text('device_check')
            check_msg = self.i18n_manager.get_text('checking_usrp')

            # 创建检查进度对话框
            check_dialog = ProgressDialog(
                parent=self.main_window.get_root(),
                title=check_title,
                message=check_msg,
                language=self.i18n_manager.current_language,
                width=450,
                height=120
            )

            # 启动自动脉冲模式
            check_dialog.start_auto_pulse()

            # 在后台线程中检查USRP设备
            def check_usrp_thread():
                try:
                    is_available, error_msg = DataCaptureController.check_usrp_available()

                    # 关闭检查对话框
                    self.main_window.get_root().after(0, check_dialog.close)

                    if not is_available:
                        self.main_window.get_root().after(100, lambda: ScaledMessageBox.showerror(
                            self.i18n_manager.get_text('error'),
                            error_msg,
                            parent=self.main_window.get_root(),
                            language=self.i18n_manager.current_language
                        ))
                        return

                    # 继续执行数据捕获流程
                    self.main_window.get_root().after(100, self._continue_capture_data)

                except Exception as e:
                    self.main_window.get_root().after(0, check_dialog.close)
                    self.main_window.get_root().after(100, lambda: ScaledMessageBox.showerror(
                        self.i18n_manager.get_text('error'),
                        f"设备检查失败: {str(e)}" if self.i18n_manager.current_language == 'zh' else f"Device check failed: {str(e)}",
                        parent=self.main_window.get_root(),
                        language=self.i18n_manager.current_language
                    ))

            # 启动检查线程
            threading.Thread(target=check_usrp_thread, daemon=True).start()

        except ImportError as e:
            ScaledMessageBox.showerror(
                self.i18n_manager.get_text('error'),
                f"数据捕获功能不可用: {e}",
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )
        except Exception as e:
            ScaledMessageBox.showerror(
                self.i18n_manager.get_text('error'),
                f"启动数据捕获失败: {e}",
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )

    def _continue_capture_data(self):
        """继续执行数据捕获流程"""
        from utils.message_box import ScaledMessageBox
        from tkinter import filedialog
        import os

        # 显示用户提示，说明选择文件夹的用途
        info_title = self.i18n_manager.get_text('select_data_folder')
        info_msg = ("请选择一个文件夹来存储采集的信号数据。\n\n"
                   "注意：\n"
                   "• 如果文件夹已存在且不为空，将会被清空\n"
                   "• 采集的数据文件将以时间戳命名\n"
                   "• 建议选择有足够空间的文件夹") if self.i18n_manager.current_language == 'zh' else (
                   "Please select a folder to store the captured signal data.\n\n"
                   "Note:\n"
                   "• If the folder exists and is not empty, it will be cleared\n"
                   "• Captured data files will be named with timestamps\n"
                   "• It is recommended to select a folder with sufficient space")

        # 显示提示信息
        if not ScaledMessageBox.askokcancel(
            info_title,
            info_msg,
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        ):
            return  # 用户取消操作

        # 选择保存文件夹
        folder_title = self.i18n_manager.get_text('select_data_folder')
        folder_path = filedialog.askdirectory(
            title=folder_title,
            initialdir=self.config_manager.get_setting('last_folder_dir', os.path.expanduser('~'))
        )

        # 如果用户取消选择，则退出函数
        if not folder_path:
            return

        # 更新最后选择的文件夹路径
        last_folder_dir = os.path.dirname(folder_path) if os.path.dirname(folder_path) else folder_path
        self.config_manager.set_setting('last_folder_dir', last_folder_dir)
        self.config_manager.save_config()

        # 显示文件夹名称与信号类名一致的提示
        folder_name = os.path.basename(folder_path)
        folder_info_title = self.i18n_manager.get_text('folder_selection_notice')
        folder_info_msg = (f"您选择的文件夹名称为: {folder_name}\n\n"
                          f"请确保文件夹名称与所采集信号的类名一致，\n"
                          f"这样有助于后续的数据管理和分析。") if self.i18n_manager.current_language == 'zh' else (
                          f"Selected folder name: {folder_name}\n\n"
                          f"Please ensure the folder name matches the signal class name\n"
                          f"for better data management and analysis.")

        ScaledMessageBox.showinfo(
            folder_info_title,
            folder_info_msg,
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        )

        # 保存文件夹路径用于后续自动加载
        self._last_capture_folder = folder_path

        # 继续数据捕获流程
        self._start_data_capture(folder_path)

    def _start_data_capture(self, folder_path):
        """开始数据采集"""
        from existing_modules.frequency_dialog import FrequencyInputDialog
        from existing_modules.data_capture_controller import DataCaptureController
        from existing_modules.progress_dialog import ProgressDialog
        from utils.message_box import ScaledMessageBox
        import threading
        import queue

        # 使用FrequencyInputDialog创建频率输入对话框
        freq_input = FrequencyInputDialog.create_dialog(
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language,
            initial_value=2446.0
        )

        # 验证频率输入
        is_valid, freq_hz = FrequencyInputDialog.validate_frequency(
            freq_mhz=freq_input,
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        )

        # 如果输入无效，则退出函数
        if not is_valid:
            return

        # 记录日志
        print(f"用户输入的中心频率: {freq_input} MHz ({freq_hz} Hz)")

        # 创建进度提示对话框
        progress_title = self.i18n_manager.get_text('data_capture_progress')
        progress_msg = self.i18n_manager.get_text('initializing_capture')

        # 使用ProgressDialog类创建进度对话框
        progress_dialog = ProgressDialog(
            parent=self.main_window.get_root(),
            title=progress_title,
            message=progress_msg,
            language=self.i18n_manager.current_language
        )

        # 在后台线程中执行数据采集
        def capture_thread():
            try:
                # 创建一个队列用于传递进度信息
                progress_queue = queue.Queue()

                # 定义回调函数，用于更新进度
                def progress_callback(current_file, total_files, status_message):
                    # 将进度信息放入队列
                    progress_queue.put({
                        'current_file': current_file,
                        'total_files': total_files,
                        'status_message': status_message
                    })

                # 启动进度更新检查器
                def check_progress():
                    try:
                        # 非阻塞方式获取进度信息
                        while not progress_queue.empty():
                            progress_info = progress_queue.get_nowait()

                            # 计算进度百分比
                            current_file = progress_info['current_file']
                            total_files = progress_info['total_files']
                            if total_files > 0:
                                progress_percent = min(90, (current_file / total_files) * 90)
                            else:
                                progress_percent = 0

                            # 更新进度对话框
                            progress_dialog.update_progress(
                                value=progress_percent,
                                message=progress_info['status_message'],
                                file_count=current_file
                            )
                    except Exception as e:
                        print(f"进度更新错误: {e}")

                    # 如果对话框仍然存在且未被取消，继续检查进度
                    if not progress_dialog.is_cancelled() and progress_dialog.dialog.winfo_exists():
                        self.main_window.get_root().after(100, check_progress)

                # 启动进度检查
                self.main_window.get_root().after(100, check_progress)

                # 调用数据采集控制函数
                success, result_msg, file_count = DataCaptureController.capture_data(
                    folder_path=folder_path,
                    freq_hz=freq_hz,
                    progress_callback=progress_callback
                )

                # 处理采集结果
                self._handle_capture_result(progress_dialog, success, result_msg, file_count)

            except Exception as e:
                # 处理异常
                self.main_window.get_root().after(0, lambda: self._handle_capture_error(progress_dialog, e))

        # 启动采集线程
        threading.Thread(target=capture_thread, daemon=True).start()

    def _handle_capture_result(self, progress_dialog, success, result_msg, file_count):
        """处理数据采集结果"""
        from utils.message_box import ScaledMessageBox

        # 如果没有被取消，则关闭进度对话框并处理结果
        if not progress_dialog.is_cancelled():
            # 更新进度到100%
            progress_dialog.update_progress(
                value=100,
                message=self.i18n_manager.get_text('capture_completed')
            )

            # 短暂延迟后关闭对话框
            self.main_window.get_root().after(500, progress_dialog.close)

            if success:
                # 显示成功消息
                success_title = self.i18n_manager.get_text('capture_success')
                ScaledMessageBox.showinfo(
                    success_title,
                    result_msg,
                    parent=self.main_window.get_root(),
                    language=self.i18n_manager.current_language
                )

                # 数据捕获成功后自动加载文件夹
                if hasattr(self, '_last_capture_folder') and self._last_capture_folder:
                    self.main_window.get_root().after(1000, lambda: self.load_folder(self._last_capture_folder))
            else:
                # 显示错误消息
                error_title = self.i18n_manager.get_text('capture_error')
                ScaledMessageBox.showerror(
                    error_title,
                    result_msg,
                    parent=self.main_window.get_root(),
                    language=self.i18n_manager.current_language
                )

    def _handle_capture_error(self, progress_dialog, error):
        """处理数据采集异常"""
        from utils.message_box import ScaledMessageBox

        # 关闭进度对话框
        if progress_dialog and hasattr(progress_dialog, 'close'):
            progress_dialog.close()

        # 显示错误消息
        error_title = self.i18n_manager.get_text('capture_error')
        error_msg = f"数据采集失败: {str(error)}" if self.i18n_manager.current_language == 'zh' else f"Data capture failed: {str(error)}"

        ScaledMessageBox.showerror(
            error_title,
            error_msg,
            parent=self.main_window.get_root(),
            language=self.i18n_manager.current_language
        )

    def view_database(self):
        """查看数据库"""
        try:
            # 使用数据库管理器打开数据库查看窗口
            self.database_manager.view_database_structure(
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )

        except Exception as e:
            ScaledMessageBox.showerror(
                self.i18n_manager.get_text('error'),
                f"打开数据库查看失败: {e}",
                parent=self.main_window.get_root(),
                language=self.i18n_manager.current_language
            )

    def toggle_view_mode(self):
        """切换视图模式"""
        new_mode_text = self.chart_manager.toggle_view_mode()
        self.toolbar.update_view_button_text(new_mode_text)

        # 重新显示信号
        if self.current_signal is not None:
            self.display_signal()

    def toggle_language(self):
        """切换语言"""
        new_language = self.i18n_manager.toggle_language()
        self.config_manager.set_setting('language', new_language)
        self.config_manager.save_config()

        # 更新样式管理器语言
        self.style_manager.update_language(new_language)

        # 更新界面文本
        self.update_ui_texts()

    def update_segment_display(self):
        """更新分段显示"""
        # 这里应该更新TreeView显示
        pass

    def update_button_states(self):
        """更新按钮状态"""
        # 这里应该更新按钮的启用/禁用状态
        pass

    def add_segment_markers_to_plots(self):
        """在图表上添加分段标记"""
        if self.current_signal is not None and self.current_metadata:
            segments = self.segment_manager.export_segments_to_dict()
            if segments:
                fs = self.current_metadata.get('fs', 1.0)
                fc = self.current_metadata.get('fc', 0.0)

                self.plot_renderer.add_segment_markers_to_plots(
                    self.chart_manager.get_axes(),
                    segments, fs, fc
                )
                self.chart_manager.get_canvas().draw()

    def run(self):
        """运行应用程序"""
        self.main_window.mainloop()
