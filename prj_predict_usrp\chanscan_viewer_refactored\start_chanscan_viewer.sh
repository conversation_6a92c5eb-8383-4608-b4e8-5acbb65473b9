#!/bin/bash

# 脚本说明: 用于启动信道扫描分段查看工具的自动化脚本

echo "========================================="
echo "    信道扫描分段查看工具"
echo "========================================="
echo ""

# 检查Conda是否已初始化
if [ -f "$HOME/miniconda3/bin/conda" ]; then
    CONDA_BASE=$(conda info --base 2>/dev/null)
elif [ -f "$HOME/anaconda3/bin/conda" ]; then
    CONDA_BASE=$(conda info --base 2>/dev/null)
else
    echo "警告: 找不到Conda安装，将使用系统Python"
    CONDA_BASE=""
fi

# 如果找到conda，则激活环境
if [ -n "$CONDA_BASE" ]; then
    echo "Conda base 路径: $CONDA_BASE"
    source "$CONDA_BASE/etc/profile.d/conda.sh"

    # 激活目标Conda环境（根据需要修改环境名）
    ENV_NAME="env_cnr"
    echo "正在激活Conda环境: $ENV_NAME"
    conda activate $ENV_NAME

    # 检查环境是否激活成功
    if [ "$CONDA_DEFAULT_ENV" != "$ENV_NAME" ]; then
        echo "警告: 激活Conda环境 '$ENV_NAME' 失败，使用系统Python"
    else
        echo "环境激活成功，当前环境: $CONDA_DEFAULT_ENV"
    fi
else
    echo "使用系统Python环境"
fi

echo ""

# 获取脚本所在的目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
echo "脚本目录: $SCRIPT_DIR"

# 切换到脚本所在目录
cd "$SCRIPT_DIR"

# 检查基础依赖
echo "正在检查依赖模块..."
python3 -c "import tkinter, numpy, matplotlib, scipy; print('基础依赖检查通过')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 某些基础依赖可能缺失"
    echo "请确保已安装: numpy, matplotlib, scipy, tkinter"
    echo ""
fi

# 检查中文字体
echo "正在检查中文字体支持..."
python3 -c "
import platform

if platform.system() == 'Linux':
    try:
        import tkinter as tk
        import tkinter.font as tkFont

        # 创建临时根窗口以避免字体检查错误
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        available_fonts = list(tkFont.families())

        # 基于实际安装情况的字体候选列表
        chinese_fonts = [
            # 优先使用已确认安装的字体
            'WenQuanYi Zen Hei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC',
            'Droid Sans Fallback', 'Liberation Sans', 'DejaVu Sans',
            # 其他可能的字体
            'Source Han Sans SC', 'AR PL UMing CN', 'Noto Sans CJK',
            'Noto Sans', 'Source Han Sans', 'Droid Sans', 'SimHei'
        ]

        found_fonts = [font for font in chinese_fonts if font in available_fonts]

        # 如果没有找到预期字体，搜索可能的中文字体
        if not found_fonts:
            # 查找包含中文相关关键词的字体
            keywords = ['wenquanyi', 'noto', 'cjk', 'droid', 'liberation', 'dejavu', 'zen', 'hei']
            chinese_related = []
            for font in available_fonts:
                font_lower = font.lower()
                for keyword in keywords:
                    if keyword in font_lower:
                        chinese_related.append(font)
                        break

            if chinese_related:
                found_fonts = chinese_related[:1]  # 使用第一个找到的
                print(f'✓ 找到可用字体: {found_fonts[0]} (通过关键词搜索)')
            else:
                # 最后尝试使用fc-list验证
                import subprocess
                try:
                    result = subprocess.run(['fc-list', ':lang=zh'], capture_output=True, text=True)
                    if result.returncode == 0 and result.stdout.strip():
                        print('✓ 系统支持中文字体 (通过fc-list验证)')
                        found_fonts = ['DejaVu Sans']  # 使用默认字体
                    else:
                        found_fonts = []
                except:
                    found_fonts = []

        if found_fonts:
            print(f'✓ 字体检查通过: {found_fonts[0]}')
        else:
            print('⚠ 警告: 未找到合适的中文字体，将使用系统默认字体')
            print('建议安装中文字体包以获得更好的显示效果:')
            print('  Ubuntu/Debian: sudo apt install fonts-noto-cjk fonts-wqy-zenhei')
            print('  CentOS/RHEL: sudo yum install google-noto-sans-cjk-fonts wqy-zenhei-fonts')

        # 销毁临时窗口
        root.destroy()

    except Exception as e:
        print(f'字体检查失败: {e}')
        print('将使用系统默认字体')
else:
    print('非Linux系统，跳过中文字体检查')
" 2>/dev/null

# 检查主程序文件是否存在
if [ ! -f "chanscan_viewer.py" ]; then
    echo "错误: 找不到 chanscan_viewer.py 文件"
    echo "请确保在正确的目录下运行此脚本"
    exit 1
fi

echo "启动信道扫描分段查看工具..."
echo ""

# 运行Python程序
python3 chanscan_viewer.py

# 检查程序退出状态
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo ""
    echo "程序运行出现错误（退出代码: $EXIT_CODE）"
    echo "请检查以下问题："
    echo "1. Python 环境是否正确安装"
    echo "2. 必需的库是否已安装：numpy, scipy, matplotlib, tkinter"
    echo "3. 项目模块路径是否正确（chanlib, usrlib等）"
    echo "4. chanscan_viewer.py 文件是否存在"
    echo ""
    echo "如需查看详细错误信息，请直接运行:"
    echo "python3 chanscan_viewer.py"
    echo ""
else
    echo ""
    echo "程序已正常退出。"
fi

# 如果使用了conda环境，停用它
if [ -n "$CONDA_DEFAULT_ENV" ] && [ "$CONDA_DEFAULT_ENV" != "base" ]; then
    echo "正在停用Conda环境..."
    conda deactivate
fi

echo ""
echo "脚本执行完成。"