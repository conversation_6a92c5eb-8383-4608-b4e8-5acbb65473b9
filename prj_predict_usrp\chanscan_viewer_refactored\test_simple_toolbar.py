#!/usr/bin/env python3
"""
简单工具栏测试

测试最基本的工具栏显示
"""

import tkinter as tk

def test_simple_toolbar():
    """测试简单工具栏"""
    root = tk.Tk()
    root.title("简单工具栏测试")
    root.geometry("800x600")
    
    # 创建一个明显的工具栏
    toolbar = tk.Frame(root, bg='red', height=50)
    toolbar.pack(fill=tk.X, side=tk.TOP)
    toolbar.pack_propagate(False)
    
    # 添加一些按钮
    btn1 = tk.Button(toolbar, text="按钮1")
    btn1.pack(side=tk.LEFT, padx=5, pady=5)
    
    btn2 = tk.But<PERSON>(toolbar, text="按钮2")
    btn2.pack(side=tk.LEFT, padx=5, pady=5)
    
    btn3 = tk.Button(toolbar, text="按钮3")
    btn3.pack(side=tk.LEFT, padx=5, pady=5)
    
    # 添加一个标签来验证布局
    label = tk.Label(root, text="这是主内容区域", bg="lightgray")
    label.pack(fill=tk.BOTH, expand=True)
    
    print("简单工具栏测试启动...")
    root.mainloop()

if __name__ == "__main__":
    test_simple_toolbar()
