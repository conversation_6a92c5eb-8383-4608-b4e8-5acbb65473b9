#!/usr/bin/env python3
"""
测试get_text方法修复是否生效
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_i18n_manager():
    """测试I18nManager的get_text方法"""
    print("测试I18nManager...")
    
    try:
        from utils.i18n import I18nManager
        
        # 创建I18nManager实例
        i18n = I18nManager()
        
        # 测试新添加的文本键
        test_keys = [
            'device_check',
            'checking_usrp', 
            'select_data_folder',
            'folder_selection_notice',
            'data_capture_progress',
            'initializing_capture',
            'capture_completed',
            'capture_success',
            'capture_error'
        ]
        
        print("测试中文文本键:")
        i18n.set_language('zh')
        for key in test_keys:
            text = i18n.get_text(key)
            print(f"  {key}: {text}")
        
        print("\n测试英文文本键:")
        i18n.set_language('en')
        for key in test_keys:
            text = i18n.get_text(key)
            print(f"  {key}: {text}")
        
        print("\n✅ I18nManager测试通过")
        return True
        
    except Exception as e:
        print(f"❌ I18nManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_import():
    """测试Application类的导入"""
    print("\n测试Application类导入...")
    
    try:
        from core.application import ChannelScanViewerApp
        print("✅ Application类导入成功")
        return True
        
    except Exception as e:
        print(f"❌ Application类导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_text_calls():
    """测试修复后的get_text调用"""
    print("\n测试修复后的get_text调用...")
    
    try:
        from core.application import ChannelScanViewerApp
        from utils.i18n import I18nManager
        
        # 创建I18nManager实例
        i18n = I18nManager()
        
        # 模拟一些get_text调用
        test_calls = [
            ('device_check',),
            ('checking_usrp',),
            ('select_data_folder',),
            ('capture_error',)
        ]
        
        for call_args in test_calls:
            try:
                result = i18n.get_text(*call_args)
                print(f"  get_text{call_args}: {result}")
            except Exception as e:
                print(f"  ❌ get_text{call_args} 失败: {e}")
                return False
        
        print("✅ get_text调用测试通过")
        return True
        
    except Exception as e:
        print(f"❌ get_text调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试get_text方法修复...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_i18n_manager,
        test_application_import,
        test_get_text_calls
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！get_text方法修复成功")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
