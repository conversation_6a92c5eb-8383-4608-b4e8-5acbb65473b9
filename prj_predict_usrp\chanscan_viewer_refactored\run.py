#!/usr/bin/env python3
"""
信道扫描分段查看工具 - 快速启动脚本

这是一个简化的启动脚本，用于快速启动重构版本的chanscan_viewer。

使用方法：
    python run.py
    或
    ./run.py  (在Unix/Linux系统上)
"""

import sys
import os
from pathlib import Path

# 确保我们在正确的目录中
script_dir = Path(__file__).parent
os.chdir(script_dir)

# 运行主程序
if __name__ == "__main__":
    try:
        # 导入并运行主程序
        from main import main
        exit_code = main()
        sys.exit(exit_code)
    except ImportError:
        # 如果导入失败，直接执行main.py
        import subprocess
        result = subprocess.run([sys.executable, "main.py"], cwd=script_dir)
        sys.exit(result.returncode)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
