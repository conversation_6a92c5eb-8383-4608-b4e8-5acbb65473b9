"""
样式管理模块

负责字体、颜色和样式的管理，确保界面的一致性。
严格保持与原有代码相同的字体大小和样式设置。
"""

import platform
import tkinter as tk
import tkinter.font as tkFont
from typing import Dict, Any, List, Optional


class StyleManager:
    """
    样式管理器
    
    负责管理应用程序的字体、颜色和样式，保持与原有代码完全一致的样式设置。
    特别注意保持1.5倍字体放大和所有原有的字体配置。
    """
    
    def __init__(self, language: str = 'zh'):
        """
        初始化样式管理器
        
        Args:
            language: 当前语言 ('zh' 或 'en')
        """
        self.current_language = language
        self.system = platform.system()
        self.font_config = {}
        self.fonts = {}
        
        # 初始化字体配置
        self.setup_fonts()
    
    def setup_fonts(self):
        """
        设置字体（1.5倍放大，与data_viewer一致）
        
        严格保持与原有代码相同的字体配置逻辑
        """
        print(f"检测到操作系统: {self.system}")
        
        # 检测操作系统并选择合适的字体
        if self.system == 'Windows':
            # Windows 系统使用微软雅黑和 Segoe UI
            chinese_font = 'Microsoft YaHei'
            english_font = 'Segoe UI'
            monospace_font = 'Consolas'
        elif self.system == 'Linux':
            # Linux 系统使用支持中文的开源字体，按优先级排序，移除Microsoft YaHei避免警告
            chinese_font_candidates = [
                'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                'Liberation Sans', 'DejaVu Sans'
            ]
            chinese_font = self.find_available_font(chinese_font_candidates)
            english_font = 'DejaVu Sans'
            monospace_font = 'DejaVu Sans Mono'
        else:
            # 其他系统使用通用字体
            chinese_font = 'Liberation Sans'
            english_font = 'Liberation Sans'
            monospace_font = 'Liberation Mono'
        
        print(f"中文字体: {chinese_font}, 英文字体: {english_font}, 等宽字体: {monospace_font}")
        
        # 所有系统统一放大 1.5 倍字体
        base_font_size = int(9 * 1.5)  # 13.5 -> 13
        print(f"字体统一放大 1.5 倍，基础字体大小: {base_font_size}")
        
        # 字体配置参数（与data_viewer一致）
        self.font_config = {
            # 按钮字体 - 支持中英文
            'button_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'button_font_size': base_font_size,
            'button_font_weight': 'normal',
            
            # 标题字体
            'title_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'title_font_size': base_font_size,
            'title_font_weight': 'bold',
            
            # 状态标签字体
            'status_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'status_font_size': base_font_size,
            'status_font_weight': 'normal',
            
            # 文本框字体
            'text_font_family': monospace_font,
            'text_font_size': base_font_size,
            'text_font_weight': 'normal',
            
            # 文件列表字体（放大 1.25 倍）
            'list_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'list_font_size': int(base_font_size * 1.25),  # 13 * 1.25 = 16.25 -> 16
            'list_font_weight': 'normal',
        }
        
        # 验证字体可用性并设置备用字体
        self._validate_and_set_fonts()
        
        # 创建字体对象（与data_viewer一致）
        self.fonts = {
            'button_font': tkFont.Font(
                family=self.font_config['button_font_family'],
                size=self.font_config['button_font_size'],
                weight=self.font_config['button_font_weight']
            ),
            'title_font': tkFont.Font(
                family=self.font_config['title_font_family'],
                size=self.font_config['title_font_size'],
                weight=self.font_config['title_font_weight']
            ),
            'status_font': tkFont.Font(
                family=self.font_config['status_font_family'],
                size=self.font_config['status_font_size'],
                weight=self.font_config['status_font_weight']
            ),
            'text_font': tkFont.Font(
                family=self.font_config['text_font_family'],
                size=self.font_config['text_font_size'],
                weight=self.font_config['text_font_weight']
            ),
            'list_font': tkFont.Font(
                family=self.font_config['list_font_family'],
                size=self.font_config['list_font_size'],
                weight=self.font_config['list_font_weight']
            )
        }
        
        # 向后兼容性：保持原有的字体变量
        self.default_font = self.fonts['button_font']
        self.large_font = self.fonts['title_font']
        self.small_font = tkFont.Font(
            family=self.font_config['button_font_family'],
            size=int(base_font_size * 0.9),  # 稍小一点的字体
            weight='normal'
        )
    
    def _validate_and_set_fonts(self):
        """验证和设置字体"""
        # 这个方法现在主要用于字体验证，实际的字体创建在后面进行
        # 可以在这里添加字体可用性检查逻辑
        available_fonts = self.get_available_fonts()
        
        # 验证中文字体
        chinese_font = self.font_config['button_font_family']
        if chinese_font not in available_fonts:
            # 尝试备用中文字体
            if self.system == 'Linux':
                fallback_chinese = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                    'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                    'Liberation Sans', 'DejaVu Sans', 'Arial'
                ]
            elif self.system == 'Windows':
                fallback_chinese = ['Microsoft YaHei', 'SimHei', 'Arial']
            else:
                fallback_chinese = ['Liberation Sans', 'DejaVu Sans', 'Arial']

            chinese_font = self.find_available_font(fallback_chinese)
        
        # 更新字体配置
        self.font_config.update({
            'button_font_family': chinese_font if self.current_language == 'zh' else self.font_config['button_font_family'],
            'title_font_family': chinese_font if self.current_language == 'zh' else self.font_config['title_font_family'],
            'status_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
            'list_font_family': chinese_font if self.current_language == 'zh' else self.font_config['list_font_family'],
        })
        
        print(f"最终选择字体 - 中文: {chinese_font}")
    
    def find_available_font(self, font_candidates: List[str]) -> str:
        """
        从候选字体列表中找到第一个可用的字体
        
        Args:
            font_candidates: 候选字体名称列表
            
        Returns:
            第一个可用的字体名称
        """
        available_fonts = self.get_available_fonts()
        for font in font_candidates:
            if font in available_fonts:
                return font
        # 如果都没找到，返回系统默认字体
        return tkFont.nametofont("TkDefaultFont").actual()['family']
    
    def get_available_fonts(self) -> List[str]:
        """
        获取系统可用字体列表
        
        Returns:
            系统中可用的字体名称列表
        """
        return sorted(tkFont.families())
    
    def get_check_symbol(self, selected: bool) -> str:
        """
        根据系统环境获取合适的选择符号
        
        Args:
            selected: 是否选中
            
        Returns:
            选择符号字符串
        """
        if self.system == 'Windows':
            # Windows环境使用兼容性更好的符号
            return '√' if selected else '○'
        else:
            # Linux/Unix环境使用Unicode符号
            return '✓' if selected else '○'
    
    def get_font(self, font_type: str) -> tkFont.Font:
        """
        获取指定类型的字体
        
        Args:
            font_type: 字体类型 ('button', 'title', 'status', 'text', 'list', 'default', 'large', 'small')
            
        Returns:
            字体对象
        """
        if font_type in self.fonts:
            return self.fonts[font_type]
        elif font_type == 'default':
            return self.default_font
        elif font_type == 'large':
            return self.large_font
        elif font_type == 'small':
            return self.small_font
        else:
            return self.default_font
    
    def get_font_config(self) -> Dict[str, Any]:
        """
        获取字体配置字典
        
        Returns:
            字体配置字典
        """
        return self.font_config.copy()
    
    def update_language(self, language: str):
        """
        更新语言设置并重新配置字体
        
        Args:
            language: 新的语言设置 ('zh' 或 'en')
        """
        if language != self.current_language:
            self.current_language = language
            self.setup_fonts()
    
    def get_scaled_font(self, language: Optional[str] = None) -> tkFont.Font:
        """
        获取缩放字体（用于消息框等）
        
        Args:
            language: 语言设置，如果为None则使用当前语言
            
        Returns:
            缩放后的字体对象
        """
        if language is None:
            language = self.current_language
        
        if self.system == 'Windows':
            font_family = 'Microsoft YaHei' if language == 'zh' else 'Segoe UI'
        elif self.system == 'Linux':
            if language == 'zh':
                # Linux中文字体候选列表，移除Microsoft YaHei避免警告
                chinese_candidates = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                    'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                    'Liberation Sans', 'DejaVu Sans'
                ]
                # 检查可用字体
                available_fonts = tkFont.families()
                font_family = 'DejaVu Sans'  # 默认值
                for font in chinese_candidates:
                    if font in available_fonts:
                        font_family = font
                        break
            else:
                font_family = 'DejaVu Sans'
        else:
            font_family = 'Liberation Sans'  # 其他系统使用更通用的字体
        
        base_size = int(9 * 1.5)
        return tkFont.Font(family=font_family, size=base_size, weight='normal')
    
    def get_tree_font_config(self) -> tuple:
        """
        获取TreeView的字体配置
        
        Returns:
            (字体元组, 字体大小, 行高)
        """
        tree_font_size = int(self.font_config['button_font_size'] * 1.1)  # 比基础字体稍大
        tree_font = (self.font_config['button_font_family'], tree_font_size)
        row_height = int(tree_font_size * 1.8)  # 增加行高
        
        return tree_font, tree_font_size, row_height
