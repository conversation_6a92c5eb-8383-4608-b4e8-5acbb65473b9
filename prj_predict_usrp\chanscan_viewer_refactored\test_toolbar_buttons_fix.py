#!/usr/bin/env python3
"""
测试所有工具栏按钮功能修复是否生效
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_toolbar_button_callbacks():
    """测试工具栏按钮回调设置"""
    print("测试工具栏按钮回调设置...")
    
    try:
        # 检查application.py中的回调设置
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有工具栏回调是否正确设置
        expected_callbacks = [
            "'select_file': self.select_file",
            "'select_folder': self.select_folder", 
            "'select_and_load_model': self.select_and_load_model",
            "'prev_file': self.prev_file",
            "'next_file': self.next_file",
            "'capture_data': self.capture_data",
            "'run_channel_scan': self.run_channel_scan",
            "'view_database': self.view_database",
            "'toggle_view_mode': self.toggle_view_mode",
            "'toggle_language': self.toggle_language"
        ]
        
        missing_callbacks = []
        for callback in expected_callbacks:
            if callback not in content:
                missing_callbacks.append(callback)
        
        if missing_callbacks:
            print(f"❌ 缺少回调设置: {missing_callbacks}")
            return False
        else:
            print("✅ 所有工具栏回调正确设置")
        
        # 检查回调设置调用
        if 'self.toolbar.set_callbacks(toolbar_callbacks)' in content:
            print("✅ 工具栏回调正确传递")
        else:
            print("❌ 工具栏回调传递缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工具栏按钮回调测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_method_implementations():
    """测试按钮方法实现"""
    print("\n测试按钮方法实现...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有按钮方法是否存在
        expected_methods = [
            'def select_file(self):',
            'def select_folder(self):',
            'def select_and_load_model(self):',
            'def prev_file(self):',
            'def next_file(self):',
            'def capture_data(self):',
            'def run_channel_scan(self):',
            'def view_database(self):',
            'def toggle_view_mode(self):',
            'def toggle_language(self):'
        ]
        
        missing_methods = []
        for method in expected_methods:
            if method not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法实现: {missing_methods}")
            return False
        else:
            print("✅ 所有按钮方法都已实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮方法实现测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_operations():
    """测试文件操作功能"""
    print("\n测试文件操作功能...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查文件选择功能
        if 'filedialog.askopenfilename(' in content:
            print("✅ 文件选择对话框实现正确")
        else:
            print("❌ 文件选择对话框实现缺失")
            return False
        
        # 检查文件夹选择功能
        if 'filedialog.askdirectory(' in content:
            print("✅ 文件夹选择对话框实现正确")
        else:
            print("❌ 文件夹选择对话框实现缺失")
            return False
        
        # 检查load_file和load_folder方法调用
        if 'self.load_file(file_path)' in content and 'self.load_folder(folder_path)' in content:
            print("✅ 文件和文件夹加载方法调用正确")
        else:
            print("❌ 文件和文件夹加载方法调用缺失")
            return False
        
        # 检查最后选择路径的保存
        if 'self.config_manager.update_last_dir(' in content:
            print("✅ 最后选择路径保存功能正确")
        else:
            print("❌ 最后选择路径保存功能缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_selection():
    """测试模型选择功能"""
    print("\n测试模型选择功能...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模型文件类型
        if 'self.file_manager.MODEL_FILE_TYPES' in content:
            print("✅ 模型文件类型定义正确")
        else:
            print("❌ 模型文件类型定义缺失")
            return False
        
        # 检查模型路径保存
        if 'self.model_path = model_path' in content:
            print("✅ 模型路径保存正确")
        else:
            print("❌ 模型路径保存缺失")
            return False
        
        # 检查成功消息显示
        if 'ScaledMessageBox.showinfo(' in content and '模型选择成功' in content:
            print("✅ 模型选择成功消息显示正确")
        else:
            print("❌ 模型选择成功消息显示缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型选择功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_navigation_buttons():
    """测试导航按钮功能"""
    print("\n测试导航按钮功能...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查边界条件检查
        if 'self.file_manager.has_previous_file()' in content and 'self.file_manager.has_next_file()' in content:
            print("✅ 导航按钮边界条件检查正确")
        else:
            print("❌ 导航按钮边界条件检查缺失")
            return False
        
        # 检查文件列表标记更新
        if 'self.update_file_list_markers(old_index, self.file_manager.current_index)' in content:
            print("✅ 导航按钮文件列表标记更新正确")
        else:
            print("❌ 导航按钮文件列表标记更新缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导航按钮功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_required_text_keys():
    """测试必需的文本键"""
    print("\n测试必需的文本键...")
    
    try:
        with open('utils/i18n.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有必需的文本键
        required_keys = [
            'select_file', 'select_folder', 'select_model',
            'warning', 'error', 'success',
            'scan_not_available', 'no_data_loaded', 'scan_failed',
            'load_file_failed', 'no_files_found'
        ]
        
        missing_keys = []
        for key in required_keys:
            if f"'{key}':" not in content:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 缺少文本键: {missing_keys}")
            return False
        else:
            print("✅ 所有必需的文本键都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本键测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试所有工具栏按钮功能修复...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_toolbar_button_callbacks,
        test_button_method_implementations,
        test_file_operations,
        test_model_selection,
        test_navigation_buttons,
        test_required_text_keys
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具栏按钮功能修复成功")
        print("所有按钮现在应该能正常工作，包括错误处理和用户提示")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
