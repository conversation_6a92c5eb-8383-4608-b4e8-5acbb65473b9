#!/usr/bin/env python3
"""
布局调试工具

深入分析所有组件的位置、大小和层次结构
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_widget_tree(widget, level=0, parent_name="root"):
    """递归分析组件树"""
    indent = "  " * level
    widget_name = str(widget).split('.')[-1]
    
    try:
        # 获取组件信息
        widget_class = widget.__class__.__name__
        
        # 尝试获取几何信息
        try:
            x = widget.winfo_x()
            y = widget.winfo_y()
            width = widget.winfo_width()
            height = widget.winfo_height()
            req_width = widget.winfo_reqwidth()
            req_height = widget.winfo_reqheight()
            geometry = f"位置:({x},{y}) 大小:{width}x{height} 请求:{req_width}x{req_height}"
        except:
            geometry = "几何信息不可用"
        
        # 尝试获取pack信息
        try:
            pack_info = widget.pack_info()
            pack_str = f"pack:{pack_info}" if pack_info else "未pack"
        except:
            pack_str = "pack信息不可用"
        
        # 尝试获取背景色
        try:
            bg = widget.cget('bg')
            bg_str = f"背景:{bg}"
        except:
            bg_str = ""
        
        print(f"{indent}{widget_class}({widget_name}) - {geometry} {pack_str} {bg_str}")
        
        # 递归分析子组件
        try:
            children = widget.winfo_children()
            for child in children:
                analyze_widget_tree(child, level + 1, widget_name)
        except:
            pass
            
    except Exception as e:
        print(f"{indent}分析组件失败: {e}")

def debug_main_app():
    """调试主应用程序的布局"""
    print("开始调试主应用程序布局...")
    
    try:
        # 导入主应用程序
        from core.application import ChannelScanViewerApp
        
        # 创建应用程序
        app = ChannelScanViewerApp()
        
        # 等待界面完全加载
        app.main_window.get_root().update_idletasks()
        
        print("\n=== 完整组件树分析 ===")
        analyze_widget_tree(app.main_window.get_root())
        
        print("\n=== 工具栏详细信息 ===")
        if hasattr(app, 'toolbar') and hasattr(app.toolbar, 'toolbar_frame'):
            toolbar_frame = app.toolbar.toolbar_frame
            print(f"工具栏框架: {toolbar_frame}")
            print(f"工具栏父容器: {toolbar_frame.master}")
            print(f"工具栏位置: ({toolbar_frame.winfo_x()}, {toolbar_frame.winfo_y()})")
            print(f"工具栏大小: {toolbar_frame.winfo_width()}x{toolbar_frame.winfo_height()}")
            print(f"工具栏请求大小: {toolbar_frame.winfo_reqwidth()}x{toolbar_frame.winfo_reqheight()}")
            print(f"工具栏可见性: {toolbar_frame.winfo_viewable()}")
            print(f"工具栏映射状态: {toolbar_frame.winfo_ismapped()}")
            
            try:
                pack_info = toolbar_frame.pack_info()
                print(f"工具栏pack信息: {pack_info}")
            except:
                print("工具栏pack信息不可用")
            
            # 分析工具栏子组件
            print("\n工具栏子组件:")
            children = toolbar_frame.winfo_children()
            for i, child in enumerate(children):
                print(f"  子组件{i}: {child} - {child.__class__.__name__}")
                print(f"    位置: ({child.winfo_x()}, {child.winfo_y()})")
                print(f"    大小: {child.winfo_width()}x{child.winfo_height()}")
        
        print("\n=== 面板管理器详细信息 ===")
        if hasattr(app, 'panel_manager') and hasattr(app.panel_manager, 'main_paned'):
            main_paned = app.panel_manager.main_paned
            print(f"主面板: {main_paned}")
            print(f"主面板父容器: {main_paned.master}")
            print(f"主面板位置: ({main_paned.winfo_x()}, {main_paned.winfo_y()})")
            print(f"主面板大小: {main_paned.winfo_width()}x{main_paned.winfo_height()}")
            
            try:
                pack_info = main_paned.pack_info()
                print(f"主面板pack信息: {pack_info}")
            except:
                print("主面板pack信息不可用")
        
        print("\n=== 主窗口信息 ===")
        root = app.main_window.get_root()
        print(f"主窗口大小: {root.winfo_width()}x{root.winfo_height()}")
        print(f"主窗口几何: {root.geometry()}")
        
        # 检查所有直接子组件
        print("\n主窗口直接子组件:")
        for i, child in enumerate(root.winfo_children()):
            print(f"  子组件{i}: {child} - {child.__class__.__name__}")
            print(f"    位置: ({child.winfo_x()}, {child.winfo_y()})")
            print(f"    大小: {child.winfo_width()}x{child.winfo_height()}")
            try:
                pack_info = child.pack_info()
                print(f"    pack信息: {pack_info}")
            except:
                print("    pack信息不可用")
        
        print("\n调试完成，程序将继续运行...")
        print("请检查控制台输出的布局信息")
        
        # 运行应用程序
        app.run()
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_main_app()
