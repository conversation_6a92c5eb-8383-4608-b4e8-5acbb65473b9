"""
字体工具模块

提供字体相关的工具函数，包括matplotlib字体配置、系统字体检测等功能。
"""

import platform
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm


def configure_matplotlib_fonts():
    """
    智能配置matplotlib字体，避免字体警告

    根据操作系统自动选择合适的中文字体，确保图表中的中文文本能够正确显示。
    支持Linux、Windows和其他操作系统的字体配置。
    """
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    if platform.system() == 'Linux':
        # Linux系统字体候选列表，按优先级排序
        font_candidates = [
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
            'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
            'SimHei', 'Liberation Sans'
        ]
        # 只添加系统中实际存在的字体
        valid_fonts = [font for font in font_candidates if font in available_fonts]
        # 如果没有找到任何中文字体，添加DejaVu Sans作为最后备选
        if not valid_fonts:
            valid_fonts = ['DejaVu Sans']
        plt.rcParams['font.sans-serif'] = valid_fonts
    elif platform.system() == 'Windows':
        # Windows系统
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
    else:
        # 其他系统使用通用字体
        plt.rcParams['font.sans-serif'] = ['Liberation Sans', 'DejaVu Sans', 'Arial']

    plt.rcParams['axes.unicode_minus'] = False
    print(f"matplotlib字体配置: {plt.rcParams['font.sans-serif'][:3]}...")


def get_available_fonts():
    """
    获取系统可用字体列表
    
    Returns:
        list: 系统中可用的字体名称列表
    """
    return [f.name for f in fm.fontManager.ttflist]


def find_best_font(candidates):
    """
    从候选字体列表中找到第一个可用的字体
    
    Args:
        candidates (list): 候选字体名称列表
        
    Returns:
        str: 第一个可用的字体名称，如果都不可用则返回None
    """
    available_fonts = get_available_fonts()
    for font in candidates:
        if font in available_fonts:
            return font
    return None


def get_system_default_fonts():
    """
    根据操作系统获取默认字体配置
    
    Returns:
        list: 适合当前操作系统的字体列表
    """
    if platform.system() == 'Linux':
        return [
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
            'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
            'SimHei', 'Liberation Sans', 'DejaVu Sans'
        ]
    elif platform.system() == 'Windows':
        return ['Microsoft YaHei', 'SimHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
    else:
        return ['Liberation Sans', 'DejaVu Sans', 'Arial']


def check_font_availability(font_name):
    """
    检查指定字体是否可用
    
    Args:
        font_name (str): 字体名称
        
    Returns:
        bool: 字体是否可用
    """
    available_fonts = get_available_fonts()
    return font_name in available_fonts
