#!/usr/bin/env python3
"""
测试模块导入

用于验证所有模块是否可以正常导入
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块的导入"""
    print("开始测试模块导入...")
    
    # 测试核心模块
    try:
        from core.config_manager import ConfigManager
        print("✅ core.config_manager 导入成功")
    except Exception as e:
        print(f"❌ core.config_manager 导入失败: {e}")
    
    # 测试UI模块
    try:
        from ui.main_window import MainWindow
        print("✅ ui.main_window 导入成功")
    except Exception as e:
        print(f"❌ ui.main_window 导入失败: {e}")
    
    try:
        from ui.styles import StyleManager
        print("✅ ui.styles 导入成功")
    except Exception as e:
        print(f"❌ ui.styles 导入失败: {e}")
    
    try:
        from ui.toolbar import Toolbar
        print("✅ ui.toolbar 导入成功")
    except Exception as e:
        print(f"❌ ui.toolbar 导入失败: {e}")
    
    try:
        from ui.panels import PanelManager
        print("✅ ui.panels 导入成功")
    except Exception as e:
        print(f"❌ ui.panels 导入失败: {e}")
    
    # 测试数据模块
    try:
        from data.file_manager import FileManager
        print("✅ data.file_manager 导入成功")
    except Exception as e:
        print(f"❌ data.file_manager 导入失败: {e}")
    
    try:
        from data.database_manager import DatabaseManager
        print("✅ data.database_manager 导入成功")
    except Exception as e:
        print(f"❌ data.database_manager 导入失败: {e}")
    
    try:
        from data.signal_processor import SignalProcessor
        print("✅ data.signal_processor 导入成功")
    except Exception as e:
        print(f"❌ data.signal_processor 导入失败: {e}")
    
    # 测试可视化模块
    try:
        from visualization.chart_manager import ChartManager
        print("✅ visualization.chart_manager 导入成功")
    except Exception as e:
        print(f"❌ visualization.chart_manager 导入失败: {e}")
    
    try:
        from visualization.plot_renderer import PlotRenderer
        print("✅ visualization.plot_renderer 导入成功")
    except Exception as e:
        print(f"❌ visualization.plot_renderer 导入失败: {e}")
    
    # 测试信道扫描模块
    try:
        from channel_scan.scanner import ChannelScanner
        print("✅ channel_scan.scanner 导入成功")
    except Exception as e:
        print(f"❌ channel_scan.scanner 导入失败: {e}")
    
    try:
        from channel_scan.segment_manager import SegmentManager
        print("✅ channel_scan.segment_manager 导入成功")
    except Exception as e:
        print(f"❌ channel_scan.segment_manager 导入失败: {e}")
    
    # 测试工具模块
    try:
        from utils.i18n import I18nManager
        print("✅ utils.i18n 导入成功")
    except Exception as e:
        print(f"❌ utils.i18n 导入失败: {e}")
    
    try:
        from utils.font_utils import configure_matplotlib_fonts
        print("✅ utils.font_utils 导入成功")
    except Exception as e:
        print(f"❌ utils.font_utils 导入失败: {e}")
    
    try:
        from utils.message_box import ScaledMessageBox
        print("✅ utils.message_box 导入成功")
    except Exception as e:
        print(f"❌ utils.message_box 导入失败: {e}")
    
    # 测试主应用程序
    try:
        from core.application import ChannelScanViewerApp
        print("✅ core.application 导入成功")
    except Exception as e:
        print(f"❌ core.application 导入失败: {e}")
    
    print("\n模块导入测试完成！")

if __name__ == "__main__":
    test_imports()
