#!/usr/bin/env python3
"""
测试绘图布局修复是否生效
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_chart_manager_layout():
    """测试ChartManager的布局配置"""
    print("测试ChartManager布局配置...")

    try:
        # 简化测试：直接检查代码中的布局配置
        with open('visualization/chart_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查2D模式的布局配置
        if 'self.fig.add_subplot(4, 1, 1)' in content:
            print("✅ 找到正确的4x1布局配置（第1个子图）")
        else:
            print("❌ 未找到正确的4x1布局配置（第1个子图）")
            return False

        if 'self.fig.add_subplot(4, 1, 2)' in content:
            print("✅ 找到正确的4x1布局配置（第2个子图）")
        else:
            print("❌ 未找到正确的4x1布局配置（第2个子图）")
            return False

        if 'self.fig.add_subplot(4, 1, 3)' in content:
            print("✅ 找到正确的4x1布局配置（第3个子图）")
        else:
            print("❌ 未找到正确的4x1布局配置（第3个子图）")
            return False

        if 'self.fig.add_subplot(4, 1, 4)' in content:
            print("✅ 找到正确的4x1布局配置（第4个子图）")
        else:
            print("❌ 未找到正确的4x1布局配置（第4个子图）")
            return False

        # 检查是否没有2x2布局
        if 'add_subplot(2, 2,' in content:
            print("❌ 仍然存在2x2布局配置")
            return False
        else:
            print("✅ 确认没有2x2布局配置")

        # 检查注释说明
        if '# 2D 模式：4个子图，垂直排列（与原版本一致）' in content:
            print("✅ 找到正确的布局说明注释")
        else:
            print("⚠️  布局说明注释可能需要更新")

        print("✅ ChartManager布局配置测试通过")
        return True

    except Exception as e:
        print(f"❌ ChartManager布局配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_renderer_layout():
    """测试PlotRenderer的布局配置"""
    print("\n测试PlotRenderer布局配置...")

    try:
        # 简化测试：直接检查代码中的布局配置
        with open('visualization/plot_renderer.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查render_homepage_charts方法中的布局配置
        if 'fig.add_subplot(4, 1, 1)' in content:
            print("✅ PlotRenderer中找到正确的4x1布局配置（第1个子图）")
        else:
            print("❌ PlotRenderer中未找到正确的4x1布局配置（第1个子图）")
            return False

        if 'fig.add_subplot(4, 1, 2)' in content:
            print("✅ PlotRenderer中找到正确的4x1布局配置（第2个子图）")
        else:
            print("❌ PlotRenderer中未找到正确的4x1布局配置（第2个子图）")
            return False

        if 'fig.add_subplot(4, 1, 3)' in content:
            print("✅ PlotRenderer中找到正确的4x1布局配置（第3个子图）")
        else:
            print("❌ PlotRenderer中未找到正确的4x1布局配置（第3个子图）")
            return False

        if 'fig.add_subplot(4, 1, 4)' in content:
            print("✅ PlotRenderer中找到正确的4x1布局配置（第4个子图）")
        else:
            print("❌ PlotRenderer中未找到正确的4x1布局配置（第4个子图）")
            return False

        # 检查是否已经移除了2x2布局
        if 'fig.add_subplot(2, 2,' in content:
            print("❌ PlotRenderer中仍然存在2x2布局配置")
            return False
        else:
            print("✅ PlotRenderer中确认已移除2x2布局配置")

        # 检查注释说明
        if '# 创建1x4垂直子图布局（与原版本一致）' in content:
            print("✅ PlotRenderer中找到正确的布局说明注释")
        else:
            print("⚠️  PlotRenderer中布局说明注释可能需要更新")

        print("✅ PlotRenderer布局配置测试通过")
        return True

    except Exception as e:
        print(f"❌ PlotRenderer布局配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试绘图布局修复...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_chart_manager_layout,
        test_plot_renderer_layout
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！绘图布局修复成功")
        print("图表现在使用正确的1x4垂直布局，与原版本一致")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
