# Linux字体问题解决方案

## 问题描述

在Linux系统下运行chanscan_viewer时可能遇到以下问题：

1. **字体警告**: `findfont: Font family 'Microsoft YaHei' not found.`
2. **弹窗错误**: `grab failed: window not viewable`
3. **中文显示异常**: 中文字符显示为方块或乱码

## 解决方案

### 1. 快速修复（推荐）

运行自动修复脚本：

```bash
# 进入chanscan_viewer目录
cd prj_predict_usrp/chanscan_viewer

# 给脚本添加执行权限
chmod +x fix_linux_fonts.sh

# 运行修复脚本
./fix_linux_fonts.sh
```

### 2. 手动安装字体

#### Ubuntu/Debian系统：

```bash
# 更新包列表
sudo apt update

# 安装基础字体
sudo apt install -y fonts-liberation fonts-dejavu-core

# 安装中文字体
sudo apt install -y fonts-wqy-zenhei fonts-wqy-microhei

# 安装Noto CJK字体（推荐）
sudo apt install -y fonts-noto-cjk

# 刷新字体缓存
fc-cache -fv
```

#### CentOS/RHEL系统：

```bash
# 安装基础字体
sudo yum install -y liberation-fonts dejavu-sans-fonts

# 安装中文字体
sudo yum install -y wqy-zenhei-fonts wqy-microhei-fonts

# 安装Google Noto字体
sudo yum install -y google-noto-sans-cjk-fonts

# 刷新字体缓存
fc-cache -fv
```

#### Fedora系统：

```bash
# 安装基础字体
sudo dnf install -y liberation-fonts dejavu-sans-fonts

# 安装中文字体
sudo dnf install -y wqy-zenhei-fonts wqy-microhei-fonts

# 安装Google Noto字体
sudo dnf install -y google-noto-sans-cjk-fonts

# 刷新字体缓存
fc-cache -fv
```

#### Arch Linux系统：

```bash
# 安装基础字体
sudo pacman -S --noconfirm ttf-liberation ttf-dejavu

# 安装中文字体
sudo pacman -S --noconfirm wqy-zenhei wqy-microhei

# 安装Noto字体
sudo pacman -S --noconfirm noto-fonts-cjk

# 刷新字体缓存
fc-cache -fv
```

### 3. 验证字体安装

运行字体测试脚本：

```bash
# 测试字体配置
python3 test_fonts_linux.py
```

或者手动检查：

```bash
# 检查中文字体
fc-list :lang=zh

# 检查特定字体
fc-list :family="WenQuanYi Zen Hei"
fc-list :family="Liberation Sans"
```

### 4. 环境变量设置（可选）

如果仍有显示问题，可以设置以下环境变量：

```bash
# 设置字体配置
export FONTCONFIG_PATH=/etc/fonts

# 设置语言环境
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 设置显示
export DISPLAY=:0.0
```

## 代码修改说明

### 已修复的问题：

1. **移除Microsoft YaHei依赖**: 
   - 在Linux下不再尝试使用Microsoft YaHei字体
   - 使用Linux原生字体作为替代

2. **修复弹窗显示问题**:
   - 在设置`grab_set()`前确保窗口可见
   - 添加异常处理避免程序崩溃

3. **优化字体选择逻辑**:
   - 按优先级选择可用字体
   - 提供多个备选字体方案

### 字体优先级（Linux）：

1. Noto Sans CJK SC（推荐）
2. Source Han Sans SC
3. WenQuanYi Zen Hei
4. WenQuanYi Micro Hei
5. Droid Sans Fallback
6. AR PL UMing CN
7. Liberation Sans
8. DejaVu Sans

## 常见问题

### Q: 安装字体后仍然有警告？
A: 重启应用程序，或运行 `fc-cache -fv` 刷新字体缓存。

### Q: 弹窗仍然是空的？
A: 检查DISPLAY环境变量设置，确保X11转发正常工作。

### Q: 中文显示为方块？
A: 确保安装了中文字体包，并且系统语言环境设置正确。

### Q: 在远程服务器上运行有问题？
A: 确保SSH连接启用了X11转发：`ssh -X username@server`

## 测试步骤

1. 运行字体测试脚本：`python3 test_fonts_linux.py`
2. 启动chanscan_viewer：`./start_chanscan_viewer.sh`
3. 加载数据文件并检查分段列表
4. 双击分段项测试弹窗显示
5. 检查控制台是否还有字体警告

## 技术支持

如果问题仍然存在，请提供以下信息：

1. Linux发行版和版本：`cat /etc/os-release`
2. Python版本：`python3 --version`
3. 已安装字体：`fc-list | grep -i "zen\|noto\|liberation"`
4. 错误日志：完整的错误信息
5. 显示环境：`echo $DISPLAY`

## 更新日志

- 2024-01-XX: 初始版本，修复基础字体问题
- 2024-01-XX: 添加弹窗显示修复
- 2024-01-XX: 优化字体选择逻辑
