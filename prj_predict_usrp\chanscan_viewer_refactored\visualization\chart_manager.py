"""
图表管理模块

负责图表的创建、配置和管理，包括matplotlib图表的初始化、布局和事件处理。
保持与原有代码完全一致的图表样式和配置。
"""

import tkinter as tk
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import numpy as np
from typing import List, Optional, Tuple, Dict, Any, Callable


class ChartManager:
    """
    图表管理器
    
    负责matplotlib图表的创建、配置和管理，保持与原有代码完全一致的图表设置。
    """
    
    def __init__(self, parent: tk.Widget, style_manager, i18n_manager):
        """
        初始化图表管理器
        
        Args:
            parent: 父容器
            style_manager: 样式管理器
            i18n_manager: 国际化管理器
        """
        self.parent = parent
        self.style_manager = style_manager
        self.i18n_manager = i18n_manager
        
        # 图表相关属性
        self.fig = None
        self.canvas = None
        self.toolbar = None
        self.axes = []
        self.is_3d_mode = False
        self.is_computing = False
        
        # 缩放相关属性
        self.time_plot_original_xlim = None
        self.spectrum_plot_original_xlim = None
        
        # 回调函数
        self.callbacks = {}
        
        # 创建图表容器
        self.create_chart_container()
        
        # 配置matplotlib字体
        self.configure_matplotlib_fonts()
    
    def configure_matplotlib_fonts(self):
        """配置matplotlib字体"""
        # 使用样式管理器的字体配置
        try:
            from utils.font_utils import configure_matplotlib_fonts
            configure_matplotlib_fonts()
        except ImportError:
            # 如果导入失败，使用基本的字体配置
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Liberation Sans', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False
    
    def create_chart_container(self):
        """创建图表容器"""
        # 图表容器框架
        chart_container = tk.Frame(self.parent)
        chart_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建matplotlib图形 - 使用较小的初始尺寸避免窗口自动调整
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.fig.patch.set_facecolor('white')
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_container)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏
        toolbar_frame = tk.Frame(chart_container)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 绑定鼠标滚轮事件
        self.canvas.mpl_connect('scroll_event', self.on_mouse_scroll)
        
        # 初始化子图
        self.create_subplots()
        self.init_plots()
    
    def create_subplots(self):
        """创建子图"""
        self.fig.clear()
        
        if self.is_3d_mode:
            # 3D 模式：2个子图垂直排列，都是3D图
            self.axes = []
            self.axes.append(self.fig.add_subplot(2, 1, 1, projection='3d'))
            self.axes.append(self.fig.add_subplot(2, 1, 2, projection='3d'))
        else:
            # 2D 模式：4个子图，垂直排列（与原版本一致）
            self.axes = []
            self.axes.append(self.fig.add_subplot(4, 1, 1))  # 时域信号点值图
            self.axes.append(self.fig.add_subplot(4, 1, 2))  # 时域信号图
            self.axes.append(self.fig.add_subplot(4, 1, 3))  # 频谱图
            self.axes.append(self.fig.add_subplot(4, 1, 4))  # 时频图
        
        # 更新图表标题
        self.update_chart_titles()
        
        # 调整布局
        self.fig.tight_layout(pad=2.0)
    
    def update_chart_titles(self):
        """更新图表标题"""
        if hasattr(self, 'axes') and self.axes:
            texts = self.i18n_manager.get_all_texts()
            
            if self.is_3d_mode:
                # 3D模式：只有2个图表
                titles = [
                    texts['power_spectral_density_3d'],
                    texts['filtered_psd_3d']
                ]
                for i, ax in enumerate(self.axes):
                    if i < len(titles):
                        ax.set_title(titles[i], fontsize=12, pad=10)
            else:
                # 2D模式：4个图表
                titles = [
                    texts['time_domain_points'],
                    texts['time_domain_signal'],
                    texts['frequency_spectrum'],
                    texts['time_frequency']
                ]
                for i, ax in enumerate(self.axes):
                    if i < len(titles):
                        ax.set_title(titles[i], fontsize=12, pad=10)
    
    def init_plots(self):
        """初始化图表"""
        if hasattr(self, 'axes') and self.axes:
            texts = self.i18n_manager.get_all_texts()
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, texts['please_select'], 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=14, alpha=0.7)
            
            # 更新标题
            self.update_chart_titles()
            self.canvas.draw()
    
    def toggle_view_mode(self):
        """切换2D/3D视图模式"""
        self.is_3d_mode = not self.is_3d_mode
        self.create_subplots()
        self.init_plots()
        
        # 返回新的视图模式文本
        texts = self.i18n_manager.get_all_texts()
        return texts['view_3d'] if self.is_3d_mode else texts['view_2d']
    
    def clear_plots(self):
        """清空所有图表"""
        if hasattr(self, 'axes') and self.axes:
            for ax in self.axes:
                ax.clear()
            self.canvas.draw()
    
    def set_computing_state(self, is_computing: bool):
        """设置计算状态"""
        self.is_computing = is_computing
    
    def get_computing_state(self) -> bool:
        """获取计算状态"""
        return self.is_computing
    
    def on_mouse_scroll(self, event):
        """处理鼠标滚轮事件"""
        if event.inaxes is None:
            return
        
        # 根据子图位置确定处理方式
        if hasattr(self, 'axes') and self.axes:
            if not self.is_3d_mode and len(self.axes) >= 4:
                # 2D模式下的缩放处理
                if event.inaxes == self.axes[0]:  # 时域信号点值图
                    self.handle_time_plot_zoom(event, self.axes[0])
                elif event.inaxes == self.axes[2]:  # 频谱图
                    self.handle_spectrum_plot_zoom(event, self.axes[2])
    
    def handle_time_plot_zoom(self, event, ax):
        """处理时域信号点值图的缩放"""
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.time_plot_original_xlim is None:
            self.time_plot_original_xlim = ax.get_xlim()
        
        # 获取当前X轴范围
        current_xlim = ax.get_xlim()
        current_range = current_xlim[1] - current_xlim[0]
        
        # 缩放因子
        zoom_factor = 1.2 if event.button == 'down' else 1/1.2
        
        # 计算新的范围
        new_range = current_range * zoom_factor
        
        # 限制最大缩放范围
        original_range = self.time_plot_original_xlim[1] - self.time_plot_original_xlim[0]
        if new_range > original_range * 1.1:  # 最大放大到原始范围的1.1倍
            new_range = original_range * 1.1
        
        # 限制最小缩放范围
        min_range = original_range * 0.01  # 最小缩小到原始范围的1%
        if new_range < min_range:
            new_range = min_range
        
        # 计算新的中心点（基于鼠标位置）
        if event.xdata is not None:
            center = event.xdata
        else:
            center = (current_xlim[0] + current_xlim[1]) / 2
        
        # 设置新的X轴范围
        new_xlim = [center - new_range/2, center + new_range/2]
        
        # 确保不超出原始范围
        if new_xlim[0] < self.time_plot_original_xlim[0]:
            offset = self.time_plot_original_xlim[0] - new_xlim[0]
            new_xlim[0] += offset
            new_xlim[1] += offset
        if new_xlim[1] > self.time_plot_original_xlim[1]:
            offset = new_xlim[1] - self.time_plot_original_xlim[1]
            new_xlim[0] -= offset
            new_xlim[1] -= offset
        
        ax.set_xlim(new_xlim)
        self.canvas.draw()
    
    def handle_spectrum_plot_zoom(self, event, ax):
        """处理频谱图的缩放"""
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.spectrum_plot_original_xlim is None:
            self.spectrum_plot_original_xlim = ax.get_xlim()
        
        # 获取当前X轴范围
        current_xlim = ax.get_xlim()
        current_range = current_xlim[1] - current_xlim[0]
        
        # 缩放因子
        zoom_factor = 1.2 if event.button == 'down' else 1/1.2
        
        # 计算新的范围
        new_range = current_range * zoom_factor
        
        # 限制最大缩放范围
        original_range = self.spectrum_plot_original_xlim[1] - self.spectrum_plot_original_xlim[0]
        if new_range > original_range * 1.1:
            new_range = original_range * 1.1
        
        # 限制最小缩放范围
        min_range = original_range * 0.01
        if new_range < min_range:
            new_range = min_range
        
        # 计算新的中心点
        if event.xdata is not None:
            center = event.xdata
        else:
            center = (current_xlim[0] + current_xlim[1]) / 2
        
        # 设置新的X轴范围
        new_xlim = [center - new_range/2, center + new_range/2]
        
        # 确保不超出原始范围
        if new_xlim[0] < self.spectrum_plot_original_xlim[0]:
            offset = self.spectrum_plot_original_xlim[0] - new_xlim[0]
            new_xlim[0] += offset
            new_xlim[1] += offset
        if new_xlim[1] > self.spectrum_plot_original_xlim[1]:
            offset = new_xlim[1] - self.spectrum_plot_original_xlim[1]
            new_xlim[0] -= offset
            new_xlim[1] -= offset
        
        ax.set_xlim(new_xlim)
        self.canvas.draw()
    
    def reset_zoom(self):
        """重置缩放"""
        if hasattr(self, 'axes') and self.axes:
            # 重置时域图缩放
            if self.time_plot_original_xlim is not None and len(self.axes) > 0:
                self.axes[0].set_xlim(self.time_plot_original_xlim)
                self.time_plot_original_xlim = None
            
            # 重置频谱图缩放
            if self.spectrum_plot_original_xlim is not None and len(self.axes) > 2:
                self.axes[2].set_xlim(self.spectrum_plot_original_xlim)
                self.spectrum_plot_original_xlim = None
            
            self.canvas.draw()
    
    def get_figure(self) -> Figure:
        """获取matplotlib图形对象"""
        return self.fig
    
    def get_canvas(self) -> FigureCanvasTkAgg:
        """获取画布对象"""
        return self.canvas
    
    def get_axes(self) -> List:
        """获取坐标轴列表"""
        return self.axes
    
    def get_toolbar(self) -> NavigationToolbar2Tk:
        """获取工具栏对象"""
        return self.toolbar
    
    def set_callback(self, callback_name: str, callback: Callable):
        """设置回调函数"""
        self.callbacks[callback_name] = callback
    
    def get_callback(self, callback_name: str) -> Optional[Callable]:
        """获取回调函数"""
        return self.callbacks.get(callback_name)
    
    def update_language(self):
        """更新语言（重新设置图表标题）"""
        self.update_chart_titles()
        self.canvas.draw()
    
    def save_figure(self, filename: str, **kwargs):
        """
        保存图形到文件
        
        Args:
            filename: 文件名
            **kwargs: 保存参数
        """
        try:
            self.fig.savefig(filename, **kwargs)
            return True
        except Exception as e:
            print(f"保存图形失败: {e}")
            return False
    
    def destroy(self):
        """销毁图表管理器"""
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
        if self.toolbar:
            self.toolbar.destroy()
