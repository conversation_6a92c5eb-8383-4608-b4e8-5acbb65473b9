#!/bin/bash
# 使用fc-list命令检查字体安装情况
# 避免tkinter依赖问题

echo "========================================"
echo "使用fc-list检查字体安装情况"
echo "========================================"

# 检查fc-list命令是否可用
if ! command -v fc-list &> /dev/null; then
    echo "错误: fc-list命令不可用"
    echo "请安装fontconfig包:"
    echo "  Ubuntu/Debian: sudo apt install fontconfig"
    echo "  CentOS/RHEL: sudo yum install fontconfig"
    echo "  Fedora: sudo dnf install fontconfig"
    exit 1
fi

echo "fc-list命令可用"
echo ""

# 检查中文字体支持
echo "=== 检查中文字体支持 ==="
CHINESE_FONTS=$(fc-list :lang=zh | wc -l)
echo "支持中文的字体数量: $CHINESE_FONTS"

if [ $CHINESE_FONTS -gt 0 ]; then
    echo "前10个中文字体:"
    fc-list :lang=zh | head -10 | while read line; do
        # 提取字体名称
        font_name=$(echo "$line" | cut -d: -f2 | cut -d, -f1 | sed 's/^ *//')
        echo "  - $font_name"
    done
else
    echo "未找到支持中文的字体"
fi

echo ""

# 检查特定字体
echo "=== 检查特定字体 ==="
FONTS_TO_CHECK=(
    "WenQuanYi Zen Hei"
    "WenQuanYi Micro Hei"
    "Noto Sans CJK SC"
    "Source Han Sans SC"
    "Droid Sans Fallback"
    "Liberation Sans"
    "DejaVu Sans"
)

for font in "${FONTS_TO_CHECK[@]}"; do
    if fc-list ":family=$font" | grep -q "$font"; then
        echo "✓ $font"
    else
        echo "✗ $font"
    fi
done

echo ""

# 搜索WenQuanYi字体
echo "=== 搜索WenQuanYi字体 ==="
WQY_FONTS=$(fc-list | grep -i wenquanyi)
if [ -n "$WQY_FONTS" ]; then
    echo "找到WenQuanYi字体:"
    echo "$WQY_FONTS" | while read line; do
        font_name=$(echo "$line" | cut -d: -f2 | cut -d, -f1 | sed 's/^ *//')
        echo "  - $font_name"
    done
else
    echo "未找到WenQuanYi字体"
fi

echo ""

# 搜索Noto字体
echo "=== 搜索Noto字体 ==="
NOTO_FONTS=$(fc-list | grep -i noto | grep -i cjk)
if [ -n "$NOTO_FONTS" ]; then
    echo "找到Noto CJK字体:"
    echo "$NOTO_FONTS" | while read line; do
        font_name=$(echo "$line" | cut -d: -f2 | cut -d, -f1 | sed 's/^ *//')
        echo "  - $font_name"
    done
else
    echo "未找到Noto CJK字体"
fi

echo ""

# 总体评估
echo "=== 总体评估 ==="
if [ $CHINESE_FONTS -gt 0 ]; then
    echo "✓ 系统支持中文字体显示"
    
    # 检查是否有推荐字体
    if fc-list ":family=WenQuanYi Zen Hei" | grep -q "WenQuanYi Zen Hei"; then
        echo "✓ 推荐字体 WenQuanYi Zen Hei 已安装"
    elif fc-list ":family=Noto Sans CJK SC" | grep -q "Noto Sans CJK SC"; then
        echo "✓ 推荐字体 Noto Sans CJK SC 已安装"
    else
        echo "⚠ 建议安装推荐的中文字体"
    fi
else
    echo "✗ 系统不支持中文字体显示"
    echo "建议安装中文字体包:"
    echo "  Ubuntu/Debian: sudo apt install fonts-wqy-zenhei fonts-noto-cjk"
    echo "  CentOS/RHEL: sudo yum install wqy-zenhei-fonts google-noto-sans-cjk-fonts"
    echo "  Fedora: sudo dnf install wqy-zenhei-fonts google-noto-sans-cjk-fonts"
fi

echo ""
echo "检查完成。"
