# 信道扫描分段查看工具 - 重构版本 (Channel Scan Segmentation Viewer - Refactored)

## 重构说明

**这是原有 `chanscan_viewer.py` 的重构版本！**

原有的单体文件包含7502行代码，现已重构为模块化架构，具有以下改进：

### 🚀 重构改进
- **模块化设计**：将7502行代码拆分为多个独立模块
- **可维护性**：每个模块都有单一职责，代码结构清晰
- **可扩展性**：新功能可以轻松添加到相应模块
- **可读性**：完整的注释和文档
- **向后兼容**：保持与原有代码完全一致的功能

### 📁 新的模块结构
```
chanscan_viewer_refactored/
├── main.py                 # 🆕 主程序入口
├── run.py                  # 🆕 快速启动脚本
├── core/                   # 🆕 核心功能模块
├── ui/                     # 🆕 用户界面模块
├── data/                   # 🆕 数据处理模块
├── visualization/          # 🆕 可视化模块
├── channel_scan/           # 🆕 信道扫描模块
├── utils/                  # 🆕 工具模块
└── existing_modules/       # 现有模块（保持不变）
```

### 🎯 使用方法
```bash
# 新的启动方式
python main.py
# 或
python run.py
```

---

## 项目简介

这是一个基于 Python 和 Tkinter 的信号数据分析工具，主要功能包括数据文件可视化、信道扫描和信号分段、分段结果管理、向量数据库操作和USRP数据捕获。该工具基于原有的 `data_viewer` 修改而来，删除了模型推理功能，增加了信道扫描分段功能和数据捕获功能。

## 核心功能

### 1. 数据文件可视化
- **支持格式**: `.bvsp`、`.dat`、`.hdfv` 文件
- **四种图表**: 时域信号点值图、时域信号图、频谱图、时频图
- **布局方式**: 图表从上到下垂直排列（与data_viewer保持一致）
- **视图模式**: 支持 2D/3D 视图切换（3D模式显示2个3D图表）
- **高性能绘图**: 
  - 智能抽样优化大数据文件显示性能（最大10万点显示）
  - 多线程异步计算FFT和时频图，避免界面卡顿
  - 自适应FFT计算策略（直接FFT、智能抽样FFT、分段平均FFT）
  - MATLAB兼容的频谱和时频图计算

### 2. 信道扫描分段
- **自动分段**: 使用 `proc_wbsig` 函数进行信道扫描，自动检测信号段
- **无阈值限制**: 修改了原有的阈值限制，尽可能全面地绘制分出的段
- **分段信息**: 显示每个分段的频率、带宽、持续时间等信息

### 3. 分段结果管理
- **列表显示**: 在分段结果面板中以表格形式显示所有检测到的分段
- **多选支持**: 支持单选和多选分段
- **操作按钮**: 添加选中、删除选中、清空等操作

### 4. 向量数据库管理
- **查看结构**: 查看数据库文件结构，显示所有记录的详细信息
- **添加记录**: 将信道扫描得到的分段添加到数据库，自动递增class_id
- **智能ID管理**: 新添加的数据类别ID从数据库已有最大ID+1开始
- **特征向量更新**: 支持使用新模型重新提取所有数据库记录的特征向量

### 5. USRP数据捕获功能 (新增)
- **实时数据采集**: 支持从USRP设备实时采集信号数据
- **参数配置**: 可配置中心频率、采样率、带宽等参数
- **数据格式**: 采集数据自动保存为hdfv格式，包含完整的元数据信息
- **用户提示**: 
  - 文件夹名称与信号类名一致性提醒
  - 现有数据文件清除确认对话框
  - 采集进度实时显示
- **异常处理**: 自动检测和处理异常短信号数据
- **UI优化**: 采集完成后异步加载数据，避免界面卡顿

## 完整文件结构

### 核心程序文件
```
chanscan_viewer/
├── chanscan_viewer.py              # 主程序文件 (主界面和核心逻辑)
├── core_functions.py               # 核心功能模块 (数据加载、信号处理)
├── adaptive_chart_renderer.py      # 自适应图表渲染器
├── data_capture_controller.py      # 数据捕获控制器 (USRP数据采集)
├── frequency_dialog.py             # 频率输入对话框
├── progress_dialog.py              # 进度显示对话框
├── viewer_config.json              # 配置文件 (用户设置保存)
├── start_chanscan_viewer.bat       # Windows启动脚本
├── start_chanscan_viewer.sh        # Linux/Mac启动脚本
├── install_chinese_fonts.sh        # Linux中文字体安装脚本
└── README.md                       # 说明文档
```

### 依赖的项目模块
```
deepsc_research/
├── chanlib/                        # 信道处理库
│   └── func_scansig.py            # 信道扫描功能
├── usrlib/                         # 用户库
│   ├── usrlib.py                  # 向量数据库操作
│   └── usrp_samp.py               # USRP采样功能
├── utils/                          # 工具库
│   └── dataloader.py              # 数据加载器
├── config/                         # 配置目录
│   ├── DataFiles/                  # 数据文件存储
│   └── database/                   # 数据库文件
└── prj_predict_usrp/
    └── chanscan_viewer/            # 本工具目录
```

## 数据组织结构要求

### 1. 数据捕获目录结构

#### 推荐的数据存储结构
```
datafiles/
├── signal_class_a/             # 信号类别A (文件夹名=信号类名)
│   ├── 00-20250721-180109.hdfv # 采集数据文件 (序号-日期时间.hdfv)
│   ├── 01-20250721-180110.hdfv
│   └── ...
├── signal_class_b/             # 信号类别B
└── ...
```

#### 数据文件命名规范
- **格式**: `{序号}-{日期时间}.hdfv`
- **序号**: 两位数字，从00开始递增 (00, 01, 02, ...)
- **日期时间**: YYYYMMDD-HHMMSS格式
- **示例**: `00-20250721-180109.hdfv`

#### 文件夹命名要求
- **信号类名一致性**: 文件夹名称必须与信号类名保持一致
- **命名规范**: 建议使用下划线分隔的英文名称 (如: `nb_radiolink_t16d`)
- **避免特殊字符**: 不使用空格、中文字符或特殊符号

### 2. 数据库目录结构

#### 向量数据库文件组织
```
config/
├── database/                       # 数据库文件目录
│   ├── arc_vector.db              # 主要的向量数据库文件
│   ├── backup/                    # 数据库备份目录
│   └── temp/                      # 临时数据库文件
├── models/                        # 模型文件目录
│   ├── current_model.pth          # 当前使用的模型
│   └── backup_models/             # 模型备份
└── DataFiles/                     # 数据文件配置目录
    ├── signal_class_a/            # 按信号类别组织的数据文件
    └── signal_class_b/
```

#### 数据库表结构 (v3版本)
向量数据库包含以下主要字段：
- `record_id`: 数据记录编号 (自动递增，从1开始)
- `class_id`: 信号类别标识符 (基于class_def.txt定义)
- `class_name`: 信号类别名称 (对应文件夹名)
- `file_path`: 原始数据文件路径
- `sample_rate`: 采样率 (Hz)
- `bandwidth`: 信号带宽 (Hz)
- `start_sample`: 分段起始采样点
- `end_sample`: 分段结束采样点
- `feature_vector`: 特征向量数据 (640维)

#### 数据库版本兼容性
系统支持多版本数据库格式的自动检测和迁移：
- **v1 (Legacy)**: 6字段格式，不包含样本位置信息
- **v2 (With Samples)**: 8字段格式，包含样本位置但无record_id
- **v3 (Current)**: 9字段格式，包含record_id和完整字段信息

#### 数据库迁移功能
- **自动检测**: 启动时自动检测数据库版本
- **安全迁移**: 迁移前自动创建备份文件
- **数据完整性**: 保持所有原有数据不丢失
- **class_id推断**: 基于文件路径和class_def.txt自动推断正确的类别ID
- **用户提示**: 提供友好的迁移进度显示和结果反馈

### 3. 支持的数据格式

#### 数据文件格式
1. **HDFV格式** (.hdfv) - 推荐格式
   - 包含完整的元数据信息
   - 支持复数信号数据
   - 高效的存储和读取性能

2. **BVSP格式** (.bvsp) - 兼容格式
   - 二进制信号数据格式
   - 需要额外的参数文件

3. **DAT格式** (.dat) - 通用格式
   - 原始二进制数据
   - 需要手动指定数据参数

#### 数据质量要求
- **最小信号长度**: 建议不少于1024个采样点
- **采样率**: 建议15.36 MHz或其他标准采样率
- **数据完整性**: 避免OVERFLOW错误导致的数据丢失
- **元数据完整性**: 包含采样率、中心频率、带宽等关键信息

## 安装和使用

### 必需依赖
```bash
pip install numpy matplotlib scipy tkinter h5py
```

### 可选依赖
```bash
pip install torch  # 用于3D显示功能
pip install cupy   # 用于GPU加速FFT计算
```

### 启动工具

#### Windows系统
```batch
cd prj_predict_usrp/chanscan_viewer
start_chanscan_viewer.bat
```

#### Linux/Mac系统
```bash
cd prj_predict_usrp/chanscan_viewer
chmod +x start_chanscan_viewer.sh
./start_chanscan_viewer.sh
```

### 基本操作流程

1. **加载数据文件**: 选择文件或文件夹加载数据
2. **查看信号分析图表**: 自动显示四种信号分析图表
3. **运行信道扫描**: 点击"信道扫描"按钮开始自动分段
4. **管理分段结果**: 选择分段并添加到向量数据库
5. **数据捕获**: 使用USRP设备实时采集信号数据
6. **查看数据库**: 查看和管理向量数据库内容

## 注意事项

1. **依赖关系**: 确保项目相关模块路径正确
2. **文件格式**: 目前支持 `.bvsp`、`.dat`、`.hdfv` 格式
3. **内存使用**: 大文件可能占用较多内存，建议适当调整采样参数
4. **USRP设备**: 数据捕获功能需要连接USRP设备
5. **数据质量**: 建议定期检查采集数据的质量和完整性

## 最新更新

### 数据库Chunked支持修复 (2025-07-22)

#### 关键问题修复
- ✅ **"Only chunked datasets can be resized" 错误彻底修复**: 解决添加分段时的数据库扩展问题
- ✅ **数据库迁移chunked支持**: migrate_database_structure函数正确设置所有数据集的chunks=True参数
- ✅ **删除操作chunked支持**: DeleteArcVectorRecords函数重建数据库时保持v3格式和chunked支持
- ✅ **特征向量更新完整性**: update_database_vectors函数包含所有字段的resize操作
- ✅ **强制重建工具**: 提供force_rebuild_database.py工具处理现有数据库的chunked问题

#### 数据库操作全面优化
- ✅ **动态调整大小支持**: 所有9个数据集都支持maxshape=(None, ...)和chunks=True
- ✅ **添加分段功能**: 现在可以正常在chanscan_viewer中添加分段到数据库
- ✅ **删除记录功能**: 删除记录后重建的数据库保持完整的v3格式
- ✅ **特征向量更新**: 支持使用新模型重新提取所有数据库记录的特征向量
- ✅ **数据库迁移**: 旧版本数据库迁移后正确支持动态扩展

#### 数据完整性保障
- ✅ **自动备份机制**: 所有数据库修改操作前自动创建带时间戳的备份文件
- ✅ **数据安全验证**: 修改后自动验证数据库结构和记录完整性
- ✅ **错误恢复**: 操作失败时自动恢复备份数据库
- ✅ **向后兼容性**: 完全兼容v1/v2/v3三种数据库格式

#### 测试验证完成
- ✅ **Chunked数据集测试**: 验证所有数据集支持动态调整大小
- ✅ **数据库操作测试**: 验证添加、删除、更新操作正常工作
- ✅ **兼容性测试**: 验证所有相关模块的数据库兼容性
- ✅ **功能完整性测试**: 确认所有核心功能正常工作

### 数据库重构 (2025-07-22)

#### 重大功能更新
- ✅ **数据库结构重构**: 将class_id从数据编号改为真实类别标识符
- ✅ **新增record_id字段**: 专门用作数据记录编号，从1开始自动递增
- ✅ **数据库版本检测**: 自动检测v1/v2/v3三种数据库格式
- ✅ **安全数据迁移**: 自动迁移旧版本数据库，保持数据完整性
- ✅ **class_id智能推断**: 基于文件路径和class_def.txt自动推断正确的类别ID
- ✅ **向后兼容性**: 新代码完全兼容旧版本数据库

#### 用户界面改进
- ✅ **数据库查看界面**: TreeView新增record_id列，区分显示数据编号和类别ID
- ✅ **迁移提示对话框**: 友好的数据库升级提示和进度显示
- ✅ **特征向量更新**: 新增class_id同步更新选项
- ✅ **错误处理优化**: 配置文件问题的智能检测和用户提示

#### 预测模块兼容性
- ✅ **predict_proc.py更新**: 适配9字段数据库返回值
- ✅ **MyHelper.py更新**: 文件依赖检查功能兼容性更新
- ✅ **多版本支持**: 支持9/8/6字段三种数据库格式的无缝切换

#### 数据安全保障
- ✅ **自动备份机制**: 迁移前自动创建带时间戳的备份文件
- ✅ **数据完整性验证**: 迁移后自动验证数据记录数和字段完整性
- ✅ **错误恢复**: 迁移失败时自动恢复原始数据库

### 界面优化 (2025-07-21)

#### 修复的问题
- ✅ 数据采集完成后程序卡顿问题
- ✅ 弹框宽度不能自适应文本内容
- ✅ 频率输入对话框位置不居中
- ✅ 缺少用户操作提示和确认

#### 新增功能
- ✅ 异步数据加载，避免UI阻塞
- ✅ 智能弹框宽度自适应
- ✅ 数据捕获用户提示功能
- ✅ 异常数据检测和处理
- ✅ 采集完成消息路径缩短显示

#### 性能优化
- ✅ 多线程异步处理
- ✅ 智能文本尺寸计算
- ✅ 内存使用优化
- ✅ UI响应性提升
