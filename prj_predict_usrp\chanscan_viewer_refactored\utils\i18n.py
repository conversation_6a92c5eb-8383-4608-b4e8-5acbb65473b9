"""
国际化支持模块

提供多语言文本管理和语言切换功能。
"""


class I18nManager:
    """
    国际化管理器，负责多语言文本的管理和切换
    """
    
    def __init__(self, default_language='zh'):
        self.current_language = default_language
        self.texts_zh = {
            'title': '信道扫描分段查看工具',
            'select_file': '选择文件',
            'select_folder': '选择文件夹',
            'select_model': '选择模型',
            'previous': '上一个',
            'next': '下一个',
            'toggle_view': '切换视图',
            'view_2d': '2D视图',
            'view_3d': '3D视图',
            'language_switch': '中/En',
            'file_list': '文件列表',
            'file_info': '文件信息',
            'segment_results': '分段结果',
            'please_select': '请选择数据文件',
            'warning': '警告',
            'no_files_found': '未找到支持的数据文件！',
            'loading': '正在加载',
            'loaded': '已加载',
            'load_failed': '加载失败',
            'error': '错误',
            'load_file_failed': '加载文件失败',
            'data_capture': '数据捕获',
            'channel_scan': '信道扫描',
            'view_database': '查看数据库',
            'scan_not_available': '请先安装相关依赖包',
            'no_data_loaded': '请先加载数据文件',
            'processing': '正在处理',
            'scan_complete': '扫描完成',
            'scan_failed': '扫描失败',
            'clear_results': '清空',
            'add_to_db': '添加到数据库',
            'skip_file': '跳过文件',
            'select_all': '全选',
            'deselect_all': '全不选',
            'delete_selected': '删除选中',
            'add_segments_to_db_confirm': '这将使用模型处理选中的信号段并添加到数据库，确认吗？',
            'no_segments_selected': '请先选择要添加的信号段',
            'adding_segments_to_db': '正在添加信号段到数据库...',
            'add_to_db_success': '成功添加 {count} 个信号段到数据库',
            'add_to_db_failed': '添加信号段到数据库失败',
            'confirm_delete_segments': '确认删除选中的信号段吗？',
            'segments_deleted': '已删除 {count} 个信号段',
            'model_loading': '正在加载模型...',
            'model_load_failed': '模型加载失败，数据库功能受限',
            'model_load_success': '模型加载成功',
            'no_model_prompt_title': '模型未加载',
            'no_model_prompt_message': '信道扫描功能需要加载模型。\n是否现在选择一个模型文件？',
            'no_model_db_prompt_title': '添加到数据库需要模型',
            'no_model_db_prompt_message': '添加信号段到数据库需要加载模型。\n是否现在选择一个模型文件？',
            'no_model_db_add_prompt_message': '添加到数据库需要加载模型。\n是否现在选择一个模型文件？\n如果加载成功，将自动继续执行添加操作。',
            'selected_segments_count': '选中的分段数: {count}',
            'expand_panel': '展开面板',
            'results_cleared': '分段结果已清空',
            'new_scan_prompt': '点击"信道扫描"开始新的扫描...',
            'ready': '就绪',
            'signal_analysis_charts': '信号分析图表',
            'time_domain_points': '时域信号点值图',
            'time_domain_signal': '时域信号图',
            'frequency_spectrum': '频谱图',
            'time_frequency': '时频图',
            'file_path': '文件路径',
            'file_size': '文件大小',
            'sample_rate': '采样率',
            'center_frequency': '中心频率',
            'signal_length': '信号长度',
            'duration': '持续时间',
            'samples_unit': '个采样点',
            'close': '关闭',
            'segments_added': '分段已添加到数据库',
            'add_failed': '添加失败',
            'no_database_file': '数据库文件不存在',
            'database_structure': '数据库结构',
            'preparing_data': '准备数据',
            'data_sampling_completed': '数据采样完成',
            'points_unit': '点',
            'time_plot_completed_calculating_spectrum': '时域图绘制完成，正在计算频谱',
            'spectrum_completed_calculating_timefreq': '频谱计算完成，正在计算时频图',
            'completing_rendering': '完成渲染',
            'time_domain_range': '时域范围',
            'display_text': '显示',
            'sampling_text': '采样',
            'time_points_unit': '时间点 (×10^5)',
            'signal_voltage': '信号电压',
            'time_ms': '时间(ms)',
            'voltage_v': '电压(V)',
            'frequency_mhz': '频率(MHz)',
            'spectrum_value': '频谱值',
            'time_frame': '时间帧',
            'frequency_frame': '频率帧',
            'power_db': '功率(dB)',
            'power_spectral_density_3d': '功率谱密度图 3D',
            'filtered_psd_3d': '滤波后功率谱密度图 3D',
            'signal_amplitude': '信号幅度',
            'sample_points': '采样点',
            'amplitude': '幅度'
        }
        
        self.texts_en = {
            'title': 'Channel Scan Segmentation Viewer',
            'select_file': 'Select File',
            'select_folder': 'Select Folder',
            'select_model': 'Select Model',
            'previous': 'Previous',
            'next': 'Next',
            'toggle_view': 'Toggle View',
            'view_2d': '2D View',
            'view_3d': '3D View',
            'language_switch': 'En/中',
            'file_list': 'File List',
            'file_info': 'File Information',
            'segment_results': 'Segment Results',
            'please_select': 'Please select a data file',
            'warning': 'Warning',
            'no_files_found': 'No supported data files found!',
            'loading': 'Loading',
            'loaded': 'Loaded',
            'load_failed': 'Load Failed',
            'error': 'Error',
            'load_file_failed': 'Failed to load file',
            'data_capture': 'Data Capture',
            'channel_scan': 'Channel Scan',
            'view_database': 'View Database',
            'scan_not_available': 'Please install dependencies first',
            'no_data_loaded': 'Please load data file first',
            'processing': 'Processing',
            'scan_complete': 'Scan Complete',
            'scan_failed': 'Scan Failed',
            'clear_results': 'Clear',
            'add_to_db': 'Add to Database',
            'skip_file': 'Skip File',
            'select_all': 'Select All',
            'deselect_all': 'Deselect All',
            'delete_selected': 'Delete Selected',
            'add_segments_to_db_confirm': 'This will process selected segments using the model and add them to the database. Continue?',
            'no_segments_selected': 'Please select segments to add first',
            'adding_segments_to_db': 'Adding segments to database...',
            'add_to_db_success': 'Successfully added {count} segments to database',
            'add_to_db_failed': 'Failed to add segments to database',
            'confirm_delete_segments': 'Confirm to delete selected segments?',
            'segments_deleted': 'Deleted {count} segments',
            'model_loading': 'Loading model...',
            'model_load_failed': 'Model loading failed, database functions limited',
            'model_load_success': 'Model loaded successfully',
            'no_model_prompt_title': 'Model Not Loaded',
            'no_model_prompt_message': 'Channel scan requires a model to be loaded.\nDo you want to select a model file now?',
            'no_model_db_prompt_title': 'Model Required for Database',
            'no_model_db_prompt_message': 'Adding segments to database requires a model.\nDo you want to select a model file now?',
            'no_model_db_add_prompt_message': 'Adding to the database requires a model.\nSelect a model file now?\nIf loaded successfully, the operation will continue automatically.',
            'selected_segments_count': 'Selected segments: {count}',
            'expand_panel': 'Expand Panel',
            'results_cleared': 'Segment results cleared',
            'new_scan_prompt': 'Click "Channel Scan" to start new scan...',
            'ready': 'Ready',
            'signal_analysis_charts': 'Signal Analysis Charts',
            'time_domain_points': 'Time Domain Signal Points',
            'time_domain_signal': 'Time Domain Signal',
            'frequency_spectrum': 'Frequency Spectrum',
            'time_frequency': 'Time-Frequency',
            'file_path': 'File Path',
            'file_size': 'File Size',
            'sample_rate': 'Sample Rate',
            'center_frequency': 'Center Frequency',
            'signal_length': 'Signal Length',
            'duration': 'Duration',
            'samples_unit': 'samples',
            'close': 'Close',
            'segments_added': 'Segments added to database',
            'add_failed': 'Addition failed',
            'no_database_file': 'Database file does not exist',
            'database_structure': 'Database Structure',
            'preparing_data': 'Preparing data',
            'data_sampling_completed': 'Data sampling completed',
            'points_unit': 'pts',
            'time_plot_completed_calculating_spectrum': 'Time plot completed, calculating spectrum',
            'spectrum_completed_calculating_timefreq': 'Spectrum completed, calculating time-frequency',
            'completing_rendering': 'Completing rendering',
            'time_domain_range': 'Time domain range',
            'display_text': 'Display',
            'sampling_text': 'Sampling',
            'time_points_unit': 'Time Points (×10^5)',
            'signal_voltage': 'Signal Voltage',
            'time_ms': 'Time (ms)',
            'voltage_v': 'Voltage (V)',
            'frequency_mhz': 'Frequency (MHz)',
            'spectrum_value': 'Spectrum Value',
            'time_frame': 'Time Frame',
            'frequency_frame': 'Frequency Frame',
            'power_db': 'Power (dB)',
            'power_spectral_density_3d': 'Power Spectral Density 3D',
            'filtered_psd_3d': 'Filtered PSD 3D',
            'signal_amplitude': 'Signal Amplitude',
            'sample_points': 'Sample Points',
            'amplitude': 'Amplitude'
        }
        
        # 当前选中的文本字典
        self.texts = self.texts_zh if default_language == 'zh' else self.texts_en
    
    def set_language(self, language):
        """
        设置当前语言
        
        Args:
            language (str): 语言代码，'zh' 或 'en'
        """
        self.current_language = language
        self.texts = self.texts_zh if language == 'zh' else self.texts_en
    
    def get_text(self, key, **kwargs):
        """
        获取指定键的文本
        
        Args:
            key (str): 文本键
            **kwargs: 格式化参数
            
        Returns:
            str: 对应的文本，如果键不存在则返回键本身
        """
        text = self.texts.get(key, key)
        if kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError):
                return text
        return text
    
    def get_all_texts(self):
        """
        获取当前语言的所有文本
        
        Returns:
            dict: 当前语言的文本字典
        """
        return self.texts.copy()
    
    def toggle_language(self):
        """
        切换语言（中文/英文）
        
        Returns:
            str: 切换后的语言代码
        """
        new_language = 'en' if self.current_language == 'zh' else 'zh'
        self.set_language(new_language)
        return new_language
