"""
工具栏模块

负责工具栏的创建和管理，包括按钮布局、样式和事件绑定。
严格保持与原有代码相同的按钮大小、颜色和布局。
"""

import tkinter as tk
from typing import Dict, Callable, Optional, Any


class Toolbar:
    """
    工具栏管理器
    
    负责工具栏的创建和管理，保持与原有代码完全一致的按钮配置。
    """
    
    def __init__(self, parent: tk.Widget, style_manager, i18n_manager):
        """
        初始化工具栏
        
        Args:
            parent: 父容器
            style_manager: 样式管理器
            i18n_manager: 国际化管理器
        """
        self.parent = parent
        self.style_manager = style_manager
        self.i18n_manager = i18n_manager
        self.toolbar_frame = None
        self.view_button = None
        
        # 按钮回调函数字典
        self.callbacks = {}
        
        # 创建工具栏
        self.create_toolbar()

    def pack_toolbar(self):
        """打包工具栏（确保在顶部显示）"""
        if self.toolbar_frame:
            # 使用正常的工具栏样式
            self.toolbar_frame.config(bg='SystemButtonFace', height=40, relief=tk.RAISED, bd=1)

            # 先unpack确保没有冲突，然后重新pack
            self.toolbar_frame.pack_forget()
            self.toolbar_frame.pack(fill=tk.X, side=tk.TOP, pady=(0, 5))
            self.toolbar_frame.pack_propagate(False)  # 防止子组件改变框架大小

            # 强制更新布局
            self.toolbar_frame.update_idletasks()

            # 确保工具栏在最前面
            self.toolbar_frame.lift()

    def create_toolbar(self):
        """
        创建工具栏

        保持与原有代码完全一致的工具栏布局和按钮配置
        """
        self.toolbar_frame = tk.Frame(self.parent, relief=tk.RAISED, bd=1)
        # 设置最小尺寸确保工具栏可见
        self.toolbar_frame.config(height=40)
        # 不立即pack，等待显式调用pack_toolbar
        
        # 根据语言设置按钮宽度
        current_language = self.i18n_manager.current_language
        if current_language == 'zh':
            btn_widths = {'file': 10, 'model': 10, 'nav': 8, 'func': 12, 'view': 8}
        else:
            btn_widths = {'file': 12, 'model': 12, 'nav': 9, 'func': 15, 'view': 9}
        
        # 获取字体
        default_font = self.style_manager.get_font('default')
        
        # 获取文本
        texts = self.i18n_manager.get_all_texts()
        
        # 左侧按钮组
        left_frame = tk.Frame(self.toolbar_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # 文件操作按钮
        self._create_button(
            left_frame, texts['select_file'], 'select_file',
            default_font, btn_widths['file'], side=tk.LEFT, padx=2
        )
        
        self._create_button(
            left_frame, texts['select_folder'], 'select_folder',
            default_font, btn_widths['file'], side=tk.LEFT, padx=2
        )
        
        # 模型选择按钮
        self._create_button(
            left_frame, texts['select_model'], 'select_and_load_model',
            default_font, btn_widths['model'], side=tk.LEFT, padx=5
        )
        
        # 导航按钮
        self._create_button(
            left_frame, texts['previous'], 'prev_file',
            default_font, btn_widths['nav'], side=tk.LEFT, padx=2
        )
        
        self._create_button(
            left_frame, texts['next'], 'next_file',
            default_font, btn_widths['nav'], side=tk.LEFT, padx=2
        )
        
        # 中间按钮组
        middle_frame = tk.Frame(self.toolbar_frame)
        middle_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=2)
        
        # 数据捕获按钮
        self._create_button(
            middle_frame, texts['data_capture'], 'capture_data',
            default_font, btn_widths['func'], side=tk.LEFT, padx=2,
            bg='#2196F3', fg='white'
        )
        
        # 信道扫描按钮
        self._create_button(
            middle_frame, texts['channel_scan'], 'run_channel_scan',
            default_font, btn_widths['func'], side=tk.LEFT, padx=2,
            bg='#4CAF50', fg='white'
        )
        
        # 查看数据库按钮
        self._create_button(
            middle_frame, texts['view_database'], 'view_database',
            default_font, btn_widths['func'], side=tk.LEFT, padx=2
        )
        
        # 右侧按钮组
        right_frame = tk.Frame(self.toolbar_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=2)
        
        # 视图切换按钮
        self.view_button = tk.Button(
            right_frame, text=texts['view_2d'], 
            command=self._get_callback('toggle_view_mode'),
            font=default_font, width=btn_widths['view']
        )
        self.view_button.pack(side=tk.RIGHT, padx=2)
        
        # 语言切换按钮
        self._create_button(
            right_frame, texts['language_switch'], 'toggle_language',
            default_font, btn_widths['view'], side=tk.RIGHT, padx=2
        )
    
    def _create_button(self, parent: tk.Widget, text: str, callback_name: str,
                      font, width: int, side: str = tk.LEFT, padx: int = 2,
                      bg: Optional[str] = None, fg: Optional[str] = None):
        """
        创建按钮的辅助方法
        
        Args:
            parent: 父容器
            text: 按钮文本
            callback_name: 回调函数名称
            font: 字体
            width: 按钮宽度
            side: 布局方向
            padx: 水平间距
            bg: 背景色
            fg: 前景色
        """
        button_kwargs = {
            'text': text,
            'command': self._get_callback(callback_name),
            'font': font,
            'width': width
        }
        
        if bg:
            button_kwargs['bg'] = bg
        if fg:
            button_kwargs['fg'] = fg
        
        button = tk.Button(parent, **button_kwargs)
        button.pack(side=side, padx=padx)

        return button
    
    def _get_callback(self, callback_name: str) -> Callable:
        """
        获取回调函数
        
        Args:
            callback_name: 回调函数名称
            
        Returns:
            回调函数
        """
        return self.callbacks.get(callback_name, lambda: None)
    
    def set_callback(self, callback_name: str, callback: Callable):
        """
        设置回调函数
        
        Args:
            callback_name: 回调函数名称
            callback: 回调函数
        """
        self.callbacks[callback_name] = callback
    
    def set_callbacks(self, callbacks: Dict[str, Callable]):
        """
        批量设置回调函数
        
        Args:
            callbacks: 回调函数字典
        """
        self.callbacks.update(callbacks)
    
    def update_texts(self):
        """更新工具栏文本（用于语言切换）"""
        # 销毁现有工具栏
        if self.toolbar_frame:
            self.toolbar_frame.destroy()
        
        # 重新创建工具栏
        self.create_toolbar()
    
    def update_view_button_text(self, text: str):
        """
        更新视图切换按钮文本
        
        Args:
            text: 新的按钮文本
        """
        if self.view_button:
            self.view_button.config(text=text)
    
    def get_toolbar_frame(self) -> tk.Frame:
        """
        获取工具栏框架
        
        Returns:
            工具栏框架
        """
        return self.toolbar_frame
    
    def destroy(self):
        """销毁工具栏"""
        if self.toolbar_frame:
            self.toolbar_frame.destroy()
            self.toolbar_frame = None
    
    def pack(self, **kwargs):
        """打包工具栏"""
        if self.toolbar_frame:
            self.toolbar_frame.pack(**kwargs)
    
    def pack_forget(self):
        """取消打包工具栏"""
        if self.toolbar_frame:
            self.toolbar_frame.pack_forget()
    
    def configure(self, **kwargs):
        """配置工具栏"""
        if self.toolbar_frame:
            self.toolbar_frame.configure(**kwargs)
    
    def get_button_widths(self) -> Dict[str, int]:
        """
        获取按钮宽度配置
        
        Returns:
            按钮宽度字典
        """
        current_language = self.i18n_manager.current_language
        if current_language == 'zh':
            return {'file': 10, 'model': 10, 'nav': 8, 'func': 12, 'view': 8}
        else:
            return {'file': 12, 'model': 12, 'nav': 9, 'func': 15, 'view': 9}
    
    def enable_button(self, callback_name: str):
        """
        启用指定按钮
        
        Args:
            callback_name: 回调函数名称
        """
        # 这里可以添加按钮启用逻辑
        # 由于原代码中没有明确的按钮引用管理，这里提供接口
        pass
    
    def disable_button(self, callback_name: str):
        """
        禁用指定按钮
        
        Args:
            callback_name: 回调函数名称
        """
        # 这里可以添加按钮禁用逻辑
        # 由于原代码中没有明确的按钮引用管理，这里提供接口
        pass
