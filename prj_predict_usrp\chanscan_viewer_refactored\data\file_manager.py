"""
文件管理模块

负责文件的加载、保存、验证和管理，包括支持的文件格式处理和文件信息获取。
保持所有原有的文件读取优化。
"""

import os
import sys
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass

# 添加项目路径以导入core_functions
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from core_functions import load_signal_data
    CORE_FUNCTIONS_AVAILABLE = True
except ImportError as e:
    CORE_FUNCTIONS_AVAILABLE = False
    print(f"警告: core_functions不可用，文件加载功能受限: {e}")


@dataclass
class FileInfo:
    """文件信息数据类"""
    path: str
    size: int
    format: str
    sample_rate: Optional[float] = None
    center_frequency: Optional[float] = None
    duration: Optional[float] = None
    signal_length: Optional[int] = None
    
    def get_size_mb(self) -> float:
        """获取文件大小（MB）"""
        return self.size / (1024 * 1024)


class FileManager:
    """
    文件管理器
    
    负责文件的加载、验证和管理。保持与原有文件处理逻辑完全一致，
    包括所有的文件读取优化。
    """
    
    # 支持的文件格式
    SUPPORTED_EXTENSIONS = ['.bvsp', '.dat', '.hdfv']
    
    # 文件类型定义
    FILE_TYPES = [
        ('信号数据文件', '*.bvsp *.dat *.hdfv'),
        ('BVSP文件', '*.bvsp'),
        ('DAT文件', '*.dat'),
        ('HDF5文件', '*.hdfv'),
        ('所有文件', '*.*')
    ]
    
    MODEL_FILE_TYPES = [
        ("PyTorch模型", "*.pth *.pt"),
        ("所有文件", "*.*")
    ]
    
    IMAGE_FILE_TYPES = [
        ("PNG files", "*.png"),
        ("PDF files", "*.pdf"),
        ("All files", "*.*")
    ]
    
    TEXT_FILE_TYPES = [
        ("文本文件", "*.txt"),
        ("所有文件", "*.*")
    ]
    
    def __init__(self):
        """初始化文件管理器"""
        self.current_files: List[str] = []
        self.current_index: int = 0
        self.current_signal_data = None
        self.current_metadata = None
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式
        
        Returns:
            支持的文件扩展名列表
        """
        return self.SUPPORTED_EXTENSIONS.copy()
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为支持的格式
        """
        return Path(file_path).suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def find_files_in_folder(self, folder_path: str, recursive: bool = True) -> List[str]:
        """
        在文件夹中查找支持的文件
        
        Args:
            folder_path: 文件夹路径
            recursive: 是否递归搜索子文件夹
            
        Returns:
            找到的文件路径列表
        """
        folder = Path(folder_path)
        if not folder.exists() or not folder.is_dir():
            return []
        
        files = []
        for ext in self.SUPPORTED_EXTENSIONS:
            pattern = f"*{ext}"
            if recursive:
                files.extend(folder.rglob(pattern))
            else:
                files.extend(folder.glob(pattern))
        
        # 去重并转换为字符串路径
        unique_files = list(set([str(f) for f in files]))
        return sorted(unique_files)
    
    def load_file(self, file_path: str) -> Tuple[bool, Optional[Any], Optional[Dict], Optional[str]]:
        """
        加载文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (成功标志, 信号数据, 元数据, 错误信息)
        """
        if not CORE_FUNCTIONS_AVAILABLE:
            return False, None, None, "core_functions不可用"
        
        if not os.path.exists(file_path):
            return False, None, None, f"文件不存在: {file_path}"
        
        if not self.is_supported_file(file_path):
            return False, None, None, f"不支持的文件格式: {Path(file_path).suffix}"
        
        try:
            print(f"文件路径: {file_path}")
            print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")
            
            success, signal_data, metadata, error_msg = load_signal_data(file_path)
            
            if success:
                # 验证加载的数据
                if signal_data is None:
                    return False, None, None, "信号数据为空"
                if len(signal_data) == 0:
                    return False, None, None, "信号数据长度为0"
                
                # 检查信号长度是否异常（小于10个点）
                if len(signal_data) < 10:
                    return False, None, None, f"信号数据过短: {len(signal_data)} 个采样点"
                
                # 保存当前数据
                self.current_signal_data = signal_data
                self.current_metadata = metadata
                
                return True, signal_data, metadata, None
            else:
                return False, None, None, error_msg or "文件加载失败"
                
        except Exception as e:
            error_msg = f"加载文件时发生异常: {str(e)}"
            print(f"\n=== 文件加载错误详情 ===")
            print(f"文件: {file_path}")
            print(f"错误: {error_msg}")
            print("=" * 50)
            return False, None, None, error_msg
    
    def get_file_info(self, file_path: str) -> FileInfo:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息对象
        """
        if not os.path.exists(file_path):
            return FileInfo(
                path=file_path,
                size=0,
                format="未知",
            )
        
        file_size = os.path.getsize(file_path)
        file_format = Path(file_path).suffix.upper().lstrip('.')
        
        # 尝试获取更详细的信息
        sample_rate = None
        center_frequency = None
        duration = None
        signal_length = None
        
        if self.current_metadata and self.current_signal_data is not None:
            sample_rate = self.current_metadata.get('fs')
            center_frequency = self.current_metadata.get('fc')
            signal_length = len(self.current_signal_data)
            if sample_rate and signal_length:
                duration = signal_length / sample_rate
        
        return FileInfo(
            path=file_path,
            size=file_size,
            format=file_format,
            sample_rate=sample_rate,
            center_frequency=center_frequency,
            duration=duration,
            signal_length=signal_length
        )
    
    def set_current_files(self, files: List[str], index: int = 0):
        """
        设置当前文件列表
        
        Args:
            files: 文件路径列表
            index: 当前文件索引
        """
        self.current_files = files
        self.current_index = max(0, min(index, len(files) - 1)) if files else 0
    
    def get_current_file(self) -> Optional[str]:
        """
        获取当前文件路径
        
        Returns:
            当前文件路径，如果没有则返回None
        """
        if not self.current_files or self.current_index >= len(self.current_files):
            return None
        return self.current_files[self.current_index]
    
    def has_next_file(self) -> bool:
        """检查是否有下一个文件"""
        return self.current_index < len(self.current_files) - 1
    
    def has_previous_file(self) -> bool:
        """检查是否有上一个文件"""
        return self.current_index > 0
    
    def next_file(self) -> Optional[str]:
        """
        切换到下一个文件
        
        Returns:
            下一个文件路径，如果没有则返回None
        """
        if self.has_next_file():
            self.current_index += 1
            return self.get_current_file()
        return None
    
    def previous_file(self) -> Optional[str]:
        """
        切换到上一个文件
        
        Returns:
            上一个文件路径，如果没有则返回None
        """
        if self.has_previous_file():
            self.current_index -= 1
            return self.get_current_file()
        return None
    
    def validate_folder(self, folder_path: str) -> Tuple[bool, str]:
        """
        验证文件夹是否适合用于数据捕获
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            (是否有效, 验证信息)
        """
        if not os.path.exists(folder_path):
            return True, "文件夹不存在，将创建新文件夹"
        
        if not os.path.isdir(folder_path):
            return False, "路径不是一个文件夹"
        
        # 检查文件夹是否为空
        try:
            files = list(os.listdir(folder_path))
            if files:
                return False, f"文件夹不为空，包含 {len(files)} 个项目"
            else:
                return True, "文件夹为空，可以使用"
        except PermissionError:
            return False, "没有访问文件夹的权限"
        except Exception as e:
            return False, f"检查文件夹时出错: {str(e)}"
