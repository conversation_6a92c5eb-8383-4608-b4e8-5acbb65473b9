#!/usr/bin/env python3
"""
测试额外修复：数据库查看窗口功能按钮和绘图显示一致性
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_database_window_functionality():
    """测试数据库查看窗口功能"""
    print("测试数据库查看窗口功能...")
    
    try:
        with open('data/database_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据库窗口是否包含所有必要的功能按钮
        required_features = [
            'view_selected_analysis',  # 查看分段按钮
            'delete_selected_records',  # 删除选中按钮
            'update_vectors',  # 更新特征向量按钮
            'on_tree_click',  # 点击事件处理
            'db_selection_state',  # 选择状态管理
            'current_selected_item'  # 当前选中项
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)
        
        if missing_features:
            print(f"❌ 数据库窗口缺少功能: {missing_features}")
            return False
        else:
            print("✅ 数据库窗口包含所有必要功能")
        
        # 检查按钮文本和样式
        button_checks = [
            '"查看分段"',  # 查看分段按钮
            '"删除选中"',  # 删除按钮
            '"更新特征向量"',  # 更新按钮
            'bg=\'#ccffcc\'',  # 查看按钮颜色
            'bg=\'#ffcccc\'',  # 删除按钮颜色
            'bg=\'#ffffcc\''   # 更新按钮颜色
        ]
        
        for check in button_checks:
            if check in content:
                print(f"✅ 找到按钮配置: {check}")
            else:
                print(f"❌ 缺少按钮配置: {check}")
                return False
        
        # 检查窗口大小设置（1.5倍放大）
        if 'int(1000 * 1.5)' in content and 'int(700 * 1.5)' in content:
            print("✅ 数据库窗口大小设置正确（1.5倍放大）")
        else:
            print("❌ 数据库窗口大小设置不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库窗口功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_rendering_consistency():
    """测试绘图渲染一致性"""
    print("\n测试绘图渲染一致性...")
    
    try:
        with open('visualization/plot_renderer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查时域信号点值图的修复
        time_domain_points_checks = [
            'rasterized=True',  # 光栅化设置
            "linewidth=0.5, rasterized=True",  # 正确的线宽和光栅化
        ]
        
        for check in time_domain_points_checks:
            if check in content:
                print(f"✅ 时域信号点值图配置正确: {check}")
            else:
                print(f"❌ 时域信号点值图配置错误: {check}")
                return False
        
        # 检查时域信号图的修复（应该显示I和Q分量，不是幅度）
        time_domain_signal_checks = [
            'signal_i = np.real(sampled_signal)',  # I分量提取
            'signal_q = np.imag(sampled_signal)',  # Q分量提取
            "ax.plot(time_ms, signal_i, 'b-', linewidth=0.5)",  # I分量绘制
            "ax.plot(time_ms, signal_q, 'r-', linewidth=0.5)",  # Q分量绘制
            "texts['voltage_v']"  # 正确的Y轴标签
        ]
        
        for check in time_domain_signal_checks:
            if check in content:
                print(f"✅ 时域信号图配置正确: {check}")
            else:
                print(f"❌ 时域信号图配置错误: {check}")
                return False
        
        # 检查时频图的修复（不应该有颜色条）
        time_frequency_checks = [
            'ax.imshow(Pxx_db',  # 使用imshow而不是pcolormesh
            'origin=\'lower\'',  # 正确的原点设置
            'interpolation=\'bilinear\'',  # 双线性插值
        ]
        
        for check in time_frequency_checks:
            if check in content:
                print(f"✅ 时频图配置正确: {check}")
            else:
                print(f"❌ 时频图配置错误: {check}")
                return False
        
        # 检查是否移除了颜色条
        if 'plt.colorbar(im, ax=ax' not in content:
            print("✅ 时频图正确移除了颜色条")
        else:
            print("❌ 时频图仍然包含颜色条")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 绘图渲染一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_keys_completeness():
    """测试文本键完整性"""
    print("\n测试文本键完整性...")
    
    try:
        with open('utils/i18n.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查绘图相关的文本键
        required_text_keys = [
            'time_domain_points',  # 时域信号点值图
            'time_domain_signal',  # 时域信号图
            'frequency_spectrum',  # 频谱图
            'time_frequency',  # 时频图
            'time_points_unit',  # 时间点单位
            'time_ms',  # 时间(ms)
            'voltage_v',  # 电压(V)
            'frequency_mhz',  # 频率(MHz)
            'spectrum_value',  # 频谱值
            'signal_voltage'  # 信号电压
        ]
        
        missing_keys = []
        for key in required_text_keys:
            if f"'{key}':" not in content:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 缺少文本键: {missing_keys}")
            return False
        else:
            print("✅ 所有绘图相关文本键都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本键完整性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_database_integration():
    """测试应用程序数据库集成"""
    print("\n测试应用程序数据库集成...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查数据库查看方法
        if 'def view_database(self):' in content:
            print("✅ 找到view_database方法")
        else:
            print("❌ 未找到view_database方法")
            return False
        
        # 检查数据库管理器调用
        if 'self.database_manager.view_database_structure(' in content:
            print("✅ 正确调用数据库管理器")
        else:
            print("❌ 数据库管理器调用不正确")
            return False
        
        # 检查错误处理
        if 'ScaledMessageBox.showerror(' in content:
            print("✅ 包含错误处理")
        else:
            print("❌ 缺少错误处理")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 应用程序数据库集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试额外修复：数据库查看窗口功能按钮和绘图显示一致性...")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        test_database_window_functionality,
        test_plot_rendering_consistency,
        test_text_keys_completeness,
        test_application_database_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 80)
    print(f"额外修复测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有额外修复测试通过！")
        print("\n✅ 修复完成的功能：")
        print("  - 数据库查看窗口包含所有功能按钮（查看分段、删除选中、更新特征向量）")
        print("  - 时域信号点值图不显示图例，使用光栅化")
        print("  - 时域信号图显示I和Q分量，使用正确的电压单位")
        print("  - 时频图不显示右侧颜色条，与原版本一致")
        print("  - 所有绘图相关文本键完整")
        print("  - 应用程序正确集成数据库功能")
        print("\n🎯 重构版本现在与原版本功能和显示完全一致！")
        return True
    else:
        print("❌ 部分额外修复测试失败，需要进一步修复")
        failed_count = total - passed
        print(f"   失败的测试数量: {failed_count}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
