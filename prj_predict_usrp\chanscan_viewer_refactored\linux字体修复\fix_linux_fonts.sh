#!/bin/bash
# -*- coding: utf-8 -*-
"""
Linux字体问题修复脚本
解决chanscan_viewer在Linux下的字体显示问题
"""

set -e  # 遇到错误时退出

echo "========================================"
echo "Linux字体问题修复脚本"
echo "========================================"

# 检测操作系统
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "此脚本仅适用于Linux系统"
    exit 1
fi

# 获取Linux发行版信息
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "检测到操作系统: $OS $VER"
echo ""

# 检查当前字体状态
echo "检查当前字体状态..."
python3 -c "
import platform
import sys

if platform.system() == 'Linux':
    try:
        import tkinter as tk
        import tkinter.font as tkFont

        # 创建临时根窗口以避免字体检查错误
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        available_fonts = list(tkFont.families())
        chinese_fonts = [
            'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN',
            'Liberation Sans', 'DejaVu Sans'
        ]

        found_fonts = [font for font in chinese_fonts if font in available_fonts]

        # 销毁临时窗口
        root.destroy()

        if found_fonts:
            print(f'✓ 已安装的可用字体: {found_fonts}')
            sys.exit(0)
        else:
            print('✗ 未找到合适的字体')
            sys.exit(1)
    except Exception as e:
        print(f'字体检查失败: {e}')
        sys.exit(1)
else:
    print('非Linux系统，无需修复')
    sys.exit(0)
"

FONT_CHECK_RESULT=$?

if [ $FONT_CHECK_RESULT -eq 0 ]; then
    echo "字体已正确配置，无需修复。"
    echo ""
    echo "如果仍然遇到字体问题，请尝试："
    echo "1. 重启应用程序"
    echo "2. 运行: fc-cache -fv"
    echo "3. 检查DISPLAY环境变量设置"
    exit 0
fi

echo ""
echo "需要安装或修复字体配置..."
echo ""

# 根据不同的Linux发行版安装字体
case "$OS" in
    *Ubuntu*|*Debian*)
        echo "检测到 Ubuntu/Debian 系统"
        echo "正在安装字体包..."
        
        # 更新包列表
        echo "更新包列表..."
        sudo apt update
        
        # 安装基础字体包
        echo "安装基础字体包..."
        sudo apt install -y fonts-liberation fonts-dejavu-core
        
        # 安装中文字体包
        echo "安装中文字体包..."
        sudo apt install -y fonts-wqy-zenhei fonts-wqy-microhei
        
        # 尝试安装Noto字体（可能在某些版本中不可用）
        echo "尝试安装 Noto CJK 字体..."
        sudo apt install -y fonts-noto-cjk || echo "Noto CJK字体安装失败，继续..."
        
        # 安装Droid字体作为备选
        echo "安装 Droid 字体..."
        sudo apt install -y fonts-droid-fallback || echo "Droid字体安装失败，继续..."
        ;;
        
    *CentOS*|*Red\ Hat*|*RHEL*)
        echo "检测到 CentOS/RHEL 系统"
        echo "正在安装字体包..."
        
        # 安装基础字体
        sudo yum install -y liberation-fonts dejavu-sans-fonts
        
        # 安装中文字体
        sudo yum install -y wqy-zenhei-fonts wqy-microhei-fonts
        
        # 尝试安装Google Noto字体
        sudo yum install -y google-noto-sans-cjk-fonts || echo "Noto CJK字体安装失败，继续..."
        ;;
        
    *Fedora*)
        echo "检测到 Fedora 系统"
        echo "正在安装字体包..."
        
        # 安装基础字体
        sudo dnf install -y liberation-fonts dejavu-sans-fonts
        
        # 安装中文字体
        sudo dnf install -y wqy-zenhei-fonts wqy-microhei-fonts
        
        # 安装Google Noto字体
        sudo dnf install -y google-noto-sans-cjk-fonts || echo "Noto CJK字体安装失败，继续..."
        ;;
        
    *Arch*)
        echo "检测到 Arch Linux 系统"
        echo "正在安装字体包..."
        
        # 安装基础字体
        sudo pacman -S --noconfirm ttf-liberation ttf-dejavu
        
        # 安装中文字体
        sudo pacman -S --noconfirm wqy-zenhei wqy-microhei
        
        # 安装Noto字体
        sudo pacman -S --noconfirm noto-fonts-cjk || echo "Noto CJK字体安装失败，继续..."
        ;;
        
    *)
        echo "未识别的Linux发行版: $OS"
        echo "请手动安装以下字体包："
        echo "  - Liberation Fonts (liberation-fonts)"
        echo "  - DejaVu Fonts (dejavu-fonts)"
        echo "  - WenQuanYi Zen Hei (wqy-zenhei-fonts)"
        echo "  - WenQuanYi Micro Hei (wqy-microhei-fonts)"
        echo ""
        echo "常用命令参考："
        echo "  Ubuntu/Debian: sudo apt install fonts-liberation fonts-wqy-zenhei"
        echo "  CentOS/RHEL: sudo yum install liberation-fonts wqy-zenhei-fonts"
        echo "  Fedora: sudo dnf install liberation-fonts wqy-zenhei-fonts"
        echo "  Arch: sudo pacman -S ttf-liberation wqy-zenhei"
        exit 1
        ;;
esac

# 刷新字体缓存
echo ""
echo "刷新字体缓存..."
fc-cache -fv

# 验证字体安装
echo ""
echo "验证字体安装..."

python3 -c "
import platform
import sys

if platform.system() == 'Linux':
    try:
        import tkinter as tk
        import tkinter.font as tkFont

        # 创建临时根窗口以避免字体检查错误
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        available_fonts = list(tkFont.families())
        chinese_fonts = [
            'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN',
            'Liberation Sans', 'DejaVu Sans'
        ]

        found_fonts = [font for font in chinese_fonts if font in available_fonts]

        # 销毁临时窗口
        root.destroy()

        if found_fonts:
            print(f'✓ 字体修复成功！可用字体: {found_fonts}')
            print('建议重启应用程序以确保字体生效。')
            sys.exit(0)
        else:
            print('✗ 字体修复失败，未找到合适的字体')
            print('请检查字体安装是否成功，或手动安装字体包。')
            sys.exit(1)
    except Exception as e:
        print(f'字体验证失败: {e}')
        sys.exit(1)
"

VERIFY_RESULT=$?

echo ""
if [ $VERIFY_RESULT -eq 0 ]; then
    echo "========================================"
    echo "字体修复完成！"
    echo "========================================"
    echo ""
    echo "下一步："
    echo "1. 重启 chanscan_viewer 应用程序"
    echo "2. 如果仍有问题，请重启终端或重新登录"
    echo "3. 运行测试脚本验证: python3 test_fonts_linux.py"
else
    echo "========================================"
    echo "字体修复失败"
    echo "========================================"
    echo ""
    echo "请尝试："
    echo "1. 手动安装字体包"
    echo "2. 检查网络连接"
    echo "3. 检查包管理器权限"
    echo "4. 联系系统管理员"
fi

exit $VERIFY_RESULT
