#!/usr/bin/env python3
"""
分页文件列表管理器测试脚本

验证分页逻辑是否正确：
1. 小于100个文件时返回全部文件
2. 大于100个文件时正确分页
3. 缓存机制工作正常
"""

import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'chanscan_viewer'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'chanscan_viewer_refactored', 'utils'))

try:
    from paginated_file_list import PaginatedFileListManager
    print("✅ 成功导入原版PaginatedFileListManager")
except ImportError as e:
    print(f"❌ 导入原版失败: {e}")

try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'chanscan_viewer_refactored', 'utils'))
    from paginated_file_list import PaginatedFileListManager as RefactoredPaginatedFileListManager
    print("✅ 成功导入重构版PaginatedFileListManager")
except ImportError as e:
    print(f"❌ 导入重构版失败: {e}")


def test_small_file_list():
    """测试小文件列表（<100个文件）"""
    print("\n=== 测试小文件列表（50个文件）===")
    
    # 创建50个测试文件
    test_files = [f"file_{i:03d}.txt" for i in range(50)]
    
    manager = PaginatedFileListManager()
    manager.set_files(test_files)
    
    # 验证不启用分页
    assert not manager.is_pagination_enabled(), "小文件列表不应启用分页"
    print("✅ 小文件列表正确禁用分页")
    
    # 验证返回全部文件
    current_files = manager.get_current_page_files()
    assert len(current_files) == 50, f"应返回50个文件，实际返回{len(current_files)}个"
    assert current_files == test_files, "应返回完整的文件列表"
    print("✅ 小文件列表正确返回全部文件")
    
    # 验证分页信息
    page_info = manager.get_page_info()
    assert page_info == (1, 1, 1, 50), f"分页信息错误: {page_info}"
    print("✅ 小文件列表分页信息正确")


def test_large_file_list():
    """测试大文件列表（>100个文件）"""
    print("\n=== 测试大文件列表（250个文件）===")
    
    # 创建250个测试文件
    test_files = [f"file_{i:03d}.txt" for i in range(250)]
    
    manager = PaginatedFileListManager()
    manager.set_files(test_files)
    
    # 验证启用分页
    assert manager.is_pagination_enabled(), "大文件列表应启用分页"
    print("✅ 大文件列表正确启用分页")
    
    # 验证总页数
    assert manager.get_total_pages() == 3, f"应有3页，实际{manager.get_total_pages()}页"
    print("✅ 总页数计算正确")
    
    # 验证第一页
    page1_files = manager.get_current_page_files()
    assert len(page1_files) == 100, f"第一页应有100个文件，实际{len(page1_files)}个"
    assert page1_files == test_files[0:100], "第一页文件内容错误"
    print("✅ 第一页内容正确")
    
    # 验证分页信息
    page_info = manager.get_page_info()
    assert page_info == (1, 3, 1, 100), f"第一页分页信息错误: {page_info}"
    print("✅ 第一页分页信息正确")
    
    # 切换到第二页
    success = manager.next_page()
    assert success, "切换到第二页应该成功"
    
    page2_files = manager.get_current_page_files()
    assert len(page2_files) == 100, f"第二页应有100个文件，实际{len(page2_files)}个"
    assert page2_files == test_files[100:200], "第二页文件内容错误"
    print("✅ 第二页内容正确")
    
    # 切换到第三页
    success = manager.next_page()
    assert success, "切换到第三页应该成功"
    
    page3_files = manager.get_current_page_files()
    assert len(page3_files) == 50, f"第三页应有50个文件，实际{len(page3_files)}个"
    assert page3_files == test_files[200:250], "第三页文件内容错误"
    print("✅ 第三页内容正确")
    
    # 验证最后一页分页信息
    page_info = manager.get_page_info()
    assert page_info == (3, 3, 201, 250), f"第三页分页信息错误: {page_info}"
    print("✅ 第三页分页信息正确")
    
    # 测试边界条件
    success = manager.next_page()
    assert not success, "超出最后一页应该失败"
    print("✅ 边界条件测试通过")


def test_cache_mechanism():
    """测试缓存机制"""
    print("\n=== 测试缓存机制 ===")
    
    # 创建500个测试文件
    test_files = [f"file_{i:03d}.txt" for i in range(500)]
    
    manager = PaginatedFileListManager()
    manager.set_files(test_files)
    
    # 访问多个页面以测试缓存
    for page in range(5):
        manager.goto_page(page)
        files = manager.get_current_page_files()
        assert len(files) == 100, f"页面{page}文件数量错误"
    
    # 检查缓存信息
    cache_info = manager.get_cache_info()
    assert cache_info[0] <= cache_info[1], "缓存页面数不应超过最大值"
    assert cache_info[1] == 3, "最大缓存页面数应为3"
    print(f"✅ 缓存机制正常工作: {cache_info[0]}/{cache_info[1]} 页面已缓存")


def test_file_search():
    """测试文件查找功能"""
    print("\n=== 测试文件查找功能 ===")
    
    # 创建250个测试文件
    test_files = [f"file_{i:03d}.txt" for i in range(250)]
    
    manager = PaginatedFileListManager()
    manager.set_files(test_files)
    
    # 测试查找不同页面的文件
    test_cases = [
        ("file_050.txt", 0),  # 第一页
        ("file_150.txt", 1),  # 第二页
        ("file_220.txt", 2),  # 第三页
    ]
    
    for file_name, expected_page in test_cases:
        found_page = manager.find_file_page(file_name)
        assert found_page == expected_page, f"文件{file_name}应在第{expected_page}页，实际在第{found_page}页"
        print(f"✅ 文件{file_name}正确定位到第{found_page}页")
    
    # 测试查找不存在的文件
    not_found = manager.find_file_page("nonexistent.txt")
    assert not_found is None, "不存在的文件应返回None"
    print("✅ 不存在文件的查找正确返回None")


def main():
    """主测试函数"""
    print("开始测试分页文件列表管理器...")
    
    try:
        test_small_file_list()
        test_large_file_list()
        test_cache_mechanism()
        test_file_search()
        
        print("\n🎉 所有测试通过！分页逻辑工作正常。")
        return True
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
