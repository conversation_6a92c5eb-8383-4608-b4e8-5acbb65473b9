#!/usr/bin/env python3
"""
测试文件选择和绘图更新机制修复是否生效
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_file_manager_methods():
    """测试FileManager的方法"""
    print("测试FileManager方法...")
    
    try:
        from data.file_manager import FileManager
        
        # 创建FileManager实例
        file_manager = FileManager()
        
        # 测试基本方法
        print("✅ FileManager导入成功")
        
        # 测试文件列表设置
        test_files = [
            "/path/to/file1.bvsp",
            "/path/to/file2.dat", 
            "/path/to/file3.hdfv"
        ]
        
        file_manager.set_current_files(test_files, 0)
        print(f"设置文件列表: {len(test_files)}个文件")
        print(f"当前索引: {file_manager.current_index}")
        
        # 测试导航方法
        print(f"有下一个文件: {file_manager.has_next_file()}")
        print(f"有上一个文件: {file_manager.has_previous_file()}")
        
        # 测试切换文件
        if file_manager.has_next_file():
            next_file = file_manager.next_file()
            print(f"切换到下一个文件: {next_file}")
            print(f"新的当前索引: {file_manager.current_index}")
        
        if file_manager.has_previous_file():
            prev_file = file_manager.previous_file()
            print(f"切换到上一个文件: {prev_file}")
            print(f"新的当前索引: {file_manager.current_index}")
        
        print("✅ FileManager方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ FileManager方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_file_methods():
    """测试Application中的文件相关方法"""
    print("\n测试Application文件相关方法...")
    
    try:
        # 检查方法是否存在
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查update_file_list_markers方法
        if 'def update_file_list_markers(self, old_index: int, new_index: int):' in content:
            print("✅ 找到update_file_list_markers方法")
        else:
            print("❌ 未找到update_file_list_markers方法")
            return False
        
        # 检查on_file_select方法中的标记更新调用
        if 'self.update_file_list_markers(old_index, index)' in content:
            print("✅ on_file_select方法中包含标记更新调用")
        else:
            print("❌ on_file_select方法中缺少标记更新调用")
            return False
        
        # 检查prev_file和next_file方法中的标记更新
        if 'self.update_file_list_markers(old_index, self.file_manager.current_index)' in content:
            print("✅ prev_file/next_file方法中包含标记更新调用")
        else:
            print("❌ prev_file/next_file方法中缺少标记更新调用")
            return False
        
        # 检查文件列表显示中的标记
        if 'marker = "● " if i == self.file_manager.current_index else "  "' in content:
            print("✅ 文件列表显示包含选中标记")
        else:
            print("❌ 文件列表显示缺少选中标记")
            return False
        
        # 检查display_signal方法调用
        if 'self.display_signal()' in content:
            print("✅ 找到display_signal方法调用")
        else:
            print("❌ 未找到display_signal方法调用")
            return False
        
        print("✅ Application文件相关方法测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Application文件相关方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_mechanism():
    """测试回调机制"""
    print("\n测试回调机制...")
    
    try:
        # 检查panels.py中的回调绑定
        with open('ui/panels.py', 'r', encoding='utf-8') as f:
            panels_content = f.read()
        
        if "self.file_listbox.bind('<<ListboxSelect>>', self._get_callback('on_file_select'))" in panels_content:
            print("✅ 文件列表框正确绑定on_file_select回调")
        else:
            print("❌ 文件列表框回调绑定不正确")
            return False
        
        if 'def _get_callback(self, callback_name: str) -> Callable:' in panels_content:
            print("✅ 找到_get_callback方法")
        else:
            print("❌ 未找到_get_callback方法")
            return False
        
        # 检查application.py中的回调设置
        with open('core/application.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        if "'on_file_select': self.on_file_select" in app_content:
            print("✅ Application中正确设置on_file_select回调")
        else:
            print("❌ Application中缺少on_file_select回调设置")
            return False
        
        if 'self.panel_manager.set_callbacks(panel_callbacks)' in app_content:
            print("✅ 回调正确传递给panel_manager")
        else:
            print("❌ 回调未正确传递给panel_manager")
            return False
        
        print("✅ 回调机制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 回调机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_display_signal_chain():
    """测试显示信号的调用链"""
    print("\n测试显示信号调用链...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查load_file -> display_signal调用链
        if 'def load_file(self, file_path: str):' in content:
            print("✅ 找到load_file方法")
            
            # 在load_file方法中查找display_signal调用
            if 'self.display_signal()' in content:
                print("✅ load_file方法中包含display_signal调用")
            else:
                print("❌ load_file方法中缺少display_signal调用")
                return False
        else:
            print("❌ 未找到load_file方法")
            return False
        
        # 检查display_signal方法实现
        if 'def display_signal(self):' in content:
            print("✅ 找到display_signal方法")
            
            # 检查是否调用了plot_renderer
            if 'self.plot_renderer.display_signal_2d(' in content or 'self.plot_renderer.display_signal_3d(' in content:
                print("✅ display_signal方法中包含plot_renderer调用")
            else:
                print("❌ display_signal方法中缺少plot_renderer调用")
                return False
            
            # 检查是否更新了画布
            if 'self.chart_manager.get_canvas().draw()' in content:
                print("✅ display_signal方法中包含画布更新调用")
            else:
                print("❌ display_signal方法中缺少画布更新调用")
                return False
        else:
            print("❌ 未找到display_signal方法")
            return False
        
        print("✅ 显示信号调用链测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 显示信号调用链测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试文件选择和绘图更新机制修复...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_file_manager_methods,
        test_application_file_methods,
        test_callback_mechanism,
        test_display_signal_chain
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！文件选择和绘图更新机制修复成功")
        print("现在文件切换时应该能正确更新绘图和文件列表标记")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
