"""
信道扫描模块

负责信道扫描的执行和管理，包括扫描参数配置、扫描过程控制和结果处理。
保持与原有代码完全一致的扫描逻辑。
"""

import threading
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass

# 添加项目路径以导入core_functions
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from core_functions import run_channel_scan
    CHANSCAN_AVAILABLE = True
except ImportError:
    CHANSCAN_AVAILABLE = False
    print("警告: 信道扫描功能不可用，请检查相关依赖")


@dataclass
class ScanResult:
    """扫描结果数据类"""
    start_sample: int
    end_sample: int
    center_freq: float
    bandwidth: float
    confidence: float
    class_name: str = "Unknown"
    snr: float = 0.0
    duration: float = 0.0


class ChannelScanner:
    """
    信道扫描器
    
    负责信道扫描的执行和管理，保持与原有代码完全一致的扫描逻辑。
    """
    
    def __init__(self, i18n_manager):
        """
        初始化信道扫描器
        
        Args:
            i18n_manager: 国际化管理器
        """
        self.i18n_manager = i18n_manager
        self.is_scanning = False
        self.scan_thread = None
        self.scan_results = []
        
        # 回调函数
        self.progress_callback = None
        self.complete_callback = None
        self.error_callback = None
    
    def set_callbacks(self, progress_callback: Optional[Callable] = None,
                     complete_callback: Optional[Callable] = None,
                     error_callback: Optional[Callable] = None):
        """
        设置回调函数
        
        Args:
            progress_callback: 进度回调函数
            complete_callback: 完成回调函数
            error_callback: 错误回调函数
        """
        self.progress_callback = progress_callback
        self.complete_callback = complete_callback
        self.error_callback = error_callback
    
    def is_available(self) -> bool:
        """
        检查信道扫描功能是否可用
        
        Returns:
            是否可用
        """
        return CHANSCAN_AVAILABLE
    
    def run_scan(self, signal_data, metadata: Dict[str, Any], model_path: Optional[str] = None) -> bool:
        """
        运行信道扫描
        
        Args:
            signal_data: 信号数据
            metadata: 信号元数据
            model_path: 模型文件路径
            
        Returns:
            是否成功启动扫描
        """
        if not CHANSCAN_AVAILABLE:
            if self.error_callback:
                texts = self.i18n_manager.get_all_texts()
                self.error_callback(texts['scan_not_available'])
            return False
        
        if self.is_scanning:
            if self.error_callback:
                self.error_callback("扫描正在进行中，请等待完成")
            return False
        
        if signal_data is None:
            if self.error_callback:
                texts = self.i18n_manager.get_all_texts()
                self.error_callback(texts['no_data_loaded'])
            return False
        
        # 启动扫描线程
        self.scan_thread = threading.Thread(target=self._scan_thread, 
                                           args=(signal_data, metadata, model_path),
                                           daemon=True)
        self.scan_thread.start()
        
        return True
    
    def _scan_thread(self, signal_data, metadata: Dict[str, Any], model_path: Optional[str]):
        """
        扫描线程函数
        
        Args:
            signal_data: 信号数据
            metadata: 信号元数据
            model_path: 模型文件路径
        """
        try:
            self.is_scanning = True
            self.scan_results = []
            
            # 获取文本
            texts = self.i18n_manager.get_all_texts()
            
            # 更新进度
            if self.progress_callback:
                self.progress_callback(texts['processing'])
            
            # 调用核心扫描函数
            def internal_progress_callback(message):
                if self.progress_callback:
                    self.progress_callback(message)
            
            # 执行扫描
            success, segments, error_msg = run_channel_scan(
                signal_data=signal_data,
                metadata=metadata,
                model_path=model_path,
                progress_callback=internal_progress_callback
            )
            
            if success and segments:
                # 转换扫描结果
                self.scan_results = []
                for segment in segments:
                    scan_result = ScanResult(
                        start_sample=segment.get('start_sample', 0),
                        end_sample=segment.get('end_sample', 0),
                        center_freq=segment.get('center_freq', 0.0),
                        bandwidth=segment.get('bandwidth', 0.0),
                        confidence=segment.get('confidence', 0.0),
                        class_name=segment.get('class_name', 'Unknown'),
                        snr=segment.get('snr', 0.0),
                        duration=segment.get('duration', 0.0)
                    )
                    self.scan_results.append(scan_result)
                
                # 调用完成回调
                if self.complete_callback:
                    self.complete_callback(self.scan_results)
            else:
                # 调用错误回调
                if self.error_callback:
                    self.error_callback(error_msg or "扫描失败")
                    
        except Exception as e:
            error_msg = f"扫描过程中发生异常: {str(e)}"
            print(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
        finally:
            self.is_scanning = False
    
    def stop_scan(self):
        """停止扫描"""
        if self.is_scanning and self.scan_thread:
            # 注意：这里只是设置标志，实际的停止需要在扫描函数中实现
            self.is_scanning = False
            # 等待线程结束
            if self.scan_thread.is_alive():
                self.scan_thread.join(timeout=5.0)
    
    def get_scan_results(self) -> List[ScanResult]:
        """
        获取扫描结果
        
        Returns:
            扫描结果列表
        """
        return self.scan_results.copy()
    
    def clear_results(self):
        """清空扫描结果"""
        self.scan_results = []
    
    def get_scan_status(self) -> bool:
        """
        获取扫描状态
        
        Returns:
            是否正在扫描
        """
        return self.is_scanning
    
    def get_results_count(self) -> int:
        """
        获取结果数量
        
        Returns:
            扫描结果数量
        """
        return len(self.scan_results)
    
    def get_result_by_index(self, index: int) -> Optional[ScanResult]:
        """
        根据索引获取扫描结果
        
        Args:
            index: 结果索引
            
        Returns:
            扫描结果，如果索引无效则返回None
        """
        if 0 <= index < len(self.scan_results):
            return self.scan_results[index]
        return None
    
    def export_results_to_dict(self) -> List[Dict[str, Any]]:
        """
        将扫描结果导出为字典列表
        
        Returns:
            字典格式的扫描结果列表
        """
        results = []
        for result in self.scan_results:
            result_dict = {
                'start_sample': result.start_sample,
                'end_sample': result.end_sample,
                'center_freq': result.center_freq,
                'bandwidth': result.bandwidth,
                'confidence': result.confidence,
                'class_name': result.class_name,
                'snr': result.snr,
                'duration': result.duration
            }
            results.append(result_dict)
        return results
    
    def import_results_from_dict(self, results_data: List[Dict[str, Any]]):
        """
        从字典列表导入扫描结果
        
        Args:
            results_data: 字典格式的扫描结果列表
        """
        self.scan_results = []
        for result_dict in results_data:
            scan_result = ScanResult(
                start_sample=result_dict.get('start_sample', 0),
                end_sample=result_dict.get('end_sample', 0),
                center_freq=result_dict.get('center_freq', 0.0),
                bandwidth=result_dict.get('bandwidth', 0.0),
                confidence=result_dict.get('confidence', 0.0),
                class_name=result_dict.get('class_name', 'Unknown'),
                snr=result_dict.get('snr', 0.0),
                duration=result_dict.get('duration', 0.0)
            )
            self.scan_results.append(scan_result)
    
    def filter_results_by_confidence(self, min_confidence: float) -> List[ScanResult]:
        """
        根据置信度过滤结果
        
        Args:
            min_confidence: 最小置信度
            
        Returns:
            过滤后的结果列表
        """
        return [result for result in self.scan_results if result.confidence >= min_confidence]
    
    def filter_results_by_class(self, class_name: str) -> List[ScanResult]:
        """
        根据类别过滤结果
        
        Args:
            class_name: 类别名称
            
        Returns:
            过滤后的结果列表
        """
        return [result for result in self.scan_results if result.class_name == class_name]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取扫描结果统计信息
        
        Returns:
            统计信息字典
        """
        if not self.scan_results:
            return {
                'total_segments': 0,
                'avg_confidence': 0.0,
                'max_confidence': 0.0,
                'min_confidence': 0.0,
                'class_distribution': {}
            }
        
        confidences = [result.confidence for result in self.scan_results]
        class_names = [result.class_name for result in self.scan_results]
        
        # 统计类别分布
        class_distribution = {}
        for class_name in class_names:
            class_distribution[class_name] = class_distribution.get(class_name, 0) + 1
        
        return {
            'total_segments': len(self.scan_results),
            'avg_confidence': sum(confidences) / len(confidences),
            'max_confidence': max(confidences),
            'min_confidence': min(confidences),
            'class_distribution': class_distribution
        }
