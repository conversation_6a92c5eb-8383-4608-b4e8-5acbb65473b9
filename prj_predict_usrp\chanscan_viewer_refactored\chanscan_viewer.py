#!/usr/bin/env python3
"""
信道扫描分段查看工具 (Channel Scan Segmentation Viewer)

这是一个基于 Python 和 Tkinter 的信号数据分析工具，主要功能包括：
- 数据文件可视化（支持 .dat、.bvsp、.hdfv 格式）
- 信道扫描和信号分段（基于 proc_wbsig 函数）
- 分段结果管理（可选中、添加、删除、编辑）
- 向量数据库管理和查看
- 数据库记录分析图表查看

核心功能:
    - 数据文件可视化（四种信号分析图表：时域点值图、时域信号图、频谱图、时频图）
    - 信道扫描自动分段（支持参数调整）
    - 分段结果列表（支持单选/多选，显示详细信息）
    - 分段位置手动编辑（支持实时预览和保存）
    - 添加选中分段到向量数据库（自动生成特征向量）
    - 数据库记录管理（查看、删除、分析图表）
    - 源文件管理（自动复制到配置目录，依赖检查删除）

技术特性:
    - 高性能图表绘制（智能采样、异步计算）
    - 完整的分段标记显示（时间、频率、信息标签）
    - 数据库向后兼容（支持新旧格式自动检测）
    - 跨平台字体支持（Windows/Linux自适应）
    - 多语言界面支持（中文/英文）

作者：Caonairui
创建时间：2025-07-09
最后更新：2025-07-15
版本：v2.1
"""

# 解决 OpenMP 库冲突问题
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 标准库导入
import tkinter as tk
from tkinter import ttk, filedialog
import sys
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor

# 科学计算库导入
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from scipy.signal import spectrogram

# 导入自定义模块
try:
    from .frequency_dialog import FrequencyInputDialog, FileCountInputDialog
    from .progress_dialog import ProgressDialog
    from .data_capture_controller import DataCaptureController
    from .adaptive_chart_renderer import AdaptiveChartRenderer
except ImportError:
    # 当作为主模块运行时的导入路径
    from frequency_dialog import FrequencyInputDialog, FileCountInputDialog
    from progress_dialog import ProgressDialog
    from data_capture_controller import DataCaptureController
    from adaptive_chart_renderer import AdaptiveChartRenderer

# 添加项目路径到系统路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 尝试导入 torch（3D 显示功能需要）
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("警告: 未找到 torch 库，3D 显示功能将不可用。如需 3D 功能，请安装: pip install torch")

# 导入项目相关模块
try:
    # 添加项目路径到系统路径
    parent_dir = Path(__file__).parent.parent
    sys.path.append(str(parent_dir))
    
    # 核心功能导入
    from core_functions import (
        load_signal_data, 
        load_arcface_model
    )
    
    CHANSCAN_AVAILABLE = True
    print("信道扫描功能可用")
except ImportError as e:
    CHANSCAN_AVAILABLE = False
    print(f"警告: 信道扫描功能不可用，原因: {e}")

# 配置 matplotlib 中文字体显示
import platform

def configure_matplotlib_fonts():
    """
    智能配置matplotlib字体，避免字体警告

    根据操作系统自动选择合适的中文字体，确保图表中的中文文本能够正确显示。
    支持Linux、Windows和其他操作系统的字体配置。
    """
    import matplotlib.font_manager as fm

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    if platform.system() == 'Linux':
        # Linux系统字体候选列表，按优先级排序
        font_candidates = [
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
            'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
            'SimHei', 'Liberation Sans'
        ]
        # 只添加系统中实际存在的字体
        valid_fonts = [font for font in font_candidates if font in available_fonts]
        # 如果没有找到任何中文字体，添加DejaVu Sans作为最后备选
        if not valid_fonts:
            valid_fonts = ['DejaVu Sans']
        plt.rcParams['font.sans-serif'] = valid_fonts
    elif platform.system() == 'Windows':
        # Windows系统
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
    else:
        # 其他系统使用通用字体
        plt.rcParams['font.sans-serif'] = ['Liberation Sans', 'DejaVu Sans', 'Arial']

    plt.rcParams['axes.unicode_minus'] = False
    print(f"matplotlib字体配置: {plt.rcParams['font.sans-serif'][:3]}...")

# 配置字体
configure_matplotlib_fonts()


class ScaledMessageBox:
    """
    自定义的放大弹窗类，支持多语言和 1.5 倍字体放大
    """
    
    @staticmethod
    def get_scaled_font(language='zh'):
        import tkinter.font as tkFont
        import platform

        system = platform.system()
        if system == 'Windows':
            font_family = 'Microsoft YaHei' if language == 'zh' else 'Segoe UI'
        elif system == 'Linux':
            if language == 'zh':
                # Linux中文字体候选列表，移除Microsoft YaHei避免警告
                chinese_candidates = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                    'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                    'Liberation Sans', 'DejaVu Sans'
                ]
                # 检查可用字体
                available_fonts = tkFont.families()
                font_family = 'DejaVu Sans'  # 默认值
                for font in chinese_candidates:
                    if font in available_fonts:
                        font_family = font
                        break
            else:
                font_family = 'DejaVu Sans'
        else:
            font_family = 'Liberation Sans'  # 其他系统使用更通用的字体
        
        base_size = int(9 * 1.5)
        return tkFont.Font(family=font_family, size=base_size, weight='normal')

    @staticmethod
    def _calculate_text_size(text, font):
        """
        计算文本在给定字体下的显示尺寸

        Args:
            text (str): 要计算的文本
            font (tkinter.font.Font): 字体对象

        Returns:
            tuple: (width, height) 文本的宽度和高度
        """
        # 创建临时的根窗口来测量文本
        temp_root = tk.Tk()
        temp_root.withdraw()  # 隐藏窗口

        try:
            # 创建临时标签来测量文本尺寸
            temp_label = tk.Label(temp_root, text=text, font=font, wraplength=0)
            temp_label.update_idletasks()

            # 获取文本尺寸
            width = temp_label.winfo_reqwidth()
            height = temp_label.winfo_reqheight()

            # 对于多行文本，计算最长行的宽度
            if '\n' in text:
                lines = text.split('\n')
                max_line_width = 0
                for line in lines:
                    if line.strip():  # 忽略空行
                        line_label = tk.Label(temp_root, text=line, font=font)
                        line_label.update_idletasks()
                        line_width = line_label.winfo_reqwidth()
                        max_line_width = max(max_line_width, line_width)
                width = max_line_width

            # 添加额外的缓冲空间，确保文本完全显示
            width = int(width * 1.1)  # 增加10%的缓冲空间

            return width, height

        finally:
            temp_root.destroy()

    @staticmethod
    def showinfo(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'info', parent, language)
    
    @staticmethod
    def showwarning(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'warning', parent, language)
    
    @staticmethod
    def showerror(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'error', parent, language)
    
    @staticmethod
    def askokcancel(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'okcancel', parent, language)
    
    @staticmethod
    def askyesno(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'yesno', parent, language)

    @staticmethod
    def askyesnocancel(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'yesnocancel', parent, language)

    @staticmethod
    def _show_dialog(title, message, dialog_type, parent=None, language='zh'):
        # 如果是错误信息，同时输出到命令行
        if dialog_type == 'error':
            print(f"\n=== 错误信息 ===")
            print(f"标题: {title}")
            print(f"详情: {message}")
            print("=" * 50)

        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.resizable(True, True)  # 允许调整大小
        dialog.grab_set()

        # 确保对话框始终在最顶层
        dialog.attributes('-topmost', True)
        dialog.lift()
        dialog.focus_force()

        # 将对话框添加到活动对话框列表中（如果parent是主窗口）
        if parent and hasattr(parent, 'active_dialogs'):
            parent.active_dialogs.append(dialog)

        # 获取字体信息用于计算文本尺寸
        font = ScaledMessageBox.get_scaled_font(language)

        # 计算文本实际需要的尺寸
        text_width, _ = ScaledMessageBox._calculate_text_size(message, font)

        # 设置最小和最大宽度
        min_width = 300
        max_width = int(dialog.winfo_screenwidth() * 0.8)  # 屏幕宽度的80%

        # 智能计算边距：根据文本长度调整
        icon_width = 80   # 图标和间距的宽度

        # 根据文本宽度动态调整边距
        if text_width < 200:
            # 短文本：使用较小的边距
            padding_width = 80
        elif text_width < 400:
            # 中等文本：使用中等边距
            padding_width = 100
        else:
            # 长文本：使用较大的边距
            padding_width = 120

        required_width = text_width + icon_width + padding_width

        # 应用宽度限制
        width = max(min_width, min(required_width, max_width))

        # 对于短消息，避免窗口过宽
        if text_width < 150 and width > 450:
            width = 450

        # 计算高度
        base_height = 200 if dialog_type == 'error' else 150
        height = int(base_height * 1.5)

        # 根据消息行数动态调整高度
        msg_lines = message.count('\n') + 1
        if msg_lines > 3:
            height += int(25 * (msg_lines - 3) * 1.5)

        # 如果文本宽度超过了最大宽度，需要增加高度来容纳换行
        if text_width > (max_width - icon_width - padding_width):
            # 估算需要的额外行数
            extra_lines = text_width // (max_width - icon_width - padding_width)
            height += int(25 * extra_lines * 1.5)
        
        if parent:
            parent_x = parent.winfo_rootx()
            parent_y = parent.winfo_rooty()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
        else:
            x = (dialog.winfo_screenwidth() - width) // 2
            y = (dialog.winfo_screenheight() - height) // 2
        
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # font变量已在前面定义，这里只需要定义button_font
        button_font = ScaledMessageBox.get_scaled_font(language)
        
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # 图标框架
        icon_frame = tk.Frame(main_frame)
        icon_frame.pack(side=tk.LEFT, padx=(0, 15))
        
        import platform
        system = platform.system()
        
        if dialog_type == 'info':
            icon_text = "[i]" if system == 'Linux' else "ℹ️"
            icon_color = "#1E90FF"
        elif dialog_type == 'warning':
            icon_text = "/!\\" if system == 'Linux' else "⚠️"
            icon_color = "#FF8C00"
        elif dialog_type == 'error':
            icon_text = "[X]" if system == 'Linux' else "❌"
            icon_color = "#DC143C"
        else:
            icon_text = "[?]" if system == 'Linux' else "❓"
            icon_color = "#4682B4"
        
        if system == 'Linux':
            icon_font = ('monospace', int(20 * 1.5), 'bold')
        else:
            icon_font = ('Arial', int(24 * 1.5))
        
        icon_label = tk.Label(icon_frame, text=icon_text, font=icon_font, fg=icon_color)
        icon_label.pack()
        
        # 消息文本
        msg_frame = tk.Frame(main_frame)
        msg_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 对于长消息或错误消息，使用Text控件和滚动条
        if len(message) > 200 or dialog_type == 'error' or message.count('\n') > 5:
            # 创建Text控件和滚动条
            text_frame = tk.Frame(msg_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            msg_text = tk.Text(text_frame, font=font, wrap=tk.WORD,
                              yscrollcommand=scrollbar.set, state=tk.NORMAL,
                              height=min(15, max(5, message.count('\n') + 3)))
            msg_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.config(command=msg_text.yview)

            msg_text.insert(tk.END, message)
            msg_text.config(state=tk.DISABLED)
        else:
            # 对于短消息，使用Label
            msg_label = tk.Label(msg_frame, text=message, font=font, wraplength=int(350 * 1.5),
                               justify=tk.LEFT, anchor='w')
            msg_label.pack(fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=(0, 20))
        
        result = [None]
        
        def on_ok():
            result[0] = True
            dialog.destroy()
        
        def on_cancel():
            result[0] = False
            dialog.destroy()
        
        def on_yes():
            result[0] = True
            dialog.destroy()
        
        def on_no():
            result[0] = False
            dialog.destroy()

        def on_cancel_yesnocancel():
            result[0] = None
            dialog.destroy()

        if language == 'zh':
            ok_text, cancel_text, yes_text, no_text = "确定", "取消", "是", "否"
        else:
            ok_text, cancel_text, yes_text, no_text = "OK", "Cancel", "Yes", "No"
        
        if dialog_type in ['info', 'warning', 'error']:
            ok_btn = tk.Button(button_frame, text=ok_text, command=on_ok, font=button_font, width=12)
            ok_btn.pack(side=tk.RIGHT)
            result[0] = True
        elif dialog_type == 'okcancel':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)
            
            ok_btn = tk.Button(btn_container, text=ok_text, command=on_ok, font=button_font, width=10)
            ok_btn.pack(side=tk.RIGHT, padx=(0, 10))
            
            cancel_btn = tk.Button(btn_container, text=cancel_text, command=on_cancel, font=button_font, width=10)
            cancel_btn.pack(side=tk.RIGHT)
            
            result[0] = False
        elif dialog_type == 'yesno':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)

            yes_btn = tk.Button(btn_container, text=yes_text, command=on_yes, font=button_font, width=10)
            yes_btn.pack(side=tk.RIGHT, padx=(0, 10))

            no_btn = tk.Button(btn_container, text=no_text, command=on_no, font=button_font, width=10)
            no_btn.pack(side=tk.RIGHT)

            result[0] = False
        elif dialog_type == 'yesnocancel':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)

            yes_btn = tk.Button(btn_container, text=yes_text, command=on_yes, font=button_font, width=8)
            yes_btn.pack(side=tk.RIGHT, padx=(0, 8))

            no_btn = tk.Button(btn_container, text=no_text, command=on_no, font=button_font, width=8)
            no_btn.pack(side=tk.RIGHT, padx=(0, 8))

            cancel_btn = tk.Button(btn_container, text=cancel_text, command=on_cancel_yesnocancel, font=button_font, width=8)
            cancel_btn.pack(side=tk.RIGHT)

            result[0] = None
        
        def on_escape(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            elif dialog_type == 'yesnocancel':
                on_cancel_yesnocancel()
            else:
                on_cancel()
        dialog.bind('<Escape>', on_escape)

        def on_enter(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            elif dialog_type == 'okcancel':
                on_ok()
            elif dialog_type == 'yesno':
                on_yes()
            elif dialog_type == 'yesnocancel':
                on_yes()
        dialog.bind('<Return>', on_enter)
        
        if dialog_type in ['info', 'warning', 'error']:
            ok_btn.focus_set()
        elif dialog_type == 'okcancel':
            ok_btn.focus_set()
        elif dialog_type == 'yesno':
            yes_btn.focus_set()
        elif dialog_type == 'yesnocancel':
            yes_btn.focus_set()
        
        dialog.wait_window()
        return result[0]


class ChannelScanViewer:
    """
    信道扫描分段查看工具主类
    
    基于 data_viewer 修改，删除模型推理功能，增加信道扫描分段功能
    """
    
    def __init__(self, root):
        self.root = root
        self.current_language = 'zh'  # 当前语言设置
        
        # 定义中文界面文本
        self.texts_zh = {
            'title': '信道扫描分段查看工具',
            'select_file': '选择文件',
            'select_folder': '选择文件夹',
            'select_model': '选择模型',
            'previous': '上一个',
            'next': '下一个',
            'toggle_view': '切换视图',
            'view_2d': '2D视图',
            'view_3d': '3D视图',
            'language_switch': '中/En',
            'file_list': '文件列表',
            'file_info': '文件信息',
            'segment_results': '分段结果',
            'please_select': '请选择数据文件',
            'warning': '警告',
            'no_files_found': '未找到支持的数据文件！',
            'loading': '正在加载',
            'loaded': '已加载',
            'load_failed': '加载失败',
            'error': '错误',
            'load_file_failed': '加载文件失败',
            'data_capture': '数据捕获',
            'channel_scan': '信道扫描',
            'view_database': '查看数据库',
            'scan_not_available': '请先安装相关依赖包',
            'no_data_loaded': '请先加载数据文件',
            'processing': '正在处理',
            'scan_complete': '扫描完成',
            'scan_failed': '扫描失败',
            'clear_results': '清空',
            'add_to_db': '添加到数据库',
            'skip_file': '跳过文件',
            'select_all': '全选',
            'deselect_all': '全不选',
            'delete_selected': '删除选中',
            'add_segments_to_db_confirm': '这将使用模型处理选中的信号段并添加到数据库，确认吗？',
            'no_segments_selected': '请先选择要添加的信号段',
            'adding_segments_to_db': '正在添加信号段到数据库...',
            'add_to_db_success': '成功添加 {count} 个信号段到数据库',
            'add_to_db_failed': '添加信号段到数据库失败',
            'confirm_delete_segments': '确认删除选中的信号段吗？',
            'segments_deleted': '已删除 {count} 个信号段',
            'model_loading': '正在加载模型...',
            'model_load_failed': '模型加载失败，数据库功能受限',
            'model_load_success': '模型加载成功',
            'no_model_prompt_title': '模型未加载',
            'no_model_prompt_message': '信道扫描功能需要加载模型。\n是否现在选择一个模型文件？',
            'no_model_db_prompt_title': '添加到数据库需要模型',
            'no_model_db_prompt_message': '添加信号段到数据库需要加载模型。\n是否现在选择一个模型文件？',
            'no_model_db_add_prompt_message': '添加到数据库需要加载模型。\n是否现在选择一个模型文件？\n如果加载成功，将自动继续执行添加操作。',
            'selected_segments_count': '选中的分段数: {count}',
            'expand_panel': '展开面板',
            'results_cleared': '分段结果已清空',
            'new_scan_prompt': '点击"信道扫描"开始新的扫描...',
            'ready': '就绪',
            'signal_analysis_charts': '信号分析图表',
            'time_domain_points': '时域信号点值图',
            'time_domain_signal': '时域信号图',
            'frequency_spectrum': '频谱图',
            'time_frequency': '时频图',
            'file_path': '文件路径',
            'file_size': '文件大小',
            'sample_rate': '采样率',
            'center_frequency': '中心频率',
            'signal_length': '信号长度',
            'duration': '持续时间',
            'samples_unit': '个采样点',
            'close': '关闭',
            'no_segments_selected': '请先选择要添加的分段',
            'segments_added': '分段已添加到数据库',
            'add_failed': '添加失败',
            'segments_deleted': '选中的分段已删除',
            'no_database_file': '数据库文件不存在',
            'database_structure': '数据库结构',
            'preparing_data': '准备数据',
            'data_sampling_completed': '数据采样完成',
            'points_unit': '点',
            'time_plot_completed_calculating_spectrum': '时域图绘制完成，正在计算频谱',
            'spectrum_completed_calculating_timefreq': '频谱计算完成，正在计算时频图',
            'completing_rendering': '完成渲染',
            'time_domain_range': '时域范围',
            'display_text': '显示',
            'sampling_text': '采样',
            'time_points_unit': '时间点 (×10^5)',
            'signal_voltage': '信号电压',
            'time_ms': '时间(ms)',
            'voltage_v': '电压(V)',
            'frequency_mhz': '频率(MHz)',
            'spectrum_value': '频谱值',
            'time_frame': '时间帧',
            'frequency_frame': '频率帧',
            'power_db': '功率(dB)',
            'power_spectral_density_3d': '功率谱密度图 3D',
            'filtered_psd_3d': '滤波后功率谱密度图 3D',
            'signal_amplitude': '信号幅度',
            'sample_points': '采样点',
            'amplitude': '幅度'
        }
        
        # 定义英文界面文本
        self.texts_en = {
            'title': 'Channel Scan Segmentation Viewer',
            'select_file': 'Select File',
            'select_folder': 'Select Folder',
            'select_model': 'Select Model',
            'previous': 'Previous',
            'next': 'Next',
            'toggle_view': 'Toggle View',
            'view_2d': '2D View',
            'view_3d': '3D View',
            'language_switch': 'En/中',
            'file_list': 'File List',
            'file_info': 'File Information',
            'segment_results': 'Segment Results',
            'please_select': 'Please select a data file',
            'warning': 'Warning',
            'no_files_found': 'No supported data files found!',
            'loading': 'Loading',
            'loaded': 'Loaded',
            'load_failed': 'Load Failed',
            'error': 'Error',
            'load_file_failed': 'Failed to load file',
            'data_capture': 'Data Capture',
            'channel_scan': 'Channel Scan',
            'view_database': 'View Database',
            'scan_not_available': 'Please install dependencies first',
            'no_data_loaded': 'Please load data file first',
            'processing': 'Processing',
            'scan_complete': 'Scan Complete',
            'scan_failed': 'Scan Failed',
            'clear_results': 'Clear',
            'add_to_db': 'Add to Database',
            'skip_file': 'Skip File',
            'select_all': 'Select All',
            'deselect_all': 'Deselect All',
            'delete_selected': 'Delete Selected',
            'add_segments_to_db_confirm': 'This will process selected segments using the model and add them to the database. Continue?',
            'no_segments_selected': 'Please select segments to add first',
            'adding_segments_to_db': 'Adding segments to database...',
            'add_to_db_success': 'Successfully added {count} segments to database',
            'add_to_db_failed': 'Failed to add segments to database',
            'confirm_delete_segments': 'Confirm to delete selected segments?',
            'segments_deleted': 'Deleted {count} segments',
            'model_loading': 'Loading model...',
            'model_load_failed': 'Model loading failed, database functions limited',
            'model_load_success': 'Model loaded successfully',
            'no_model_prompt_title': 'Model Not Loaded',
            'no_model_prompt_message': 'Channel scan requires a model to be loaded.\nDo you want to select a model file now?',
            'no_model_db_prompt_title': 'Model Required for Database',
            'no_model_db_prompt_message': 'Adding segments to database requires a model.\nDo you want to select a model file now?',
            'no_model_db_add_prompt_message': 'Adding to the database requires a model.\nSelect a model file now?\nIf loaded successfully, the operation will continue automatically.',
            'selected_segments_count': 'Selected segments: {count}',
            'expand_panel': 'Expand Panel',
            'results_cleared': 'Segment results cleared',
            'new_scan_prompt': 'Click "Channel Scan" to start new scan...',
            'ready': 'Ready',
            'signal_analysis_charts': 'Signal Analysis Charts',
            'time_domain_points': 'Time Domain Signal Points',
            'time_domain_signal': 'Time Domain Signal',
            'frequency_spectrum': 'Frequency Spectrum',
            'time_frequency': 'Time-Frequency',
            'file_path': 'File Path',
            'file_size': 'File Size',
            'sample_rate': 'Sample Rate',
            'center_frequency': 'Center Frequency',
            'signal_length': 'Signal Length',
            'duration': 'Duration',
            'samples_unit': 'samples',
            'close': 'Close',
            'segments_added': 'Segments added to database',
            'add_failed': 'Addition failed',
            'segments_deleted': 'Selected segments deleted',
            'no_database_file': 'Database file does not exist',
            'database_structure': 'Database Structure',
            'preparing_data': 'Preparing data',
            'data_sampling_completed': 'Data sampling completed',
            'points_unit': 'pts',
            'time_plot_completed_calculating_spectrum': 'Time plot completed, calculating spectrum',
            'spectrum_completed_calculating_timefreq': 'Spectrum completed, calculating time-frequency',
            'completing_rendering': 'Completing rendering',
            'time_domain_range': 'Time domain range',
            'display_text': 'Display',
            'sampling_text': 'Sampling',
            'time_points_unit': 'Time Points (×10^5)',
            'signal_voltage': 'Signal Voltage',
            'time_ms': 'Time (ms)',
            'voltage_v': 'Voltage (V)',
            'frequency_mhz': 'Frequency (MHz)',
            'spectrum_value': 'Spectrum Value',
            'time_frame': 'Time Frame',
            'frequency_frame': 'Frequency Frame',
            'power_db': 'Power (dB)',
            'power_spectral_density_3d': 'Power Spectral Density 3D',
            'filtered_psd_3d': 'Filtered PSD 3D',
            'signal_amplitude': 'Signal Amplitude',
            'sample_points': 'Sample Points',
            'amplitude': 'Amplitude'
        }
        
        # 当前选中的文本字典
        self.texts = self.texts_zh
        
        # 初始化数据成员
        self.init_data_members()
        
        # 设置窗口
        self.setup_window()
        
        # 设置字体
        self.setup_fonts()
        
        # 创建界面
        self.create_interface()
        
        # 初始化图表
        self.init_plots()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 检查数据库版本并提示迁移（延迟执行，避免阻塞界面初始化）
        self.root.after(1000, self.check_database_migration)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                import json
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.last_file_dir = config.get('last_file_dir', os.getcwd())
                    self.last_folder_dir = config.get('last_folder_dir', os.getcwd())
                    self.last_model_dir = config.get('last_model_dir', os.getcwd())
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.last_file_dir = os.getcwd()
            self.last_folder_dir = os.getcwd()
            self.last_model_dir = os.getcwd()

    def save_config(self):
        """保存配置文件"""
        try:
            import json
            config = {
                'last_file_dir': getattr(self, 'last_file_dir', os.getcwd()),
                'last_folder_dir': getattr(self, 'last_folder_dir', os.getcwd()),
                'last_model_dir': getattr(self, 'last_model_dir', os.getcwd())
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def check_database_migration(self):
        """检查数据库版本并提示迁移"""
        try:
            # 导入数据库版本检测函数
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.append(parent_dir)

            from usrlib.usrlib import detect_database_version, migrate_database_structure

            # 检测数据库版本
            version, has_record_id, error_msg = detect_database_version()

            if error_msg and "数据库文件不存在" in error_msg:
                # 数据库文件不存在，无需迁移
                return

            if error_msg:
                print(f"数据库版本检测失败: {error_msg}")
                return

            # 如果数据库已经是最新版本，无需迁移
            if has_record_id:
                print(f"数据库版本: {version} (最新版本)")
                return

            # 需要迁移，显示提示对话框
            print(f"检测到旧版本数据库: {version}")
            self.show_migration_dialog(version)

        except Exception as e:
            print(f"数据库迁移检查失败: {e}")

    def show_migration_dialog(self, current_version):
        """显示数据库迁移对话框"""
        try:
            migration_message = (
                f"检测到数据库版本: {current_version}\n\n"
                "为了支持新功能，需要升级数据库结构：\n"
                "• 新增 record_id 字段作为数据编号\n"
                "• class_id 字段恢复为类别标识符\n"
                "• 保持所有现有数据不变\n\n"
                "升级过程将自动备份原数据库。\n"
                "是否现在进行数据库升级？"
            )

            user_choice = ScaledMessageBox.askyesno(
                "数据库升级",
                migration_message,
                parent=self.root,
                language=self.current_language
            )

            if user_choice:
                self.perform_database_migration()
            else:
                # 用户选择稍后升级
                later_message = (
                    "数据库升级已推迟。\n\n"
                    "注意：在升级前，新功能可能无法正常工作。\n"
                    "您可以随时通过 '查看数据库' 功能进行升级。"
                )
                ScaledMessageBox.showinfo(
                    "升级推迟",
                    later_message,
                    parent=self.root,
                    language=self.current_language
                )

        except Exception as e:
            print(f"显示迁移对话框失败: {e}")

    def perform_database_migration(self):
        """执行数据库迁移"""
        try:
            # 导入迁移函数
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.append(parent_dir)

            from usrlib.usrlib import migrate_database_structure

            # 创建进度窗口
            progress_window = self.create_migration_progress_window()

            def migration_thread():
                try:
                    # 执行迁移
                    success, migrated_records, error_msg = migrate_database_structure(
                        progress_callback=progress_window.update_progress
                    )

                    # 在主线程中显示结果
                    self.root.after(0, lambda: self.on_migration_complete(
                        progress_window, success, migrated_records, error_msg
                    ))

                except Exception as e:
                    self.root.after(0, lambda: self.on_migration_complete(
                        progress_window, False, 0, str(e)
                    ))

            # 启动迁移线程
            import threading
            migration_thread = threading.Thread(target=migration_thread)
            migration_thread.daemon = True
            migration_thread.start()

        except Exception as e:
            print(f"执行数据库迁移失败: {e}")
            ScaledMessageBox.showerror(
                "迁移失败",
                f"数据库迁移失败: {str(e)}",
                parent=self.root,
                language=self.current_language
            )

    def create_migration_progress_window(self):
        """创建数据库迁移进度窗口"""
        class MigrationProgressWindow:
            def __init__(self, parent):
                self.parent = parent

                # 创建进度窗口
                self.window = tk.Toplevel(parent)
                self.window.title("数据库升级")
                self.window.geometry("400x200")
                self.window.transient(parent)
                self.window.grab_set()
                self.window.resizable(False, False)

                # 居中显示
                self.window.update_idletasks()
                x = (self.window.winfo_screenwidth() - 400) // 2
                y = (self.window.winfo_screenheight() - 200) // 2
                self.window.geometry(f"400x200+{x}+{y}")

                # 创建界面元素
                main_frame = tk.Frame(self.window)
                main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

                # 标题
                title_label = tk.Label(main_frame, text="正在升级数据库...",
                                     font=('Arial', 12, 'bold'))
                title_label.pack(pady=(0, 10))

                # 进度条
                import tkinter.ttk as ttk
                self.progress_var = tk.DoubleVar()
                self.progress_bar = ttk.Progressbar(main_frame,
                                                  variable=self.progress_var,
                                                  maximum=100)
                self.progress_bar.pack(fill=tk.X, pady=(0, 10))

                # 状态标签
                self.status_label = tk.Label(main_frame, text="准备开始...",
                                           font=('Arial', 10))
                self.status_label.pack(pady=(0, 10))

                # 详细信息标签
                self.detail_label = tk.Label(main_frame, text="",
                                           font=('Arial', 9), fg='gray')
                self.detail_label.pack()

            def update_progress(self, current, total, message):
                """更新进度"""
                if total > 0:
                    progress = (current / total) * 100
                    self.progress_var.set(progress)

                self.status_label.config(text=message)
                self.detail_label.config(text=f"进度: {current}/{total}" if total > 0 else "")
                self.window.update()

            def close(self):
                """关闭进度窗口"""
                try:
                    self.window.destroy()
                except:
                    pass

        return MigrationProgressWindow(self.root)

    def on_migration_complete(self, progress_window, success, migrated_records, error_msg):
        """迁移完成回调"""
        try:
            # 关闭进度窗口
            progress_window.close()

            if success:
                # 迁移成功
                success_message = (
                    f"数据库升级成功！\n\n"
                    f"• 升级记录数: {migrated_records}\n"
                    f"• 新增 record_id 字段作为数据编号\n"
                    f"• class_id 字段恢复为类别标识符\n"
                    f"• 原数据库已自动备份\n\n"
                    f"现在可以使用所有新功能了。"
                )
                ScaledMessageBox.showinfo(
                    "升级成功",
                    success_message,
                    parent=self.root,
                    language=self.current_language
                )
            else:
                # 迁移失败
                error_message = (
                    f"数据库升级失败：\n\n{error_msg}\n\n"
                    f"请检查数据库文件权限或联系技术支持。"
                )
                ScaledMessageBox.showerror(
                    "升级失败",
                    error_message,
                    parent=self.root,
                    language=self.current_language
                )

        except Exception as e:
            print(f"迁移完成回调失败: {e}")
    
    def setup_window(self):
        """设置主窗口"""
        self.root.title(self.texts['title'])

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 使用固定的合理尺寸，避免受扩展显示器影响
        # 特别是通过xshell连接Linux服务器时，屏幕尺寸可能不准确
        import platform
        if platform.system() == 'Linux':
            # Linux下使用固定的合理尺寸
            window_width = 1600
            window_height = 1000
        else:
            # Windows和其他系统可以根据屏幕尺寸调整
            window_width = int(screen_width * 0.8)
            window_height = int(screen_height * 0.8)

        # 计算窗口位置（居中）
        if platform.system() == 'Linux':
            x = max(0, (screen_width - window_width) // 8)
            y = max(0, (screen_height - window_height) // 4)
        else:
            x = max(0, (screen_width - window_width) // 2)
            y = max(0, (screen_height - window_height) // 2)

        # 设置窗口几何
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 设置最小尺寸
        self.root.minsize(1000, 700)
        
        # 设置窗口图标（如果有的话）
        try:
            # 这里可以设置窗口图标
            pass
        except:
            pass
    
    def init_data_members(self):
        """初始化数据成员"""
        # 文件相关
        self.current_files = []
        self.current_index = 0
        self.current_signal = None
        self.current_metadata = {}
        self.pending_data_warning = None  # 用于存储待显示的数据异常警告
        self.active_dialogs = []  # 跟踪活动的对话框

        # 视图模式
        self.is_3d_mode = False
        
        # 分段结果
        self.segment_results = []
        self.segment_fs = None # 用于存储分段的采样率
        
        # 模型
        self.arcface_model = None
        self.pending_add_to_db_after_model_load = False
        
        # 界面相关
        self.panel_collapsed = {}
        
        # 图表相关
        self.fig = None
        self.canvas = None
        self.axes = None
        
        # 状态
        self.is_loading = False

        # 记住上次选择的目录
        self.config_file = os.path.join(os.path.dirname(__file__), 'viewer_config.json')
        self.load_config()

        # 分别记住不同类型的上次选择目录
        if not hasattr(self, 'last_file_dir'):
            self.last_file_dir = os.getcwd()
        if not hasattr(self, 'last_folder_dir'):
            self.last_folder_dir = os.getcwd()
        if not hasattr(self, 'last_model_dir'):
            self.last_model_dir = os.getcwd()

        # 保持向后兼容
        self.last_selected_dir = self.last_file_dir

        # 新增：data_viewer 中的绘图相关变量
        self.is_computing = False  # 是否正在计算
        self.thread_pool = ThreadPoolExecutor(max_workers=4)  # 线程池
        
        # 初始化自适应图表渲染器
        self.chart_renderer = AdaptiveChartRenderer(max_workers=4)

        # GPU加速支持检测
        self.gpu_available = False
        self.gpu_backend = None
        try:
            import cupy as cp
            self.cp = cp
            self.gpu_available = True
            self.gpu_backend = 'cupy'
            print("GPU加速可用 (CuPy)")
        except ImportError:
            try:
                import torch
                if torch.cuda.is_available():
                    self.torch = torch
                    self.gpu_available = True
                    self.gpu_backend = 'torch'
                    print("GPU加速可用 (PyTorch)")
                else:
                    print("GPU不可用，使用CPU计算")
            except ImportError:
                print("未安装GPU加速库，使用CPU计算")
        except AttributeError as e:
            if "numpy.bool" in str(e):
                print("CuPy与numpy版本不兼容，尝试使用PyTorch...")
                try:
                    import torch
                    if torch.cuda.is_available():
                        self.torch = torch
                        self.gpu_available = True
                        self.gpu_backend = 'torch'
                        print("GPU加速可用 (PyTorch)")
                    else:
                        print("GPU不可用，使用CPU计算")
                except ImportError:
                    print("未安装GPU加速库，使用CPU计算")
            else:
                print(f"GPU加速检查失败: {e}")
        except Exception as e:
            print(f"GPU加速检查失败: {e}")
        
        # 缩放相关变量
        self.time_plot_zoom_level = 1.0
        self.time_plot_center = 0.5
        self.time_plot_original_xlim = None
        self.spectrum_plot_zoom_level = 1.0
        self.spectrum_plot_center = 0.5
        self.spectrum_plot_original_xlim = None
        self.min_zoom_level = 1.0           # 最小缩放级别（默认大小）
        self.max_zoom_level = 50.0          # 最大缩放级别

        # 计算结果缓存（用于避免重复计算）
        self.computation_cache = {}
        self.cache_max_size = 3  # 最多缓存3个结果

        # 性能模式设置
        self.performance_mode = False  # 是否启用性能模式
        self.performance_threshold = 3000000  # 超过300万点启用性能模式
        self.user_performance_mode = False  # 用户手动设置的性能模式

        # 文件对话框设置
        self.setup_file_dialog_options()
    
    def setup_fonts(self):
        """设置字体（1.5倍放大，与data_viewer一致）"""
        import tkinter.font as tkFont
        import platform
        
        system = platform.system()
        
        # 检测操作系统并选择合适的字体
        if system == 'Windows':
            # Windows 系统使用微软雅黑和 Segoe UI
            chinese_font = 'Microsoft YaHei'
            english_font = 'Segoe UI'
            monospace_font = 'Consolas'
        elif system == 'Linux':
            # Linux 系统使用支持中文的开源字体，按优先级排序，移除Microsoft YaHei避免警告
            chinese_font_candidates = [
                'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                'Liberation Sans', 'DejaVu Sans'
            ]
            chinese_font = self.find_available_font(chinese_font_candidates)
            english_font = 'DejaVu Sans'
            monospace_font = 'DejaVu Sans Mono'
        else:
            # 其他系统使用通用字体
            chinese_font = 'Liberation Sans'
            english_font = 'Liberation Sans'
            monospace_font = 'Liberation Mono'
        
        print(f"检测到操作系统: {system}")
        print(f"中文字体: {chinese_font}, 英文字体: {english_font}, 等宽字体: {monospace_font}")
        
        # 所有系统统一放大 1.5 倍字体
        base_font_size = int(9 * 1.5)  # 13.5 -> 13
        print(f"字体统一放大 1.5 倍，基础字体大小: {base_font_size}")
        
        # 字体配置参数（与data_viewer一致）
        self.font_config = {
            # 按钮字体 - 支持中英文
            'button_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'button_font_size': base_font_size,
            'button_font_weight': 'normal',
            
            # 标题字体
            'title_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'title_font_size': base_font_size,
            'title_font_weight': 'bold',
            
            # 状态标签字体
            'status_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'status_font_size': base_font_size,
            'status_font_weight': 'normal',
            
            # 文本框字体
            'text_font_family': monospace_font,
            'text_font_size': base_font_size,
            'text_font_weight': 'normal',
            
            # 文件列表字体（放大 1.25 倍）
            'list_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'list_font_size': int(base_font_size * 1.25),  # 13 * 1.25 = 16.25 -> 16
            'list_font_weight': 'normal',
        }
        
        # 验证字体可用性并设置备用字体
        self._validate_and_set_fonts()
        
        # 创建字体对象（与data_viewer一致）
        self.fonts = {
            'button_font': tkFont.Font(
                family=self.font_config['button_font_family'],
                size=self.font_config['button_font_size'],
                weight=self.font_config['button_font_weight']
            ),
            'title_font': tkFont.Font(
                family=self.font_config['title_font_family'],
                size=self.font_config['title_font_size'],
                weight=self.font_config['title_font_weight']
            ),
            'status_font': tkFont.Font(
                family=self.font_config['status_font_family'],
                size=self.font_config['status_font_size'],
                weight=self.font_config['status_font_weight']
            ),
            'text_font': tkFont.Font(
                family=self.font_config['text_font_family'],
                size=self.font_config['text_font_size'],
                weight=self.font_config['text_font_weight']
            ),
            'list_font': tkFont.Font(
                family=self.font_config['list_font_family'],
                size=self.font_config['list_font_size'],
                weight=self.font_config['list_font_weight']
            ),
        }
        
        # 向后兼容性：保持原有的字体变量
        self.default_font = self.fonts['button_font']
        self.large_font = self.fonts['title_font']
        self.small_font = tkFont.Font(
            family=self.font_config['button_font_family'],
            size=int(base_font_size * 0.9),  # 稍小一点的字体
            weight='normal'
        )
    
    def _validate_and_set_fonts(self):
        """验证和设置字体"""
        # 这个方法现在主要用于字体验证，实际的字体创建在后面进行
        # 可以在这里添加字体可用性检查逻辑
        available_fonts = self.get_available_fonts()
        
        # 验证中文字体
        chinese_font = self.font_config['button_font_family']
        if chinese_font not in available_fonts:
            # 尝试备用中文字体
            import platform
            current_system = platform.system()
            if current_system == 'Linux':
                fallback_chinese = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                    'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                    'Liberation Sans', 'DejaVu Sans', 'Arial'
                ]
            elif current_system == 'Windows':
                fallback_chinese = ['Microsoft YaHei', 'SimHei', 'WenQuanYi Zen Hei',
                                   'Liberation Sans', 'DejaVu Sans', 'Arial']
            else:
                fallback_chinese = ['Liberation Sans', 'DejaVu Sans', 'Arial']

            chinese_font = self.find_available_font(fallback_chinese)
        
        # 更新字体配置
        self.font_config.update({
            'button_font_family': chinese_font if self.current_language == 'zh' else self.font_config['button_font_family'],
            'title_font_family': chinese_font if self.current_language == 'zh' else self.font_config['title_font_family'],
            'status_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
            'list_font_family': chinese_font if self.current_language == 'zh' else self.font_config['list_font_family'],
        })
        
        print(f"最终选择字体 - 中文: {chinese_font}")
    
    def find_available_font(self, font_candidates):
        """从候选字体列表中找到第一个可用的字体"""
        available_fonts = self.get_available_fonts()
        for font in font_candidates:
            if font in available_fonts:
                return font
        # 如果都没找到，返回系统默认字体
        import tkinter.font as tkFont
        return tkFont.nametofont("TkDefaultFont").actual()['family']

    def get_check_symbol(self, selected):
        """根据系统环境获取合适的选择符号"""
        import platform
        if platform.system() == 'Windows':
            # Windows环境使用兼容性更好的符号
            return '√' if selected else '○'
        else:
            # Linux/Unix环境使用Unicode符号
            return '✓' if selected else '○'

    def get_available_fonts(self):
        """获取系统可用字体列表"""
        import tkinter.font as tkFont
        return sorted(tkFont.families())
    
    def on_closing(self):
        """处理窗口关闭事件"""
        try:
            # 保存配置
            self.save_config()
            # 保存设置或清理资源
            if hasattr(self, 'fig') and self.fig:
                plt.close(self.fig)
        except Exception as e:
            print(f"关闭时清理资源失败: {e}")
        finally:
            self.root.quit()
            self.root.destroy()

    def create_interface(self):
        """创建用户界面"""
        # 创建主框架
        self.main_frame = tk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要布局（左侧面板 + 右侧图表）
        self.create_main_layout()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = tk.Frame(self.main_frame, relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 根据语言设置按钮宽度
        if self.current_language == 'zh':
            btn_widths = {'file': 10, 'model': 10, 'nav': 8, 'func': 12, 'view': 8}
        else:
            btn_widths = {'file': 12, 'model': 12, 'nav': 9, 'func': 15, 'view': 9}
        
        # 左侧按钮组
        left_frame = tk.Frame(toolbar)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)
        
        # 文件操作按钮
        tk.Button(left_frame, text=self.texts['select_file'], command=self.select_file,
                 font=self.default_font, width=btn_widths['file']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(left_frame, text=self.texts['select_folder'], command=self.select_folder,
                 font=self.default_font, width=btn_widths['file']).pack(side=tk.LEFT, padx=2)
        
        # 模型选择按钮
        tk.Button(left_frame, text=self.texts['select_model'], command=self.select_and_load_model,
                 font=self.default_font, width=btn_widths['model']).pack(side=tk.LEFT, padx=5)
        
        # 导航按钮
        tk.Button(left_frame, text=self.texts['previous'], command=self.prev_file,
                 font=self.default_font, width=btn_widths['nav']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(left_frame, text=self.texts['next'], command=self.next_file,
                 font=self.default_font, width=btn_widths['nav']).pack(side=tk.LEFT, padx=2)
        
        # 中间按钮组
        middle_frame = tk.Frame(toolbar)
        middle_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=2)
        
        # 数据捕获按钮
        tk.Button(middle_frame, text=self.texts['data_capture'], command=self.capture_data,
                 font=self.default_font, width=btn_widths['func'], bg='#2196F3', fg='white').pack(side=tk.LEFT, padx=2)
        
        # 信道扫描按钮
        tk.Button(middle_frame, text=self.texts['channel_scan'], command=self.run_channel_scan,
                 font=self.default_font, width=btn_widths['func'], bg='#4CAF50', fg='white').pack(side=tk.LEFT, padx=2)
        
        # 查看数据库按钮
        tk.Button(middle_frame, text=self.texts['view_database'], command=self.view_database,
                 font=self.default_font, width=btn_widths['func']).pack(side=tk.LEFT, padx=2)
        
        # 右侧按钮组
        right_frame = tk.Frame(toolbar)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=2)
        
        # 视图切换按钮
        self.view_button = tk.Button(right_frame, text=self.texts['view_2d'], command=self.toggle_view_mode,
                                    font=self.default_font, width=btn_widths['view'])
        self.view_button.pack(side=tk.RIGHT, padx=2)
        
        # 语言切换按钮（如果需要）
        tk.Button(right_frame, text=self.texts['language_switch'], command=self.toggle_language,
                 font=self.default_font, width=btn_widths['view']).pack(side=tk.RIGHT, padx=2)
    
    def create_main_layout(self):
        """创建主要布局"""
        # 创建主要的水平分隔器
        self.main_paned = tk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL, sashrelief=tk.RAISED)
        self.main_paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板（文件列表、文件信息、分段结果）
        self.create_left_panel()
        
        # 右侧图表区域
        self.create_chart_container()
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_left_panel(self):
        """创建左侧面板"""
        # 左侧容器
        left_container = tk.Frame(self.main_paned)
        self.main_paned.add(left_container, width=450, minsize=350)
        
        # 创建可调节高度的面板系统
        self.create_resizable_panels(left_container)
    
    def create_resizable_panels(self, parent):
        """创建可调节高度的面板系统"""
        # 创建垂直分隔器
        self.left_paned = tk.PanedWindow(parent, orient=tk.VERTICAL, sashrelief=tk.RAISED)
        self.left_paned.pack(fill=tk.BOTH, expand=True)
        
        # 文件列表面板
        self.create_collapsible_panel('file_list', self.texts['file_list'], 200, self.create_file_list_content)
        
        # 文件信息面板
        self.create_collapsible_panel('file_info', self.texts['file_info'], 150, self.create_file_info_content)
        
        # 分段结果面板
        self.create_collapsible_panel('segment_results', self.texts['segment_results'], 300, self.create_segment_results_content)
    
    def create_collapsible_panel(self, panel_id, title, height, create_content_func):
        """创建可折叠的面板"""
        # 面板容器
        panel_frame = tk.Frame(self.left_paned, relief=tk.RAISED, bd=1)
        self.left_paned.add(panel_frame, height=height, minsize=100)
        
        # 标题栏
        title_frame = tk.Frame(panel_frame, bg='#f0f0f0', relief=tk.RAISED, bd=1)
        title_frame.pack(fill=tk.X)
        
        # 标题文本
        title_label = tk.Label(title_frame, text=title, font=self.large_font, bg='#f0f0f0')
        title_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 折叠/展开按钮
        collapse_btn = tk.Button(title_frame, text="−", font=self.default_font, width=3,
                                command=lambda: self.toggle_panel_collapse(panel_id))
        collapse_btn.pack(side=tk.RIGHT, padx=2, pady=1)
        
        # 内容区域
        content_frame = tk.Frame(panel_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # 调用内容创建函数
        create_content_func(content_frame)
        
        # 存储面板引用
        if not hasattr(self, 'panels'):
            self.panels = {}
        self.panels[panel_id] = {
            'panel_frame': panel_frame,
            'title_label': title_label,
            'collapse_btn': collapse_btn,
            'content_frame': content_frame,
            'original_height': height
        }
        
        # 初始化折叠状态
        self.panel_collapsed[panel_id] = False
    
    def create_file_list_content(self, parent):
        """创建文件列表内容"""
        # 文件列表框
        list_frame = tk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建带滚动条的列表框
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.file_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, font=self.small_font)
        self.file_listbox.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.file_listbox.yview)
        
        # 绑定选择事件
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
    
    def create_file_info_content(self, parent):
        """创建文件信息内容"""
        # 创建文本显示区域
        self.info_text = tk.Text(parent, wrap=tk.WORD, font=self.small_font, state=tk.DISABLED)
        
        # 添加滚动条
        info_scrollbar = tk.Scrollbar(parent, command=self.info_text.yview)
        self.info_text.config(yscrollcommand=info_scrollbar.set)
        
        # 布局
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_segment_results_content(self, parent):
        """创建分段结果内容"""
        # 根据语言设置按钮宽度
        if self.current_language == 'zh':
            btn_widths = {'clear': 8, 'add_db': 12, 'skip': 10, 'select': 6, 'delete': 8}
        else:
            btn_widths = {'clear': 8, 'add_db': 15, 'skip': 10, 'select': 10, 'delete': 12}
        
        # 顶部按钮区域
        button_frame = tk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 操作按钮 - 第一行
        top_buttons = tk.Frame(button_frame)
        top_buttons.pack(fill=tk.X, pady=(0, 2))
        
        tk.Button(top_buttons, text=self.texts['clear_results'], command=self.clear_segment_results,
                 font=self.small_font, width=btn_widths['clear']).pack(side=tk.LEFT, padx=2)
        
        self.add_to_db_button = tk.Button(top_buttons, text=self.texts['add_to_db'], command=self.add_segments_to_db_action,
                 font=self.small_font, width=btn_widths['add_db'], bg='#2196F3', fg='white', state=tk.DISABLED)
        self.add_to_db_button.pack(side=tk.LEFT, padx=2)
        
        self.skip_button = tk.Button(top_buttons, text=self.texts['skip_file'], command=self.skip_to_next_file_action,
                 font=self.small_font, width=btn_widths['skip'], bg='#FF9800', fg='white')
        self.skip_button.pack(side=tk.LEFT, padx=2)
        
        # 选择管理按钮 - 第二行
        select_buttons = tk.Frame(button_frame)
        select_buttons.pack(fill=tk.X, pady=(0, 5))
        
        tk.Button(select_buttons, text=self.texts['select_all'], command=self.select_all_segments,
                 font=self.small_font, width=btn_widths['select']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(select_buttons, text=self.texts['deselect_all'], command=self.deselect_all_segments,
                 font=self.small_font, width=btn_widths['select']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(select_buttons, text=self.texts['delete_selected'], command=self.delete_selected_segments,
                 font=self.small_font, width=btn_widths['delete'], bg='#f44336', fg='white').pack(side=tk.LEFT, padx=2)
        
        # 分段结果 - 使用 TreeView 替代 Text
        tree_frame = tk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建TreeView
        columns = ('select', 'segment_id', 'position', 'center_freq', 'bandwidth', 'duration', 'snr')
        self.segments_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=10)

        # 配置TreeView字体 - 增大字体以便更好地查看
        tree_font_size = int(self.font_config['button_font_size'] * 1.1)  # 比基础字体稍大
        tree_font = (self.font_config['button_font_family'], tree_font_size)

        # 创建并应用TreeView样式
        style = ttk.Style()
        style.configure("Segments.Treeview",
                       font=tree_font,
                       rowheight=int(tree_font_size * 1.8))  # 增加行高
        style.configure("Segments.Treeview.Heading",
                       font=(self.font_config['button_font_family'], tree_font_size, 'bold'))

        # 应用样式到TreeView
        self.segments_tree.configure(style="Segments.Treeview")
        
        # 设置列标题
        if self.current_language == 'zh':
            self.segments_tree.heading('select', text='选择')
            self.segments_tree.heading('segment_id', text='编号')
            self.segments_tree.heading('position', text='位置')
            self.segments_tree.heading('center_freq', text='中心频率(MHz)')
            self.segments_tree.heading('bandwidth', text='带宽(MHz)')
            self.segments_tree.heading('duration', text='持续时间(ms)')
            self.segments_tree.heading('snr', text='信噪比(dB)')
        else:
            self.segments_tree.heading('select', text='Select')
            self.segments_tree.heading('segment_id', text='ID')
            self.segments_tree.heading('position', text='Position')
            self.segments_tree.heading('center_freq', text='Center Freq(MHz)')
            self.segments_tree.heading('bandwidth', text='Bandwidth(MHz)')
            self.segments_tree.heading('duration', text='Duration(ms)')
            self.segments_tree.heading('snr', text='SNR(dB)')
        
        # 设置列宽 - 根据字体大小调整，使内容更易读
        font_scale = tree_font_size / 12  # 基于12号字体的缩放比例
        self.segments_tree.column('select', width=int(80 * font_scale))
        self.segments_tree.column('segment_id', width=int(80 * font_scale))
        self.segments_tree.column('position', width=int(160 * font_scale))
        self.segments_tree.column('center_freq', width=int(140 * font_scale))
        self.segments_tree.column('bandwidth', width=int(130 * font_scale))
        self.segments_tree.column('duration', width=int(130 * font_scale))
        self.segments_tree.column('snr', width=int(110 * font_scale))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.segments_tree.yview)
        self.segments_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.segments_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.segments_tree.bind('<Button-1>', self.on_segment_click)
        self.segments_tree.bind('<Double-1>', self.on_segment_double_click)
        self.segments_tree.bind('<Button-3>', self.on_segment_right_click)  # 右键菜单
        
        # 存储分段选择状态
        self.segment_selected = {}
    
    def create_segment_context_menu(self):
        """(此功能已移除)"""
        pass
    
    def show_segment_context_menu(self, event):
        """(此功能已移除)"""
        pass
    
    def toggle_panel_collapse(self, panel_id):
        """切换面板折叠状态"""
        if panel_id not in self.panels:
            return
        
        panel = self.panels[panel_id]
        is_collapsed = self.panel_collapsed.get(panel_id, False)
        
        if is_collapsed:
            # 展开面板
            panel['content_frame'].pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
            panel['collapse_btn'].config(text="−")
            self.panel_collapsed[panel_id] = False
        else:
            # 折叠面板
            panel['content_frame'].pack_forget()
            panel['collapse_btn'].config(text="+")
            self.panel_collapsed[panel_id] = True
    
    def create_chart_container(self):
        """创建图表容器"""
        # 右侧图表容器
        chart_container = tk.Frame(self.main_paned)
        self.main_paned.add(chart_container, minsize=600)
        
        # 图表标题
        self.chart_title_label = tk.Label(chart_container, text=self.texts['signal_analysis_charts'], 
                              font=self.large_font)
        self.chart_title_label.pack(pady=(5, 10))
        
        # matplotlib 图表
        import matplotlib
        matplotlib.use('TkAgg')
        
        from matplotlib.figure import Figure
        
        self.fig = Figure(figsize=(12, 8), dpi=100, facecolor='white')
        
        # 创建子图
        self.create_subplots()
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, chart_container)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 绑定鼠标滚轮事件进行缩放（使用matplotlib的事件系统）
        self.canvas.mpl_connect('scroll_event', self.on_mouse_scroll)
    
    def create_subplots(self):
        """创建子图"""
        self.fig.clear()
        
        if self.is_3d_mode:
            # 3D 模式：2个子图垂直排列，都是3D图
            self.axes = []
            self.axes.append(self.fig.add_subplot(2, 1, 1, projection='3d'))  # 时频图3D
            self.axes.append(self.fig.add_subplot(2, 1, 2, projection='3d'))  # 频谱图3D
        else:
            # 2D 模式：4个子图垂直排列（与data_viewer一致）
            self.axes = []
            self.axes.append(self.fig.add_subplot(4, 1, 1))  # 时域信号点值图
            self.axes.append(self.fig.add_subplot(4, 1, 2))  # 时域信号图
            self.axes.append(self.fig.add_subplot(4, 1, 3))  # 频谱图
            self.axes.append(self.fig.add_subplot(4, 1, 4))  # 时频图
        
        # 设置子图标题
        self.update_chart_titles()
        
        # 调整子图间距
        self.fig.tight_layout(pad=2.0)
    
    def update_chart_titles(self):
        """更新图表标题"""
        if hasattr(self, 'axes') and self.axes:
            if self.is_3d_mode:
                # 3D模式：只有2个图表
                titles = [
                    self.texts['time_frequency'],
                    self.texts['frequency_spectrum']
                ]
            else:
                # 2D模式：4个图表
                titles = [
                    self.texts['time_domain_points'],
                    self.texts['time_domain_signal'],
                    self.texts['frequency_spectrum'],
                    self.texts['time_frequency']
                ]
            
            for i, ax in enumerate(self.axes):
                if i < len(titles):
                    ax.set_title(titles[i], fontsize=10, pad=10)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.main_frame, relief=tk.SUNKEN, bd=1)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态标签
        self.status_label = tk.Label(status_frame, text=self.texts['ready'],
                                    font=self.small_font, anchor='w')
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2, fill=tk.X, expand=True)

        # 进度条（初始隐藏）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                          maximum=100, length=200, mode='determinate')
        # 进度条初始不显示，需要时才pack

    def show_progress_bar(self, show=True):
        """显示或隐藏进度条"""
        if show:
            self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        else:
            self.progress_bar.pack_forget()

    def update_progress(self, value, text=None):
        """更新进度条和状态文本"""
        self.progress_var.set(value)
        if text:
            self.status_label.config(text=text)
        self.root.update_idletasks()

    def setup_file_dialog_options(self):
        """设置文件对话框选项，特别是Linux下的大小"""
        import platform
        if platform.system() == 'Linux':
            # 在Linux下设置文件对话框的默认大小
            try:
                # 设置环境变量来影响GTK文件对话框的大小
                import os
                os.environ['GTK_FILE_CHOOSER_DEFAULT_WIDTH'] = '1000'
                os.environ['GTK_FILE_CHOOSER_DEFAULT_HEIGHT'] = '700'
                # 设置其他可能的环境变量
                os.environ['ZENITY_WIDTH'] = '1000'
                os.environ['ZENITY_HEIGHT'] = '700'
                print("已设置Linux文件对话框大小: 1000x700")
            except Exception as e:
                print(f"设置文件对话框大小失败: {e}")

        # 设置tkinter文件对话框的选项
        try:
            self.root.option_add('*TkFDialog*foreground', 'black')
            self.root.option_add('*TkFDialog*selectBackground', '#0078d4')
            if platform.system() == 'Linux':
                # 尝试设置Linux下的对话框大小
                self.root.option_add('*TkFDialog*width', '1000')
                self.root.option_add('*TkFDialog*height', '700')
        except Exception as e:
            print(f"设置tkinter对话框选项失败: {e}")

    def enhanced_file_dialog(self, dialog_type, title, filetypes=None, dialog_purpose='file', **kwargs):
        """增强的文件对话框，支持合适的显示尺寸

        Args:
            dialog_type: 'open' 或 'directory'
            title: 对话框标题
            filetypes: 文件类型过滤器
            dialog_purpose: 对话框用途 ('file', 'folder', 'model')，用于选择合适的初始目录
            **kwargs: 其他参数
        """
        import platform

        # 根据对话框用途设置初始目录
        if 'initialdir' not in kwargs:
            import os
            if dialog_purpose == 'model':
                kwargs['initialdir'] = getattr(self, 'last_model_dir', os.getcwd())
            elif dialog_purpose == 'folder':
                kwargs['initialdir'] = getattr(self, 'last_folder_dir', os.getcwd())
            else:  # 默认为文件选择
                kwargs['initialdir'] = getattr(self, 'last_file_dir', os.getcwd())

        if platform.system() == 'Linux':
            # 在Linux下使用zenity创建可控尺寸的文件选择对话框
            try:
                import subprocess
                import os

                # 设置初始目录
                initial_dir = kwargs.get('initialdir', os.getcwd())

                if dialog_type == 'open':
                    # 构建zenity文件选择命令
                    cmd = [
                        'zenity', '--file-selection',
                        '--title=' + title,
                        '--width=700',
                        '--height=500'
                    ]

                    # 设置初始目录，确保路径以/结尾
                    if initial_dir and os.path.exists(initial_dir):
                        if not initial_dir.endswith('/'):
                            initial_dir += '/'
                        cmd.append('--filename=' + initial_dir)

                    if filetypes:
                        for name, pattern in filetypes:
                            if pattern != "*.*":
                                patterns = pattern.split()
                                filter_str = f'{name} | ' + ' '.join(patterns)
                                cmd.extend(['--file-filter=' + filter_str])

                    # 执行zenity命令
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        selected_path = result.stdout.strip()
                        # 根据用途更新相应的目录
                        if dialog_purpose == 'model':
                            self.last_model_dir = os.path.dirname(selected_path)
                        elif dialog_purpose == 'folder':
                            self.last_folder_dir = os.path.dirname(selected_path)
                        else:
                            self.last_file_dir = os.path.dirname(selected_path)
                        # 保持向后兼容
                        self.last_selected_dir = os.path.dirname(selected_path)
                        # 保存配置
                        self.save_config()
                        return selected_path
                    else:
                        return None

                elif dialog_type == 'directory':
                    # 构建zenity目录选择命令
                    cmd = [
                        'zenity', '--file-selection',
                        '--directory',
                        '--title=' + title,
                        '--width=700',
                        '--height=500'
                    ]

                    # 设置初始目录，确保路径以/结尾
                    if initial_dir and os.path.exists(initial_dir):
                        if not initial_dir.endswith('/'):
                            initial_dir += '/'
                        cmd.append('--filename=' + initial_dir)

                    # 执行zenity命令
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        selected_path = result.stdout.strip()
                        # 根据用途更新相应的目录
                        if dialog_purpose == 'folder':
                            self.last_folder_dir = selected_path
                        else:
                            self.last_file_dir = selected_path
                        # 保持向后兼容
                        self.last_selected_dir = selected_path
                        # 保存配置
                        self.save_config()
                        return selected_path
                    else:
                        return None
                else:
                    return None

            except Exception as e:
                print(f"zenity文件对话框错误: {e}")
                # 如果zenity失败，回退到标准对话框
                if dialog_type == 'open':
                    return filedialog.askopenfilename(
                        title=title,
                        filetypes=filetypes or [("所有文件", "*.*")],
                        **kwargs
                    )
                elif dialog_type == 'directory':
                    return filedialog.askdirectory(
                        title=title,
                        **kwargs
                    )
        else:
            # Windows和其他系统使用标准对话框
            if dialog_type == 'open':
                result = filedialog.askopenfilename(
                    title=title,
                    filetypes=filetypes or [("所有文件", "*.*")],
                    **kwargs
                )
                # 根据用途更新相应的目录
                if result:
                    import os
                    if dialog_purpose == 'model':
                        self.last_model_dir = os.path.dirname(result)
                    elif dialog_purpose == 'folder':
                        self.last_folder_dir = os.path.dirname(result)
                    else:
                        self.last_file_dir = os.path.dirname(result)
                    # 保持向后兼容
                    self.last_selected_dir = os.path.dirname(result)
                    # 保存配置
                    self.save_config()
                return result
            elif dialog_type == 'directory':
                result = filedialog.askdirectory(
                    title=title,
                    **kwargs
                )
                # 根据用途更新相应的目录
                if result:
                    if dialog_purpose == 'folder':
                        self.last_folder_dir = result
                    else:
                        self.last_file_dir = result
                    # 保持向后兼容
                    self.last_selected_dir = result
                    # 保存配置
                    self.save_config()
                return result
    
    def init_plots(self):
        """初始化图表"""
        if hasattr(self, 'axes') and self.axes:
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, self.texts['please_select'], 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=12, color='gray')
            
            self.update_chart_titles()
            self.fig.tight_layout()
            self.canvas.draw()
    
    def toggle_view_mode(self):
        """切换视图模式"""
        self.is_3d_mode = not self.is_3d_mode
        
        # 更新按钮文本
        if self.is_3d_mode:
            self.view_button.config(text=self.texts['view_3d'])
        else:
            self.view_button.config(text=self.texts['view_2d'])
        
        # 重新创建子图
        self.create_subplots()
        
        # 如果有当前信号数据，重新显示
        if self.current_signal is not None:
            self.display_signal()
        else:
            self.init_plots()
    
    def toggle_language(self):
        """切换语言"""
        if self.current_language == 'zh':
            self.current_language = 'en'
            self.texts = self.texts_en
        else:
            self.current_language = 'zh'
            self.texts = self.texts_zh
        
        # 重新设置字体以适应新语言
        self.setup_fonts()
        
        # 重新创建界面以适应新语言的按钮宽度
        self.recreate_interface()
        
        # 更新界面文本
        self.update_interface_texts()
    
    def update_interface_texts(self):
        """更新界面文本"""
        # 更新窗口标题
        self.root.title(self.texts['title'])
        
        # 更新状态栏
        if hasattr(self, 'status_label'):
            current_status = self.status_label.cget('text')
            # 如果当前是默认状态，更新为新语言的ready文本
            if current_status in ['就绪', 'Ready']:
                self.status_label.config(text=self.texts['ready'])
        
        # 更新分段结果TreeView表头
        if hasattr(self, 'segments_tree'):
            # 更新表头文本
            if self.current_language == 'zh':
                self.segments_tree.heading('select', text='选择')
                self.segments_tree.heading('segment_id', text='编号')
                self.segments_tree.heading('position', text='位置')
                self.segments_tree.heading('center_freq', text='中心频率(MHz)')
                self.segments_tree.heading('bandwidth', text='带宽(MHz)')
                self.segments_tree.heading('duration', text='持续时间(ms)')
                self.segments_tree.heading('snr', text='信噪比(dB)')
            else:
                self.segments_tree.heading('select', text='Select')
                self.segments_tree.heading('segment_id', text='ID')
                self.segments_tree.heading('position', text='Position')
                self.segments_tree.heading('center_freq', text='Center Freq(MHz)')
                self.segments_tree.heading('bandwidth', text='Bandwidth(MHz)')
                self.segments_tree.heading('duration', text='Duration(ms)')
                self.segments_tree.heading('snr', text='SNR(dB)')
        
        # 更新图表标题
        self.update_chart_titles()
        if hasattr(self, 'canvas'):
            self.canvas.draw()
        
        # 更新图表容器的标题
        if hasattr(self, 'chart_title_label'):
            self.chart_title_label.config(text=self.texts['signal_analysis_charts'])
        
        # 更新图表中的提示文本
        if hasattr(self, 'axes') and self.axes and self.current_signal is None:
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, self.texts['please_select'], 
                       horizontalalignment='center', verticalalignment='center',
                       transform=ax.transAxes, fontsize=12, color='gray')
            self.update_chart_titles()
            self.fig.tight_layout()
            self.canvas.draw()
    
    def recreate_interface(self):
        """重新创建界面以适应语言切换"""
        # 保存当前数据
        current_files = self.current_files[:]
        current_index = self.current_index
        current_signal = self.current_signal
        current_metadata = self.current_metadata.copy()
        segment_results = self.segment_results[:]
        segment_fs = self.segment_fs
        arcface_model = self.arcface_model
        
        # 清空主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 重新创建界面
        self.create_toolbar()
        self.create_main_layout()
        
        # 恢复数据
        self.current_files = current_files
        self.current_index = current_index  
        self.current_signal = current_signal
        self.current_metadata = current_metadata
        self.segment_results = segment_results
        self.segment_fs = segment_fs
        self.arcface_model = arcface_model
        
        # 恢复文件列表显示
        if self.current_files:
            self.update_file_list()
            if self.current_signal is not None:
                self.display_file_info(self.current_files[self.current_index])
        
        # 恢复分段结果显示
        if self.segment_results:
            self.update_segment_results_display()
        
        # 恢复图表显示
        if self.current_signal is not None:
            self.display_signal()
        else:
            self.init_plots()

    # =================================================================================
    # 文件操作功能
    # =================================================================================
    
    def select_file(self):
        """选择单个文件"""
        filetypes = [
            ('信号数据文件', '*.bvsp *.dat *.hdfv'),
            ('BVSP文件', '*.bvsp'),
            ('DAT文件', '*.dat'),
            ('HDF5文件', '*.hdfv'),
            ('所有文件', '*.*')
        ]

        file_path = self.enhanced_file_dialog(
            'open',
            title=self.texts['select_file'],
            filetypes=filetypes,
            dialog_purpose='file'
        )

        if file_path:
            self.current_files = [file_path]
            self.current_index = 0
            self.update_file_list()
            self.load_current_file()
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = self.enhanced_file_dialog(
            'directory',
            title=self.texts['select_folder'],
            dialog_purpose='folder'
        )

        if folder_path:
            # 搜索支持的文件格式
            supported_extensions = ['.bvsp', '.dat', '.hdfv']
            files = []

            for ext in supported_extensions:
                pattern = f"*{ext}"
                # 只使用 rglob 进行递归搜索，避免重复
                files.extend(Path(folder_path).rglob(pattern))

            # 转换为字符串路径，去重并排序
            files = sorted(list(set([str(f) for f in files])))

            if files:
                self.current_files = files
                self.current_index = 0
                self.update_file_list()
                self.load_current_file()
            else:
                ScaledMessageBox.showwarning(self.texts['warning'], self.texts['no_files_found'],
                                           parent=self.root, language=self.current_language)
    
    def update_file_list(self):
        """更新文件列表显示"""
        if hasattr(self, 'file_listbox'):
            self.file_listbox.delete(0, tk.END)

            for i, file_path in enumerate(self.current_files):
                filename = os.path.basename(file_path)
                marker = "● " if i == self.current_index else "  "
                self.file_listbox.insert(tk.END, f"{marker}{filename}")

    def update_file_list_markers(self, old_index, new_index):
        """只更新文件列表的标记，避免重新构建整个列表"""
        if hasattr(self, 'file_listbox') and self.current_files:
            # 更新旧的选中项，移除标记
            if 0 <= old_index < len(self.current_files):
                old_filename = os.path.basename(self.current_files[old_index])
                self.file_listbox.delete(old_index)
                self.file_listbox.insert(old_index, f"  {old_filename}")

            # 更新新的选中项，添加标记
            if 0 <= new_index < len(self.current_files):
                new_filename = os.path.basename(self.current_files[new_index])
                self.file_listbox.delete(new_index)
                self.file_listbox.insert(new_index, f"● {new_filename}")
                # 确保新选中的项可见
                self.file_listbox.see(new_index)
    
    def prev_file(self):
        """上一个文件"""
        if self.current_files and self.current_index > 0:
            self.current_index -= 1
            self.update_file_list()
            self.load_current_file()
    
    def next_file(self):
        """下一个文件"""
        if self.current_files and self.current_index < len(self.current_files) - 1:
            self.current_index += 1
            self.update_file_list()
            self.load_current_file()
    
    def on_file_select(self, event):
        """文件列表选择事件"""
        selection = self.file_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.current_files):
                old_index = self.current_index
                self.current_index = index
                # 只更新标记，不重新构建整个列表以避免跳转
                self.update_file_list_markers(old_index, index)
                self.load_current_file()
    
    def load_current_file(self):
        """加载当前选中的文件"""
        if not self.current_files or self.current_index >= len(self.current_files):
            return

        file_path = self.current_files[self.current_index]
        self.status_label.config(text=f"{self.texts['loading']}...")
        self.root.update()

        # 在后台线程加载文件
        def load_thread():
            try:
                print(f"\n=== 开始加载文件 ===")
                print(f"文件路径: {file_path}")
                print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")

                success, signal_data, metadata, error_msg = load_signal_data(file_path)

                if success:
                    # 验证加载的数据
                    if signal_data is None:
                        raise ValueError("信号数据为空")
                    if len(signal_data) == 0:
                        raise ValueError("信号数据长度为0")

                    # 检查信号长度是否异常（小于10个点）
                    if len(signal_data) < 10:
                        warning_msg = f"警告: 信号数据异常短 ({len(signal_data)}点)，可能导致处理问题"
                        print(warning_msg)
                        # 存储警告信息，稍后显示
                        self.pending_data_warning = {
                            'title': "数据异常" if self.current_language == 'zh' else "Data Anomaly",
                            'message': warning_msg
                        }

                    print(f"信号加载成功:")
                    print(f"  信号长度: {len(signal_data)}")
                    print(f"  信号类型: {type(signal_data)}")
                    print(f"  元数据: {metadata}")
                    print("=" * 30)

                    self.current_signal = signal_data
                    self.current_metadata = metadata

                    # 更新界面（在主线程中执行）
                    self.root.after(0, lambda: self.on_file_loaded_success(file_path))
                else:
                    self.root.after(0, lambda: self.on_file_loaded_error(error_msg))

            except Exception as e:
                import traceback
                error_detail = f"文件加载异常: {str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
                print(f"\n=== 文件加载异常 ===")
                print(error_detail)
                print("=" * 50)
                self.root.after(0, lambda: self.on_file_loaded_error(error_detail))

        threading.Thread(target=load_thread, daemon=True).start()

    def load_current_file_async(self):
        """异步加载当前文件（用于文件夹加载）"""
        if not self.current_files or self.current_index >= len(self.current_files):
            return

        # 获取当前文件路径
        file_path = self.current_files[self.current_index]

        # 更新状态栏
        self.status_label.config(text=f"{self.texts['loading']}: {os.path.basename(file_path)}")

        # 延迟一点时间再开始加载，让UI有时间更新
        self.root.after(100, lambda: self.load_current_file())
    
    def on_file_loaded_success(self, file_path):
        """文件加载成功回调"""
        self.status_label.config(text=f"{self.texts['loaded']}: {os.path.basename(file_path)}")

        # 先清空之前的分段结果，避免在新信号显示时出现旧的分段标记
        self.clear_segment_results()

        # 更新文件信息
        self.display_file_info(file_path)

        # 恢复显示信号的功能
        self.display_signal()
    
    def on_file_loaded_error(self, error_msg):
        """文件加载失败回调"""
        self.status_label.config(text=self.texts['load_failed'])

        # 输出详细错误信息到命令行
        print(f"\n=== 文件加载错误详情 ===")
        print(f"错误信息: {error_msg}")
        if hasattr(self, 'current_files') and self.current_files:
            current_file = self.current_files[self.current_index] if self.current_index < len(self.current_files) else "未知文件"
            print(f"文件路径: {current_file}")
        print("=" * 50)

        # 显示错误弹窗
        detailed_msg = f"{self.texts['load_file_failed']}:\n\n{error_msg}\n\n详细错误信息已输出到命令行，请查看控制台。"
        ScaledMessageBox.showerror(self.texts['error'], detailed_msg,
                                  parent=self.root, language=self.current_language)
    
    def display_file_info(self, file_path):
        """显示文件信息"""
        if not hasattr(self, 'info_text'):
            return
        
        info_text = f"{self.texts['file_path']}: {file_path}\n"
        info_text += f"{self.texts['file_size']}: {self.current_metadata.get('file_size', 0)/1024/1024:.2f} MB\n"
        info_text += f"{self.texts['sample_rate']}: {self.current_metadata.get('fs', 0)/1e6:.2f} MHz\n"
        info_text += f"{self.texts['center_frequency']}: {self.current_metadata.get('fc', 0)/1e6:.2f} MHz\n"
        info_text += f"{self.texts['signal_length']}: {self.current_metadata.get('signal_length', 0)} {self.texts['samples_unit']}\n"
        info_text += f"{self.texts['duration']}: {self.current_metadata.get('duration', 0):.3f} 秒\n"
        
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete('1.0', tk.END)
        self.info_text.insert(tk.END, info_text)
        self.info_text.config(state=tk.DISABLED)
    
    # =================================================================================
    # 信号显示功能
    # =================================================================================
    
    def display_signal(self):
        """显示信号图表"""
        if self.current_signal is None:
            return

        # 检查信号数据是否异常
        signal_length = len(self.current_signal)
        if signal_length < 10:
            # 对于异常短的信号，显示简化的信息而不进行复杂的信号处理
            self.display_minimal_signal_info()
            return

        # 根据当前视图模式调用相应的显示函数
        if self.is_3d_mode:
            self.display_signal_3d()
        else:
            self.display_signal_2d()

    def display_minimal_signal_info(self):
        """显示异常短信号的基本信息"""
        try:
            # 清空所有图表
            for ax in self.axes:
                ax.clear()

            signal_length = len(self.current_signal)
            metadata = self.current_metadata or {}

            # 在第一个图表中显示信号信息
            ax = self.axes[0]
            info_text = f"信号数据异常短\n"
            info_text += f"信号长度: {signal_length} 点\n"
            info_text += f"采样率: {metadata.get('fs', 'N/A')} Hz\n"
            info_text += f"中心频率: {metadata.get('fc', 'N/A')} Hz\n"
            info_text += f"带宽: {metadata.get('bw', 'N/A')} Hz\n"
            info_text += f"\n无法进行正常的信号处理\n建议检查数据采集过程"

            ax.text(0.5, 0.5, info_text, ha='center', va='center',
                   transform=ax.transAxes, fontsize=12,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
            ax.set_title("信号数据异常")

            # 在其他图表中显示提示信息
            for i, ax in enumerate(self.axes[1:], 1):
                ax.text(0.5, 0.5, "数据不足\n无法显示", ha='center', va='center',
                       transform=ax.transAxes, fontsize=10)
                ax.set_title(f"图表 {i+1} - 数据不足")

            # 更新画布
            plt.tight_layout()
            self.canvas.draw()

            # 更新状态栏
            self.status_label.config(text=f"信号异常: 仅 {signal_length} 点数据")

        except Exception as e:
            print(f"显示异常信号信息失败: {e}")
            # 显示错误信息
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, f'显示失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes, fontsize=10)
                ax.set_title('显示错误')

            plt.tight_layout()
            self.canvas.draw()
    
    def display_signal_2d(self):
        """
        显示 2D 信号分析图表（完整的data_viewer实现）
        
        该方法实现了四种信号分析图表的并行计算和显示：
        1. 时域信号点值图 - 支持滚轮缩放交互
        2. 时域信号图 - 以时间为 X 轴的连续信号波形
        3. 频谱图 - FFT 频域分析，严格按 MATLAB 方式计算
        4. 时频图 - STFT 时频域分析，支持动态参数调整
        """
        if self.is_computing:
            return  # 如果正在计算，避免重复启动
        
        # 重置缩放相关变量（新数据加载时）
        self.time_plot_zoom_level = 1.0
        self.time_plot_center = 0.5
        self.time_plot_original_xlim = None
        self.spectrum_plot_zoom_level = 1.0
        self.spectrum_plot_center = 0.5
        self.spectrum_plot_original_xlim = None
        
        self.is_computing = True
        
        try:
            # 显示进度条并更新状态显示
            self.show_progress_bar(True)
            self.update_progress(0, f"{self.texts['preparing_data']}...")
            
            # 清空所有子图
            for ax in self.axes:
                ax.clear()

            # 确保分段结果已清空（防止显示旧的分段标记）
            if not hasattr(self, '_clearing_segments'):
                self.segment_results = []
                self.segment_fs = None

            # 获取信号数据和元数据
            sig = self.current_signal
            fs = self.current_metadata.get('fs', 1e6)
            fc = self.current_metadata.get('fc', 2.4e9)
            
            signal_i = np.real(sig)  # I路信号
            signal_q = np.imag(sig)  # Q路信号
            N_sig = len(sig)

            # 检查是否需要启用性能模式
            self.performance_mode = N_sig > self.performance_threshold
            if self.performance_mode:
                print(f"启用性能模式: 信号长度 {N_sig:,} > {self.performance_threshold:,}")
                self.update_progress(10, f"准备数据... (性能模式: {N_sig:,}点)")
            else:
                self.update_progress(10, "准备数据...")

            # 智能抽样：根据信号长度动态调整显示点数
            if N_sig > 5000000:  # 超过500万点
                max_display_points = 50000   # 5万点
            elif N_sig > 2000000:  # 超过200万点
                max_display_points = 80000   # 8万点
            else:
                max_display_points = 100000  # 10万点

            try:
                indices, step = self.create_smart_sampling_indices(N_sig, max_display_points)

                # 验证索引有效性
                if len(indices) == 0:
                    raise ValueError("抽样索引为空")
                if np.max(indices) >= N_sig:
                    print(f"警告: 索引越界，最大索引 {np.max(indices)} >= 信号长度 {N_sig}")
                    # 修正索引
                    indices = indices[indices < N_sig]
                    if len(indices) == 0:
                        indices = np.array([0])

                print(f"抽样信息: 原始长度={N_sig}, 抽样点数={len(indices)}, 步长={step}")
                print(f"索引范围: [{np.min(indices)}, {np.max(indices)}]")

                # 抽样数据
                sig_display = sig[indices]
                signal_i_display = signal_i[indices]
                signal_q_display = signal_q[indices]
                N_display = len(indices)

            except Exception as e:
                print(f"抽样过程出错: {e}")
                # 使用简单抽样作为备选方案
                step = max(1, N_sig // max_display_points)
                indices = np.arange(0, N_sig, step)
                sig_display = sig[indices]
                signal_i_display = signal_i[indices]
                signal_q_display = signal_q[indices]
                N_display = len(indices)
            
            # 更新进度
            self.update_progress(20, f"{self.texts['data_sampling_completed']}: {N_display}/{N_sig} {self.texts['points_unit']}")

            # 启动异步计算任务 - 使用原始信号确保准确性
            fft_future = self.compute_fft_async(sig, fs, fc)
            stft_future = self.compute_spectrogram_async(sig, fs, fc)
            
            # 1. 时域信号点值图 - 优化绘图性能
            self.update_progress(30, "绘制时域信号...")
            ax1 = self.axes[0]
            # X轴转换为1e5单位
            indices_1e5 = indices / 1e5

            # 使用更高效的绘图方法
            ax1.plot(indices_1e5, signal_i_display, 'b-', linewidth=0.5, rasterized=True)
            ax1.plot(indices_1e5, signal_q_display, 'r-', linewidth=0.5, rasterized=True)
            ax1.set_title(self.texts['time_domain_points'])
            ax1.set_xlabel(self.texts['time_points_unit'])
            ax1.set_ylabel(self.texts['signal_voltage'])
            ax1.grid(True, alpha=0.3)
            
            # 设置y轴范围（自适应计算，优先整百或半百刻度）
            y_min, y_max = self.calculate_adaptive_ylim(signal_i_display, signal_q_display)
            ax1.set_ylim(y_min, y_max)
            
            # 设置X轴范围和刻度（以1e5为单位）
            x_max_1e5 = (N_sig - 1) / 1e5
            ax1.set_xlim(0, x_max_1e5)
            
            # 设置刻度（与MATLAB对齐：每0.5×10^5一个刻度）
            tick_step = 0.5  # MATLAB对齐：步长为0.5
            
            x_ticks = np.arange(0, x_max_1e5 + tick_step, tick_step)
            ax1.set_xticks(x_ticks)
            # 根据刻度值决定显示格式：整数显示为整数，小数显示为小数
            ax1.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in x_ticks])
            
            # 2. 时域信号图（立即绘制，无需等待）
            ax2 = self.axes[1]
            # 计算实际的时间轴（严格按照MATLAB的计算方式）
            # MATLAB: signal_time = (0:N_sig-1)*(1/wb_fs); signal_time_ms = signal_time*1000;
            signal_time_start = 0
            signal_time_end = (N_sig - 1) / fs * 1000  # 完整数据的时间范围(ms)
            signal_time_display = indices / fs * 1000  # 显示点对应的实际时间
            
            ax2.plot(signal_time_display, signal_i_display, 'b-', linewidth=0.5)
            ax2.plot(signal_time_display, signal_q_display, 'r-', linewidth=0.5)
            ax2.set_title(self.texts['time_domain_signal'])
            ax2.set_xlabel(self.texts['time_ms'])
            ax2.set_ylabel(self.texts['voltage_v'])
            ax2.grid(True, alpha=0.3)
            
            # 设置y轴范围
            y_min, y_max = self.calculate_adaptive_ylim(signal_i_display, signal_q_display)
            ax2.set_ylim(y_min, y_max)
            
            # 设置X轴范围为完整的时间范围
            ax2.set_xlim(signal_time_start, signal_time_end)
            
            # 设置时域信号图的x轴刻度
            if signal_time_end > 0:
                # 根据时间范围设置合适的刻度间隔
                if signal_time_end <= 5:
                    time_tick_step = 1  # 0-5ms：每1ms一个刻度
                elif signal_time_end <= 20:
                    time_tick_step = 2  # 5-20ms：每2ms一个刻度
                elif signal_time_end <= 50:
                    time_tick_step = 5  # 20-50ms：每5ms一个刻度
                else:
                    time_tick_step = 10  # >50ms：每10ms一个刻度
                
                time_ticks = np.arange(0, signal_time_end + time_tick_step, time_tick_step)
                ax2.set_xticks(time_ticks)
                ax2.set_xticklabels([f'{int(tick)}' for tick in time_ticks])
            
            # 更新状态
            self.status_label.config(text=f"{self.texts['time_plot_completed_calculating_spectrum']}...")
            self.root.update()
            
            # 等待FFT计算完成并绘制频谱图
            try:
                freqs_mhz, fftdata = fft_future.result(timeout=30)  # 30秒超时
                
                ax3 = self.axes[2]
                ax3.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)
                ax3.set_title(f'{self.texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                ax3.set_xlabel(self.texts['frequency_mhz'])
                ax3.set_ylabel(self.texts['spectrum_value'])
                ax3.grid(True, alpha=0.3)
                
                # 消除X轴留白：精确设置频率范围
                ax3.set_xlim(freqs_mhz[0], freqs_mhz[-1])
                
                # MATLAB对齐：自适应设置X轴刻度，显示关键频率点
                key_freqs = self.find_adaptive_frequency_ticks(freqs_mhz, fftdata)
                
                if key_freqs:
                    ax3.set_xticks(key_freqs)
                    ax3.set_xticklabels([f'{f:.1f}' if f != int(f) else f'{int(f)}' for f in key_freqs])
                    print(f"频谱图显示关键频率点: {key_freqs}")
                
                self.status_label.config(text=f"{self.texts['spectrum_completed_calculating_timefreq']}...")
                self.root.update()
                
            except Exception as e:
                print(f"FFT计算超时或失败: {e}")
                ax3 = self.axes[2]
                ax3.text(0.5, 0.5, '频谱计算失败', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('频谱图 (计算失败)')
            
            # 等待时频图计算完成并绘制
            try:
                F_mhz, T_ms, Pxx_db, success = stft_future.result(timeout=45)  # 45秒超时
                
                ax4 = self.axes[3]
                if success:
                    # 绘制时频图 - 严格按照MATLAB的方式
                    # 设置显示范围：[X轴最小值, X轴最大值, Y轴最小值, Y轴最大值]
                    # 对应：[时间最小值, 时间最大值, 频率最小值, 频率最大值]
                    extent = [T_ms[0], T_ms[-1], F_mhz[0], F_mhz[-1]]
                    
                    # 使用imshow显示时频图 - 保持原始质量
                    # 注意：Pxx_db的维度应该是[频率bins, 时间frames]，正好对应imshow的[行,列]
                    im = ax4.imshow(Pxx_db, aspect='auto', cmap='viridis',
                                   extent=extent, origin='lower',
                                   interpolation='bilinear')  # 保持原始双线性插值以确保图像质量
                    
                    ax4.set_title(f'{self.texts["time_frequency"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                    ax4.set_xlabel(self.texts['time_ms'])  # X轴：时间(ms)
                    ax4.set_ylabel(self.texts['frequency_mhz'])  # Y轴：频率(MHz)
                    
                    # 设置轴范围 - 时间轴对应完整数据范围
                    if len(T_ms) > 1:
                        ax4.set_xlim(T_ms[0], T_ms[-1])
                    
                    # 频率轴范围
                    if len(F_mhz) > 1:
                        ax4.set_ylim(F_mhz[0], F_mhz[-1])
                    
                    # MATLAB对齐：设置自定义Y轴刻度
                    matlab_ticks = [2420, 2440, 2460]
                    valid_ticks = []
                    
                    for tick in matlab_ticks:
                        if F_mhz[0] <= tick <= F_mhz[-1]:
                            valid_ticks.append(tick)
                    
                    # 如果范围更大，添加更多刻度
                    if F_mhz[-1] > 2470:
                        if F_mhz[0] <= 2480 <= F_mhz[-1]:
                            valid_ticks.append(2480)
                    if F_mhz[0] < 2410:
                        if F_mhz[0] <= 2400 <= F_mhz[-1]:
                            valid_ticks.insert(0, 2400)
                    
                    if valid_ticks:
                        ax4.set_yticks(valid_ticks)
                        ax4.set_yticklabels([f'{tick}' for tick in valid_ticks])
                        print(f"时频图Y轴刻度设置: {valid_ticks}")
                    
                else:
                    # 绘制简化的幅度图
                    ax4.plot(Pxx_db, 'purple', linewidth=0.5)  # Pxx_db这时是信号幅度
                    ax4.set_title('信号幅度 (时频图计算失败)')
                    ax4.set_xlabel('采样点')
                    ax4.set_ylabel('幅度')
                    ax4.grid(True, alpha=0.3)
                    
                    # 消除X轴留白
                    if len(Pxx_db) > 1:
                        ax4.set_xlim(0, len(Pxx_db) - 1)
                
            except Exception as e:
                print(f"时频图计算超时或失败: {e}")
                ax4 = self.axes[3]
                ax4.text(0.5, 0.5, '时频图计算失败', ha='center', va='center', transform=ax4.transAxes)
                ax4.set_title('时频图 (计算失败)')
            
            # 最终绘制和状态更新 - 优化渲染性能
            self.update_progress(85, f"{self.texts['completing_rendering']}...")

            # 使用更高效的布局调整
            self.update_progress(90, "调整布局...")
            plt.tight_layout(pad=1.0)  # 减少padding以加快计算

            self.update_progress(95, "渲染图表...")
            # 使用draw_idle()代替draw()以提高性能
            self.canvas.draw_idle()

            # 添加分段标记（如果有分段结果）
            self.add_segment_markers_to_plots()

            # 完成状态更新 - 提供详细的数据范围信息
            self.update_progress(100, "绘制完成")
            time_range_ms = (N_sig - 1) / fs * 1000
            coverage_info = f"{self.texts['time_domain_range']}: 0-{N_sig-1}{self.texts['points_unit']}, 0-{time_range_ms:.1f}ms"
            display_info = f"{self.texts['display_text']}: {N_display}/{N_sig}{self.texts['points_unit']}"
            
            if N_sig > max_display_points:
                sampling_info = f"{self.texts['sampling_text']}: 1:{step}"
                final_status = f"{display_info}, {sampling_info}, {coverage_info}"
            else:
                final_status = f"{display_info}, {coverage_info}"

            # 隐藏进度条并设置最终状态
            self.show_progress_bar(False)
            self.status_label.config(text=final_status)

        except Exception as e:
            print(f"绘制过程出现错误: {e}")
            render_failed_text = "绘制失败" if self.current_language == 'zh' else "Rendering failed"
            self.show_progress_bar(False)
            self.status_label.config(text=render_failed_text)

        finally:
            self.is_computing = False
    
    def display_signal_3d(self):
        """
        显示 3D 信号分析图表（简化实现）
        """
        # 检查torch是否可用
        if not TORCH_AVAILABLE:
            # 如果torch不可用，显示提示信息并回退到2D模式
            torch_warning = ('3D显示功能需要torch库支持\n请安装: pip install torch' 
                            if self.current_language == 'zh' 
                            else '3D display requires torch library\nPlease install: pip install torch')
            
            unavailable_title = ('3D功能不可用' 
                                if self.current_language == 'zh' 
                                else '3D Feature Unavailable')
            
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, torch_warning, 
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title(unavailable_title)
            
            plt.tight_layout()
            self.canvas.draw()
            
            # 自动切换回2D模式
            self.is_3d_mode = False
            self.view_button.config(text=self.texts['view_2d'])
            return
        
        # 简化的3D显示实现
        try:
            # 清空所有子图
            for ax in self.axes:
                ax.clear()
            
            # 获取信号数据
            sig = self.current_signal
            fs = self.current_metadata.get('fs', 1e6)
            fc = self.current_metadata.get('fc', 2.4e9)
            
            # 限制数据量以提高3D显示性能
            max_3d_points = 10000
            if len(sig) > max_3d_points:
                step = len(sig) // max_3d_points
                sig = sig[::step]
            
            # 第一个3D图：简化的时频图
            ax1 = self.axes[0]
            t = np.arange(len(sig))
            f = np.fft.fftfreq(len(sig), 1/fs)[:len(sig)//2] / 1e6  # MHz
            
            # 创建网格
            T, F = np.meshgrid(t[:1000], f[:500])  # 限制数据量
            Z = np.abs(np.outer(f[:500], np.abs(sig[:1000])))
            
            surf1 = ax1.plot_surface(T, F, Z, cmap='viridis', alpha=0.8)
            ax1.set_title(self.texts['time_frequency'])
            
            time_label = '时间' if self.current_language == 'zh' else 'Time'
            freq_label = '频率(MHz)' if self.current_language == 'zh' else 'Frequency(MHz)'
            amplitude_label = '幅度' if self.current_language == 'zh' else 'Amplitude'
            
            ax1.set_xlabel(time_label)
            ax1.set_ylabel(freq_label)
            ax1.set_zlabel(amplitude_label)
            
            # 第二个3D图：频谱图的3D表示
            ax2 = self.axes[1]
            fft_data = np.fft.fftshift(np.abs(np.fft.fft(sig)))
            freqs = np.fft.fftshift(np.fft.fftfreq(len(sig), 1/fs)) / 1e6
            
            # 创建3D频谱图
            x = np.arange(len(fft_data)//10)  # 限制数据量
            y = freqs[::10]
            X, Y = np.meshgrid(x, y)
            Z = np.tile(fft_data[::10], (len(y), 1))
            
            surf2 = ax2.plot_surface(X, Y, Z, cmap='plasma', alpha=0.8)
            ax2.set_title(self.texts['frequency_spectrum'])
            
            sample_label = '采样点' if self.current_language == 'zh' else 'Sample Points'
            
            ax2.set_xlabel(sample_label)
            ax2.set_ylabel(freq_label)
            ax2.set_zlabel(amplitude_label)
            
        except Exception as e:
            print(f"3D显示计算失败: {e}")
            # 显示错误信息
            error_msg = (f'3D显示计算失败:\n{str(e)}' 
                        if self.current_language == 'zh' 
                        else f'3D display calculation failed:\n{str(e)}')
            
            error_title = ('3D显示失败' 
                          if self.current_language == 'zh' 
                          else '3D Display Failed')
            
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, error_msg, 
                       ha='center', va='center', transform=ax.transAxes, fontsize=10)
                ax.set_title(error_title)
        
        # 调整布局
        plt.tight_layout()
        self.canvas.draw()

        # 确保任何打开的对话框保持在顶层
        self._ensure_dialogs_on_top()

        self.status_label.config(text=self.texts['ready'])

    # =================================================================================
    # 信道扫描功能
    # =================================================================================
    
    def run_channel_scan(self):
        """运行信道扫描"""
        if not CHANSCAN_AVAILABLE:
            ScaledMessageBox.showwarning(self.texts['warning'], self.texts['scan_not_available'],
                                       parent=self.root, language=self.current_language)
            return
        
        if self.current_signal is None:
            ScaledMessageBox.showwarning(self.texts['warning'], self.texts['no_data_loaded'],
                                       parent=self.root, language=self.current_language)
            return
        
        # 启动扫描线程
        def scan_thread():
            try:
                from core_functions import run_channel_scan
                
                def progress_callback(message):
                    self.root.after(0, lambda: self.status_label.config(text=message))
                
                # 获取当前文件路径
                current_file_path = None
                if self.current_files and self.current_index < len(self.current_files):
                    current_file_path = self.current_files[self.current_index]

                # 运行信道扫描
                success, segments, fs_val, error_msg = run_channel_scan(
                    self.current_signal,
                    self.current_metadata,
                    progress_callback,
                    current_file_path
                )
                
                if success:
                    self.segment_results = segments
                    self.segment_fs = fs_val # 保存采样率
                    self.root.after(0, self.on_scan_complete)
                else:
                    self.root.after(0, lambda: self.on_scan_error(error_msg))
                    
            except Exception as e:
                self.root.after(0, lambda: self.on_scan_error(str(e)))
        
        self.status_label.config(text=self.texts['processing'])
        threading.Thread(target=scan_thread, daemon=True).start()
    
    def on_scan_complete(self):
        """扫描完成回调"""
        num_segments = len(self.segment_results)
        self.status_label.config(text=f"{self.texts['scan_complete']}: 检测到 {num_segments} 个信号段")
        
        # 更新分段结果显示
        self.update_segment_results_display()
        
        # 自动展开分段结果面板
        if hasattr(self, 'panel_collapsed') and self.panel_collapsed.get('segment_results', False):
            self.toggle_panel_collapse('segment_results')
        
        # 如果当前显示的是信号图表，添加分段标记
        if hasattr(self, 'current_signal') and self.current_signal is not None:
            self.add_segment_markers_to_plots()
    
    def on_scan_error(self, error_msg):
        """扫描失败回调"""
        self.status_label.config(text=self.texts['scan_failed'])
        ScaledMessageBox.showerror(self.texts['error'], f"{self.texts['scan_failed']}: {error_msg}",
                                 parent=self.root, language=self.current_language)
    
    # =================================================================================
    # 分段结果管理功能
    # =================================================================================
    
    def update_segment_results_display(self):
        """更新分段结果显示 (TreeView 风格)"""
        # 清空TreeView
        for item in self.segments_tree.get_children():
            self.segments_tree.delete(item)
        
        # 重置选择状态
        self.segment_selected = {}

        if not self.segment_results:
            return

        # 填充TreeView数据
        for i, segment in enumerate(self.segment_results):
            start_pos, end_pos = segment['start_pos'], segment['end_pos']
            signal_length = end_pos - start_pos + 1
            
            # 计算持续时间
            wideband_fs = self.current_metadata.get('fs')
            duration_ms = (signal_length / wideband_fs * 1000) if wideband_fs and wideband_fs > 0 else 0

            # 默认选中所有分段
            selected = True
            self.segment_selected[i] = selected
            
            # 插入数据到TreeView
            # 根据系统环境选择合适的符号，避免编码问题
            check_mark = self.get_check_symbol(selected)
            item_id = self.segments_tree.insert('', 'end', values=(
                check_mark,  # 选择状态
                i + 1,  # 编号
                f"[{start_pos}, {end_pos}]",  # 位置
                f"{segment['center_freq'] / 1e6:.2f}",  # 中心频率(MHz)
                f"{segment['bandwidth'] / 1e6:.2f}",  # 带宽(MHz)
                f"{duration_ms:.2f}",  # 持续时间(ms)
                f"{segment['snr']:.2f}"  # 信噪比(dB)
            ))
        
        # 更新添加按钮状态
        self.update_add_button_state()

    def clear_segment_results(self):
        """清空分段结果"""
        # 设置标志，防止在display_signal中重复清空
        self._clearing_segments = True

        self.segment_results = []
        self.segment_fs = None

        # 清空TreeView
        if hasattr(self, 'segments_tree'):
            for item in self.segments_tree.get_children():
                self.segments_tree.delete(item)

        # 清空选择状态
        self.segment_selected = {}

        # 更新添加按钮状态
        self.update_add_button_state()

        self.status_label.config(text=self.texts['results_cleared'])
        # 清除图表上的标记
        self.add_segment_markers_to_plots()

        # 清除标志
        self._clearing_segments = False

    # =================================================================================
    # 分段列表管理功能
    # =================================================================================
    
    def on_segment_click(self, event):
        """处理分段列表的点击事件"""
        region = self.segments_tree.identify_region(event.x, event.y)
        if region == "cell":
            # 获取点击的列
            column = self.segments_tree.identify_column(event.x)
            if column == '#1':  # 选择列
                item = self.segments_tree.identify_row(event.y)
                if item:
                    # 切换选中状态
                    self.toggle_segment_selection(item)
    
    def on_segment_double_click(self, event):
        """处理分段列表的双击事件"""
        item = self.segments_tree.identify_row(event.y)
        if item:
            # 显示详细信息
            self.show_segment_details(item)

    def on_segment_right_click(self, event):
        """处理分段列表的右键点击事件"""
        item = self.segments_tree.identify_row(event.y)
        if item:
            # 选中该项
            self.segments_tree.selection_set(item)

            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="编辑位置", command=lambda: self.show_segment_details(item))
            context_menu.add_separator()
            context_menu.add_command(label="切换选择", command=lambda: self.toggle_segment_selection(item))

            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def toggle_segment_selection(self, item):
        """切换分段的选中状态"""
        try:
            # 获取分段索引
            segment_index = self.get_segment_index_from_item(item)
            if segment_index is not None:
                # 切换选择状态
                self.segment_selected[segment_index] = not self.segment_selected.get(segment_index, False)
                
                # 更新显示
                values = list(self.segments_tree.item(item, 'values'))
                values[0] = self.get_check_symbol(self.segment_selected[segment_index])
                self.segments_tree.item(item, values=values)
                
                # 更新按钮状态
                self.update_add_button_state()
        except Exception as e:
            print(f"切换选择状态失败: {e}")
    
    def get_segment_index_from_item(self, item):
        """从TreeView项获取分段索引"""
        try:
            values = self.segments_tree.item(item, 'values')
            if values:
                return int(values[1]) - 1  # 编号从1开始，索引从0开始
        except (ValueError, IndexError):
            pass
        return None
    
    def show_segment_details(self, item):
        """显示分段详细信息并提供编辑功能"""
        segment_index = self.get_segment_index_from_item(item)
        if segment_index is not None and segment_index < len(self.segment_results):
            segment = self.segment_results[segment_index]

            # 创建详细信息窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"信号段 {segment_index + 1} 详细信息")
            detail_window.geometry("500x400")
            detail_window.transient(self.root)

            # 确保窗口可见后再设置grab_set，避免Linux下的grab failed错误
            detail_window.update_idletasks()  # 确保窗口布局完成
            detail_window.deiconify()  # 确保窗口可见
            try:
                detail_window.grab_set()
            except tk.TclError as e:
                print(f"警告: 无法设置窗口焦点 - {e}")
                # 在Linux下如果grab_set失败，继续执行而不中断

            # 创建主框架
            main_frame = tk.Frame(detail_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 详细信息显示区域
            info_frame = tk.Frame(main_frame)
            info_frame.pack(fill=tk.BOTH, expand=True)

            text_widget = tk.Text(info_frame, wrap=tk.WORD, font=self.default_font, height=12)
            text_widget.pack(fill=tk.BOTH, expand=True)

            # 填充详细信息
            start_pos, end_pos = segment['start_pos'], segment['end_pos']
            signal_length = end_pos - start_pos + 1
            wideband_fs = self.current_metadata.get('fs')
            duration_ms = (signal_length / wideband_fs * 1000) if wideband_fs and wideband_fs > 0 else 0

            details = f"信号段 {segment_index + 1} 详细信息:\n\n"
            details += f"位置: [{start_pos}, {end_pos}]\n"
            details += f"持续时间: {duration_ms:.2f} ms ({signal_length} 点)\n"
            details += f"中心频率: {segment['center_freq'] / 1e6:.2f} MHz\n"
            details += f"带宽: {segment['bandwidth'] / 1e6:.2f} MHz\n"
            details += f"信噪比: {segment['snr']:.2f} dB\n"
            details += f"采样率: {self.segment_fs:.2f} Hz\n" if self.segment_fs else ""
            details += f"选择状态: {'已选择' if self.segment_selected.get(segment_index, False) else '未选择'}\n"

            text_widget.insert(tk.END, details)
            text_widget.config(state=tk.DISABLED)

            # 编辑区域
            edit_frame = tk.Frame(main_frame)
            edit_frame.pack(fill=tk.X, pady=(10, 0))

            # 位置编辑标签
            tk.Label(edit_frame, text="修正起止位置:", font=self.default_font).pack(anchor='w')

            # 位置输入框架
            pos_frame = tk.Frame(edit_frame)
            pos_frame.pack(fill=tk.X, pady=5)

            tk.Label(pos_frame, text="起始位置:", font=self.default_font).pack(side=tk.LEFT)
            start_var = tk.StringVar(value=str(start_pos))
            start_entry = tk.Entry(pos_frame, textvariable=start_var, width=12, font=self.default_font)
            start_entry.pack(side=tk.LEFT, padx=(5, 10))

            tk.Label(pos_frame, text="结束位置:", font=self.default_font).pack(side=tk.LEFT)
            end_var = tk.StringVar(value=str(end_pos))
            end_entry = tk.Entry(pos_frame, textvariable=end_var, width=12, font=self.default_font)
            end_entry.pack(side=tk.LEFT, padx=5)

            # 按钮框架
            button_frame = tk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 应用修改按钮
            apply_btn = tk.Button(button_frame, text="应用修改", font=self.default_font,
                                command=lambda: self.apply_segment_position_edit(
                                    segment_index, start_var.get(), end_var.get(), detail_window))
            apply_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 关闭按钮
            close_btn = tk.Button(button_frame, text="关闭", font=self.default_font,
                                command=detail_window.destroy)
            close_btn.pack(side=tk.LEFT)

    def apply_segment_position_edit(self, segment_index, start_str, end_str, detail_window):
        """应用分段位置修改，遵循原始信号处理的长度限制规则"""
        try:
            # 验证输入
            new_start = int(start_str)
            new_end = int(end_str)

            # 基本验证
            if new_start < 0:
                raise ValueError("起始位置不能小于0")
            if new_end <= new_start:
                raise ValueError("结束位置必须大于起始位置")

            # 检查是否超出信号范围
            if hasattr(self, 'current_signal') and self.current_signal is not None:
                signal_length = len(self.current_signal)
                if new_end >= signal_length:
                    raise ValueError(f"结束位置不能超过信号长度 ({signal_length})")

            # 应用与 extract_and_save_signals 相同的长度限制规则
            wideband_fs = self.current_metadata.get('fs', 61.44e6)
            new_start, new_end = self.apply_signal_length_constraints(new_start, new_end, wideband_fs)

            # 更新分段数据
            old_segment = self.segment_results[segment_index].copy()
            self.segment_results[segment_index]['start_pos'] = new_start
            self.segment_results[segment_index]['end_pos'] = new_end

            # 重新计算持续时间
            signal_length = new_end - new_start + 1
            if wideband_fs and wideband_fs > 0:
                duration_ms = signal_length / wideband_fs * 1000
                self.segment_results[segment_index]['duration'] = duration_ms

            # 重新提取信号数据并进行窄带信号处理（保持与原始流程一致）
            if hasattr(self, 'current_signal') and self.current_signal is not None:
                # 提取修改后位置的宽带信号数据
                wb_signal_data = self.current_signal[new_start:new_end+1]

                # 进行与 extract_and_save_signals 相同的窄带信号处理
                processed_signal_data = self.process_segment_signal(
                    wb_signal_data, segment_index, new_start, new_end)

                if processed_signal_data is not None:
                    self.segment_results[segment_index]['signal_data'] = processed_signal_data
                    print(f"  重新处理信号: 宽带 {len(wb_signal_data)} -> 窄带 {len(processed_signal_data)}")
                else:
                    # 如果窄带处理失败，使用原始宽带信号
                    self.segment_results[segment_index]['signal_data'] = wb_signal_data
                    print(f"  窄带处理失败，使用宽带信号: {len(wb_signal_data)}")

            # 更新显示
            self.update_segment_results_display()

            # 更新图表标记
            self.add_segment_markers_to_plots()

            # 关闭详细信息窗口
            detail_window.destroy()

            # 显示成功消息
            duration_ms = (new_end - new_start + 1) / wideband_fs * 1000
            self.status_label.config(text=f"分段 {segment_index + 1} 位置已修改: [{new_start}, {new_end}] ({duration_ms:.2f}ms)")

            print(f"分段 {segment_index + 1} 位置修改:")
            print(f"  原位置: [{old_segment['start_pos']}, {old_segment['end_pos']}]")
            print(f"  新位置: [{new_start}, {new_end}]")
            print(f"  持续时间: {duration_ms:.2f} ms")

        except ValueError as e:
            ScaledMessageBox.showerror("输入错误", str(e), parent=detail_window, language=self.current_language)
        except Exception as e:
            ScaledMessageBox.showerror("修改失败", f"修改分段位置失败: {str(e)}",
                                     parent=detail_window, language=self.current_language)

    def apply_signal_length_constraints(self, start_pos, end_pos, wb_fs):
        """应用与原始信号处理相同的长度限制规则"""
        # 使用与 extract_and_save_signals 相同的参数
        clip_ms = 6.0  # 最小持续时间 6ms
        const_ms = 20.0  # 最大持续时间 20ms

        # 计算裁剪长度和最大长度（与 calregions.py 保持一致）
        clip_len = int(np.floor(wb_fs * clip_ms / 1000))
        max_len = int(np.floor(wb_fs * const_ms / 1000))

        # 应用长度限制规则
        original_start, original_end = start_pos, end_pos

        # 如果结束位置超过最大长度，将其设置为最大长度
        if end_pos > start_pos + max_len:
            end_pos = start_pos + max_len
            print(f"长度限制: 结束位置从 {original_end} 调整为 {end_pos} (最大长度限制)")

        # 如果长度小于最小长度，抛出错误
        if end_pos < start_pos + clip_len // 2:
            duration_ms = (end_pos - start_pos) * 1000 / wb_fs
            min_duration_ms = clip_ms / 2
            raise ValueError(f"信号长度太短: {duration_ms:.2f}ms < {min_duration_ms:.2f}ms (最小长度要求)")

        return start_pos, end_pos

    def process_segment_signal(self, wb_signal_data, segment_index, start_pos, end_pos):
        """
        对修改后的分段信号进行与 extract_and_save_signals 相同的窄带信号处理

        Args:
            wb_signal_data: 宽带信号数据
            segment_index: 分段索引
            start_pos: 起始位置
            end_pos: 结束位置

        Returns:
            处理后的窄带信号数据，如果处理失败返回 None
        """
        try:
            # 获取原始分段信息
            segment = self.segment_results[segment_index]

            # 宽带信号参数
            wb_fc = self.current_metadata.get('fc', 915e6)
            wb_fs = self.current_metadata.get('fs', 61.44e6)
            wb_bw = self.current_metadata.get('bw', 50e6)

            # 窄带信号参数（从原始分段中获取）
            nb_fc = segment.get('center_freq', wb_fc)
            nb_bw = segment.get('bandwidth', 1e6)
            nb_fs = 2.56e6  # 固定的窄带采样率

            print(f"  窄带信号处理参数:")
            print(f"    宽带: fc={wb_fc/1e6:.1f}MHz, fs={wb_fs/1e6:.1f}MHz, bw={wb_bw/1e6:.1f}MHz")
            print(f"    窄带: fc={nb_fc/1e6:.1f}MHz, fs={nb_fs/1e6:.1f}MHz, bw={nb_bw/1e6:.1f}MHz")

            # 导入必要的函数
            from chanlib.calregions import findAccuracteBW, ExtractNBSig

            # 调用自定义函数修正中心频率和带宽（与 extract_and_save_signals 保持一致）
            fft_len = 1024  # 默认FFT长度
            nb_fc_corrected, nb_bw_corrected = findAccuracteBW(
                wb_signal_data, nb_fc, nb_bw, wb_fc, wb_fs, fft_len)

            print(f"    修正后: fc={nb_fc_corrected/1e6:.1f}MHz, bw={nb_bw_corrected/1e6:.1f}MHz")

            # 调用自定义函数提取窄带信号（与模型训练时保持一致）
            # 使用与 dataloader.py 中 gLenNorm 一致的长度
            from utils.dataloader import getLenNorm
            nb_len_set = getLenNorm()  # 23808 个采样点，与模型训练时一致
            ret, nb_signal = ExtractNBSig(
                wb_signal_data, wb_fc, wb_bw, wb_fs,
                nb_fc_corrected, nb_bw_corrected, nb_fs, nb_len_set)

            if ret and nb_signal is not None and len(nb_signal) > 0:
                print(f"    窄带信号提取成功: {len(nb_signal)} 个采样点")

                # 更新分段的频率和带宽信息
                self.segment_results[segment_index]['center_freq'] = nb_fc_corrected
                self.segment_results[segment_index]['bandwidth'] = nb_bw_corrected

                return nb_signal
            else:
                print(f"    窄带信号提取失败: ret={ret}")
                return None

        except Exception as e:
            print(f"    窄带信号处理出错: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def select_all_segments(self):
        """选中所有分段"""
        for i in range(len(self.segment_results)):
            self.segment_selected[i] = True
        self.refresh_segment_display()
        self.update_add_button_state()
    
    def deselect_all_segments(self):
        """取消选中所有分段"""
        for i in range(len(self.segment_results)):
            self.segment_selected[i] = False
        self.refresh_segment_display()
        self.update_add_button_state()
    
    def delete_selected_segments(self):
        """删除选中的分段"""
        selected_indices = [i for i, selected in self.segment_selected.items() if selected]
        
        if not selected_indices:
            ScaledMessageBox.showinfo(self.texts['warning'], self.texts['no_segments_selected'],
                                     parent=self.root, language=self.current_language)
            return
        
        # 确认删除
        if not ScaledMessageBox.askyesno(self.texts['confirm_delete_segments'],
                                        self.texts['confirm_delete_segments'],
                                        parent=self.root, language=self.current_language):
            return
        
        # 删除选中的分段（从后往前删除，避免索引问题）
        for i in sorted(selected_indices, reverse=True):
            if i < len(self.segment_results):
                del self.segment_results[i]
        
        # 重新显示分段结果
        self.update_segment_results_display()
        
        # 更新图表标记
        self.add_segment_markers_to_plots()
        
        # 更新状态
        self.status_label.config(text=self.texts['segments_deleted'].format(count=len(selected_indices)))
    
    def refresh_segment_display(self):
        """刷新分段显示"""
        for item in self.segments_tree.get_children():
            segment_index = self.get_segment_index_from_item(item)
            if segment_index is not None:
                values = list(self.segments_tree.item(item, 'values'))
                values[0] = self.get_check_symbol(self.segment_selected.get(segment_index, False))
                self.segments_tree.item(item, values=values)
    
    def update_add_button_state(self):
        """更新添加按钮状态"""
        if not hasattr(self, 'add_to_db_button'):
            return
            
        selected_count = sum(1 for selected in self.segment_selected.values() if selected)
        
        if selected_count > 0:
            self.add_to_db_button.config(state=tk.NORMAL)
        else:
            self.add_to_db_button.config(state=tk.DISABLED)
    
    def get_selected_segments(self):
        """获取选中的分段"""
        selected_segments = []
        for i, selected in self.segment_selected.items():
            if selected and i < len(self.segment_results):
                selected_segments.append(self.segment_results[i])
        return selected_segments

    # =================================================================================
    # 新的数据库和文件导航功能
    # =================================================================================

    def add_segments_to_db_action(self):
        """将选中的分段添加到数据库的动作"""
        # 1. 获取选中的分段
        selected_segments = self.get_selected_segments()
        if not selected_segments:
            ScaledMessageBox.showinfo(self.texts['warning'], self.texts['no_segments_selected'], parent=self.root)
            return
            
        # 2. 检查模型是否加载
        if self.arcface_model is None:
            user_choice = ScaledMessageBox.askyesno(
                self.texts['no_model_db_prompt_title'],
                self.texts['no_model_db_add_prompt_message'],
                parent=self.root,
                language=self.current_language
            )
            if user_choice:
                self.pending_add_to_db_after_model_load = True
                self.select_and_load_model()
            return

        # 3. 用户确认
        count_str = self.texts['selected_segments_count'].format(count=len(selected_segments))
        confirm_message = self.texts['add_segments_to_db_confirm'] + f"\n\n{count_str}"

        if not ScaledMessageBox.askokcancel(self.texts['title'], 
                                           confirm_message, 
                                           parent=self.root, 
                                           language=self.current_language):
            return
            
        self.status_label.config(text=self.texts['adding_segments_to_db'])
        
        def add_thread():
            try:
                # 使用绝对导入，避免相对导入问题
                import sys
                import os
                
                # 获取当前文件所在目录（用于构建文件路径）
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                
                from core_functions import add_segments_to_db

                # 获取当前文件路径
                current_file_path = None
                if self.current_files and self.current_index < len(self.current_files):
                    current_file_path = self.current_files[self.current_index]

                # 添加选中的分段到数据库
                success, count, err_msg = add_segments_to_db(
                    self.arcface_model,
                    selected_segments,
                    current_file_path
                )
                if success:
                    success_count = count
                    self.root.after(0, lambda c=success_count: self.on_add_success(c))
                else:
                    error_message = err_msg
                    self.root.after(0, lambda msg=error_message: self.on_add_error(msg))
            except Exception as e:
                error_message = str(e)
                self.root.after(0, lambda msg=error_message: self.on_add_error(msg))
        
        threading.Thread(target=add_thread, daemon=True).start()

    def on_add_success(self, count):
        """添加到数据库成功回调"""
        msg = self.texts['add_to_db_success'].format(count=count)
        self.status_label.config(text=msg)
        ScaledMessageBox.showinfo(self.texts['title'], msg, parent=self.root)

    def on_add_error(self, error_msg):
        """添加到数据库失败回调，将错误信息输出到命令行"""
        self.status_label.config(text=self.texts['add_to_db_failed'])
        print(f"\n--- {self.texts['add_to_db_failed']} ---")
        print(f"错误详情: {error_msg}")
        print("-------------------------------------------------\n")

    def skip_to_next_file_action(self):
        """跳到下一个文件并自动扫描"""
        if self.current_index < len(self.current_files) - 1:
            self.next_file()
            # 加载文件是异步的，我们需要在加载完成后再扫描
            # 修改 on_file_loaded_success 来触发扫描
            self.root.after(200, self.run_channel_scan) # 稍作延迟以确保文件加载完成
        else:
            ScaledMessageBox.showinfo(self.texts['title'], "已是最后一个文件。", parent=self.root)

    def view_database(self):
        """查看数据库结构"""
        def view_thread():
            try:
                # 使用绝对导入，避免相对导入问题
                import sys
                import os
                
                # 获取当前文件所在目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                
                from core_functions import get_database_structure
                
                success, db_records, error_msg = get_database_structure()
                
                if success:
                    self.root.after(0, lambda records=db_records: self.show_database_window(records))
                else:
                    self.root.after(0, lambda msg=error_msg: self.on_view_db_error(msg))
                    
            except Exception as e:
                error_message = str(e)
                self.root.after(0, lambda msg=error_message: self.on_view_db_error(msg))
        
        threading.Thread(target=view_thread, daemon=True).start()
    
    def validate_folder(self, folder_path):
        """
        验证文件夹是否适合用于数据捕获
        
        检查文件夹是否存在，如果存在且不为空，则提示用户确认是否清空
        
        Args:
            folder_path (str): 要验证的文件夹路径
            
        Returns:
            bool: 如果文件夹有效或用户确认清空，则返回True；否则返回False
        """
        # 检查文件夹是否存在
        if os.path.exists(folder_path):
            # 检查是否为目录
            if not os.path.isdir(folder_path):
                error_msg = f"'{folder_path}' 不是一个有效的目录" if self.current_language == 'zh' else f"'{folder_path}' is not a valid directory"
                ScaledMessageBox.showerror(
                    "错误" if self.current_language == 'zh' else "Error",
                    error_msg,
                    parent=self.root,
                    language=self.current_language
                )
                return False
                
            # 如果文件夹不为空，显示确认清空的提示
            if os.listdir(folder_path):
                confirm_msg = "文件夹已存在且不为空，继续操作将清空该文件夹。是否继续？" if self.current_language == 'zh' else "The folder exists and is not empty. Continuing will clear this folder. Continue?"
                confirm_title = "确认清空文件夹" if self.current_language == 'zh' else "Confirm Clear Folder"
                
                if not ScaledMessageBox.askyesno(
                    confirm_title,
                    confirm_msg,
                    parent=self.root,
                    language=self.current_language
                ):
                    # 用户取消操作
                    return False
        
        # 检查文件夹权限
        try:
            # 如果文件夹不存在，尝试创建
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            
            # 测试写入权限
            test_file = os.path.join(folder_path, ".test_write_permission")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            
            return True
        except (PermissionError, OSError) as e:
            error_msg = f"无法写入文件夹 '{folder_path}': {str(e)}" if self.current_language == 'zh' else f"Cannot write to folder '{folder_path}': {str(e)}"
            ScaledMessageBox.showerror(
                "权限错误" if self.current_language == 'zh' else "Permission Error",
                error_msg,
                parent=self.root,
                language=self.current_language
            )
            return False
    
    def capture_data(self):
        """
        数据捕获功能
        
        实现USRP数据采集功能，允许用户选择保存路径和设置中心频率
        """
        # 显示USRP设备检查进度
        check_title = "设备检查" if self.current_language == 'zh' else "Device Check"
        check_msg = "正在检查USRP设备，请稍候..." if self.current_language == 'zh' else "Checking USRP device, please wait..."
        
        # 创建检查进度对话框
        check_dialog = ProgressDialog(
            parent=self.root,
            title=check_title,
            message=check_msg,
            language=self.current_language,
            width=450,
            height=120
        )
        
        # 启动自动脉冲模式
        check_dialog.start_auto_pulse()
        
        # 在后台线程中检查USRP设备
        def check_usrp_thread():
            try:
                is_available, error_msg = DataCaptureController.check_usrp_available()
                
                # 关闭检查对话框
                self.root.after(0, check_dialog.close)
                
                if not is_available:
                    self.root.after(100, lambda: ScaledMessageBox.showerror(
                        "错误" if self.current_language == 'zh' else "Error",
                        error_msg,
                        parent=self.root,
                        language=self.current_language
                    ))
                    return
                
                # 继续执行数据捕获流程
                self.root.after(100, self._continue_capture_data)
                
            except Exception as e:
                self.root.after(0, check_dialog.close)
                self.root.after(100, lambda: ScaledMessageBox.showerror(
                    "错误" if self.current_language == 'zh' else "Error",
                    f"设备检查失败: {str(e)}" if self.current_language == 'zh' else f"Device check failed: {str(e)}",
                    parent=self.root,
                    language=self.current_language
                ))
        
        # 启动检查线程
        threading.Thread(target=check_usrp_thread, daemon=True).start()
    
    def _continue_capture_data(self):
        """继续执行数据捕获流程"""
        # 显示用户提示，说明选择文件夹的用途
        info_title = "选择数据存储文件夹" if self.current_language == 'zh' else "Select Data Storage Folder"
        info_msg = ("请选择一个文件夹来存储采集的信号数据。\n\n"
                   "注意：\n"
                   "• 如果文件夹已存在且不为空，将会被清空\n"
                   "• 采集的数据文件将以时间戳命名\n"
                   "• 建议选择有足够空间的文件夹") if self.current_language == 'zh' else (
                   "Please select a folder to store the captured signal data.\n\n"
                   "Note:\n"
                   "• If the folder exists and is not empty, it will be cleared\n"
                   "• Captured data files will be named with timestamps\n"
                   "• It is recommended to select a folder with sufficient space")
        
        # 显示提示信息
        if not ScaledMessageBox.askokcancel(
            info_title,
            info_msg,
            parent=self.root,
            language=self.current_language
        ):
            return  # 用户取消操作
        
        # 选择保存文件夹
        folder_title = "选择数据存储文件夹" if self.current_language == 'zh' else "Select Data Storage Folder"
        folder_path = filedialog.askdirectory(
            title=folder_title,
            initialdir=self.last_folder_dir
        )
        
        # 如果用户取消选择，则退出函数
        if not folder_path:
            return

        # 更新最后选择的文件夹路径
        self.last_folder_dir = os.path.dirname(folder_path) if os.path.dirname(folder_path) else folder_path
        self.save_config()  # 保存配置

        # 显示文件夹名称与信号类名一致的提示
        folder_name = os.path.basename(folder_path)
        folder_info_title = "文件夹选择提示" if self.current_language == 'zh' else "Folder Selection Notice"
        folder_info_msg = (f"您选择的文件夹名称为: {folder_name}\n\n"
                          f"请确保文件夹名称与所采集信号的类名一致，\n"
                          f"这样有助于后续的数据管理和分析。") if self.current_language == 'zh' else (
                          f"Selected folder name: {folder_name}\n\n"
                          f"Please ensure the folder name matches the signal class name\n"
                          f"for better data management and analysis.")

        ScaledMessageBox.showinfo(
            folder_info_title,
            folder_info_msg,
            parent=self.root,
            language=self.current_language
        )

        # 检查文件夹是否已有数据文件
        existing_files = self._check_existing_data_files(folder_path)
        if existing_files:
            # 显示清除数据的确认对话框
            clear_title = "文件夹数据清除确认" if self.current_language == 'zh' else "Folder Data Clear Confirmation"
            clear_msg = (f"检测到文件夹中已有 {len(existing_files)} 个数据文件。\n\n"
                        f"开始数据捕获前，需要清除文件夹中的现有数据。\n"
                        f"清除的文件包括：\n"
                        f"{chr(10).join(['• ' + os.path.basename(f) for f in existing_files[:5]])}"
                        f"{chr(10) + '• ...' if len(existing_files) > 5 else ''}\n\n"
                        f"是否继续并清除现有数据？") if self.current_language == 'zh' else (
                        f"Found {len(existing_files)} existing data files in the folder.\n\n"
                        f"Existing data must be cleared before starting data capture.\n"
                        f"Files to be cleared include:\n"
                        f"{chr(10).join(['• ' + os.path.basename(f) for f in existing_files[:5]])}"
                        f"{chr(10) + '• ...' if len(existing_files) > 5 else ''}\n\n"
                        f"Continue and clear existing data?")

            if not ScaledMessageBox.askyesno(
                clear_title,
                clear_msg,
                parent=self.root,
                language=self.current_language
            ):
                return  # 用户选择不继续，退出数据捕获

        # 检查文件夹权限
        has_permission, error_msg = DataCaptureController.check_folder_permissions(folder_path)
        if not has_permission:
            ScaledMessageBox.showerror(
                "权限错误" if self.current_language == 'zh' else "Permission Error",
                error_msg,
                parent=self.root,
                language=self.current_language
            )
            return

        # 使用FrequencyInputDialog创建频率输入对话框
        freq_input = FrequencyInputDialog.create_dialog(
            parent=self.root,
            language=self.current_language,
            initial_value=2446.0
        )
        
        # 验证频率输入
        is_valid, freq_hz = FrequencyInputDialog.validate_frequency(
            freq_mhz=freq_input,
            parent=self.root,
            language=self.current_language
        )
        
        # 如果输入无效，则退出函数
        if not is_valid:
            return

        # 记录日志
        print(f"用户输入的中心频率: {freq_input} MHz ({freq_hz} Hz)")

        # 使用FileCountInputDialog创建文件个数输入对话框
        file_count_input = FileCountInputDialog.create_dialog(
            parent=self.root,
            language=self.current_language,
            initial_value=10
        )

        # 验证文件个数输入
        is_count_valid, num_files = FileCountInputDialog.validate_file_count(
            file_count=file_count_input,
            parent=self.root,
            language=self.current_language
        )

        # 如果输入无效，则退出函数
        if not is_count_valid:
            return

        # 记录日志
        print(f"用户输入的文件个数: {num_files}")

        # 开始数据采集
        self._start_data_capture(folder_path, freq_hz, num_files)
    
    def _start_data_capture(self, folder_path, freq_hz, num_files=10):
        """开始数据采集"""
        # 创建进度提示对话框
        progress_title = "数据采集进度" if self.current_language == 'zh' else "Data Capture Progress"
        progress_msg = "正在初始化数据采集..." if self.current_language == 'zh' else "Initializing data capture..."
        
        # 使用ProgressDialog类创建进度对话框
        progress_dialog = ProgressDialog(
            parent=self.root,
            title=progress_title,
            message=progress_msg,
            language=self.current_language
        )
        
        # 在后台线程中执行数据采集
        def capture_thread():
            try:
                # 创建一个队列用于传递进度信息
                import queue
                progress_queue = queue.Queue()
                
                # 定义回调函数，用于更新进度
                def progress_callback(current_file, total_files, status_message):
                    # 将进度信息放入队列
                    progress_queue.put({
                        'current_file': current_file,
                        'total_files': total_files,
                        'status_message': status_message
                    })
                
                # 启动进度更新检查器
                def check_progress():
                    try:
                        # 非阻塞方式获取进度信息
                        while not progress_queue.empty():
                            progress_info = progress_queue.get_nowait()
                            
                            # 计算进度百分比
                            current_file = progress_info['current_file']
                            total_files = progress_info['total_files']
                            if total_files > 0:
                                progress_percent = min(90, (current_file / total_files) * 90)
                            else:
                                progress_percent = 0
                            
                            # 更新进度对话框
                            progress_dialog.update_progress(
                                value=progress_percent,
                                message=progress_info['status_message'],
                                file_count=current_file
                            )
                    except Exception as e:
                        print(f"进度更新错误: {e}")
                    
                    # 如果对话框仍然存在且未被取消，继续检查进度
                    if not progress_dialog.is_cancelled() and progress_dialog.dialog.winfo_exists():
                        self.root.after(100, check_progress)
                
                # 启动进度检查
                self.root.after(100, check_progress)
                
                # 调用数据采集控制函数
                success, result_msg, file_count = DataCaptureController.capture_data(
                    folder_path=folder_path,
                    freq_hz=freq_hz,
                    progress_callback=progress_callback,
                    num_files=num_files
                )
                
                # 如果没有被取消，则关闭进度对话框并处理结果
                if not progress_dialog.is_cancelled():
                    # 更新进度到100%
                    progress_dialog.update_progress(
                        value=100,
                        message="数据采集完成" if self.current_language == 'zh' else "Data capture completed"
                    )
                    
                    # 短暂延迟后关闭对话框
                    self.root.after(500, progress_dialog.close)
                    
                    if success:
                        # 获取文件数量
                        try:
                            # 搜索支持的文件格式
                            supported_extensions = ['.bvsp', '.dat', '.hdfv']
                            files = []
                            for ext in supported_extensions:
                                pattern = f"*{ext}"
                                files.extend(Path(folder_path).rglob(pattern))
                            actual_file_count = len(set([str(f) for f in files]))
                        except Exception:
                            actual_file_count = file_count if file_count else 0
                        
                        # 显示完成消息
                        file_count_text = f"{actual_file_count} 个文件" if self.current_language == 'zh' else f"{actual_file_count} files"

                        # 缩短路径显示，避免窗口过宽
                        folder_name = os.path.basename(folder_path)
                        parent_dir = os.path.basename(os.path.dirname(folder_path))
                        short_path = f".../{parent_dir}/{folder_name}" if parent_dir else folder_name

                        complete_msg = f"数据采集完成！\n\n共采集 {file_count_text}\n文件已保存到:\n{short_path}" if self.current_language == 'zh' else f"Data capture completed!\n\nCaptured {file_count_text}\nFiles saved to:\n{short_path}"
                        complete_title = "采集完成" if self.current_language == 'zh' else "Capture Complete"
                        
                        # 使用主线程显示消息框
                        self.root.after(1000, lambda: ScaledMessageBox.showinfo(
                            complete_title,
                            complete_msg,
                            parent=self.root,
                            language=self.current_language
                        ))
                        
                        # 播放系统提示音（如果可用）
                        try:
                            import winsound
                            winsound.MessageBeep(winsound.MB_ICONINFORMATION)
                        except:
                            pass
                        
                        # 自动加载采集的数据文件夹
                        self.root.after(1500, lambda: self.select_folder_and_load(folder_path))
                        
                        # 更新状态栏
                        status_msg = f"采集完成: {file_count_text}" if self.current_language == 'zh' else f"Capture completed: {file_count_text}"
                        self.root.after(0, lambda: self.status_label.config(text=status_msg))
                    else:
                        # 显示错误消息
                        self.root.after(0, lambda: ScaledMessageBox.showerror(
                            "错误" if self.current_language == 'zh' else "Error",
                            result_msg,
                            parent=self.root,
                            language=self.current_language
                        ))
                        
                        # 更新状态栏
                        self.root.after(0, lambda: self.status_label.config(text="采集失败" if self.current_language == 'zh' else "Capture failed"))
            except Exception as e:
                # 如果出现错误，关闭进度对话框并显示错误消息
                progress_dialog.close()
                
                error_msg = f"数据采集失败: {str(e)}" if self.current_language == 'zh' else f"Data capture failed: {str(e)}"
                error_title = "错误" if self.current_language == 'zh' else "Error"
                
                # 使用主线程显示错误消息
                self.root.after(0, lambda: ScaledMessageBox.showerror(
                    error_title,
                    error_msg,
                    parent=self.root,
                    language=self.current_language
                ))
        
        # 设置取消回调函数
        def on_cancel():
            cancel_msg = "数据采集已取消" if self.current_language == 'zh' else "Data capture cancelled"
            print(cancel_msg)
            
            # 显示取消消息
            self.root.after(0, lambda: ScaledMessageBox.showinfo(
                "已取消" if self.current_language == 'zh' else "Cancelled",
                cancel_msg,
                parent=self.root,
                language=self.current_language
            ))
        
        progress_dialog.set_on_cancel(on_cancel)
        
        # 启动后台线程
        threading.Thread(target=capture_thread, daemon=True).start()
    
    def _handle_capture_result(self, progress_dialog, success, result_msg, file_count, folder_path):
        """处理数据采集结果"""
        # 如果没有被取消，则关闭进度对话框并处理结果
        if not progress_dialog.is_cancelled():
            # 更新进度到100%
            progress_dialog.update_progress(
                value=100,
                message="数据采集完成" if self.current_language == 'zh' else "Data capture completed"
            )
            
            # 短暂延迟后关闭对话框
            self.root.after(500, progress_dialog.close)
            
            if success:
                # 获取文件数量
                try:
                    # 搜索支持的文件格式
                    supported_extensions = ['.bvsp', '.dat', '.hdfv']
                    files = []
                    for ext in supported_extensions:
                        pattern = f"*{ext}"
                        files.extend(Path(folder_path).rglob(pattern))
                    actual_file_count = len(set([str(f) for f in files]))
                except Exception:
                    actual_file_count = file_count if file_count else 0
                
                # 显示完成消息
                file_count_text = f"{actual_file_count} 个文件" if self.current_language == 'zh' else f"{actual_file_count} files"

                # 缩短路径显示，避免窗口过宽
                folder_name = os.path.basename(folder_path)
                parent_dir = os.path.basename(os.path.dirname(folder_path))
                short_path = f".../{parent_dir}/{folder_name}" if parent_dir else folder_name

                complete_msg = f"数据采集完成！\n\n共采集 {file_count_text}\n文件已保存到:\n{short_path}" if self.current_language == 'zh' else f"Data capture completed!\n\nCaptured {file_count_text}\nFiles saved to:\n{short_path}"
                complete_title = "采集完成" if self.current_language == 'zh' else "Capture Complete"
                
                # 使用主线程显示消息框
                self.root.after(1000, lambda: ScaledMessageBox.showinfo(
                    complete_title,
                    complete_msg,
                    parent=self.root,
                    language=self.current_language
                ))
                
                # 播放系统提示音（如果可用）
                try:
                    import winsound
                    winsound.MessageBeep(winsound.MB_ICONINFORMATION)
                except:
                    pass
                
                # 自动加载采集的数据文件夹
                self.root.after(1500, lambda: self.select_folder_and_load(folder_path))
                
                # 更新状态栏
                status_msg = f"采集完成: {file_count_text}" if self.current_language == 'zh' else f"Capture completed: {file_count_text}"
                self.root.after(0, lambda: self.status_label.config(text=status_msg))
            else:
                # 显示错误消息
                self.root.after(0, lambda: ScaledMessageBox.showerror(
                    "错误" if self.current_language == 'zh' else "Error",
                    result_msg,
                    parent=self.root,
                    language=self.current_language
                ))
                
                # 更新状态栏
                self.root.after(0, lambda: self.status_label.config(text="采集失败" if self.current_language == 'zh' else "Capture failed"))

        # 如果用户取消选择，则退出函数
        if not folder_path:
            return
            
        # 更新最后选择的文件夹路径
        self.last_folder_dir = os.path.dirname(folder_path) if os.path.dirname(folder_path) else folder_path
        self.save_config()  # 保存配置
        
        # 检查文件夹权限
        has_permission, error_msg = DataCaptureController.check_folder_permissions(folder_path)
        if not has_permission:
            ScaledMessageBox.showerror(
                "权限错误" if self.current_language == 'zh' else "Permission Error",
                error_msg,
                parent=self.root,
                language=self.current_language
            )
            return
            
        # 使用FrequencyInputDialog创建频率输入对话框
        freq_input = FrequencyInputDialog.create_dialog(
            parent=self.root,
            language=self.current_language,
            initial_value=2446.0
        )
        
        # 验证频率输入
        is_valid, freq_hz = FrequencyInputDialog.validate_frequency(
            freq_mhz=freq_input,
            parent=self.root,
            language=self.current_language
        )
        
        # 如果输入无效，则退出函数
        if not is_valid:
            return
            
        # 记录日志
        print(f"用户输入的中心频率: {freq_input} MHz ({freq_hz} Hz)")

        # 使用FileCountInputDialog创建文件个数输入对话框
        file_count_input = FileCountInputDialog.create_dialog(
            parent=self.root,
            language=self.current_language,
            initial_value=10
        )

        # 验证文件个数输入
        is_count_valid, num_files = FileCountInputDialog.validate_file_count(
            file_count=file_count_input,
            parent=self.root,
            language=self.current_language
        )

        # 如果输入无效，则退出函数
        if not is_count_valid:
            return

        # 记录日志
        print(f"用户输入的文件个数: {num_files}")

        # 创建进度提示对话框
        progress_title = "数据采集进度" if self.current_language == 'zh' else "Data Capture Progress"
        progress_msg = "正在初始化USRP设备..." if self.current_language == 'zh' else "Initializing USRP device..."
        
        # 使用ProgressDialog类创建进度对话框
        progress_dialog = ProgressDialog(
            parent=self.root,
            title=progress_title,
            message=progress_msg,
            language=self.current_language
        )
        
        # 在后台线程中执行数据采集
        def capture_thread():
            try:
                # 创建一个队列用于传递进度信息
                import queue
                progress_queue = queue.Queue()
                
                # 定义回调函数，用于更新进度
                def progress_callback(current_file, total_files, status_message):
                    # 将进度信息放入队列
                    progress_queue.put({
                        'current_file': current_file,
                        'total_files': total_files,
                        'status_message': status_message
                    })
                
                # 启动进度更新检查器
                def check_progress():
                    try:
                        # 非阻塞方式获取进度信息
                        while not progress_queue.empty():
                            progress_info = progress_queue.get_nowait()
                            
                            # 计算进度百分比
                            current_file = progress_info['current_file']
                            total_files = progress_info['total_files']
                            if total_files > 0:
                                progress_percent = min(90, (current_file / total_files) * 90)
                            else:
                                progress_percent = 0
                            
                            # 更新进度对话框
                            progress_dialog.update_progress(
                                value=progress_percent,
                                message=progress_info['status_message'],
                                file_count=current_file
                            )
                    except Exception as e:
                        print(f"进度更新错误: {e}")
                    
                    # 如果对话框仍然存在且未被取消，继续检查进度
                    if not progress_dialog.is_cancelled() and progress_dialog.dialog.winfo_exists():
                        self.root.after(100, check_progress)
                
                # 启动进度检查
                self.root.after(100, check_progress)
                
                # 调用数据采集控制函数
                success, result_msg, file_count = DataCaptureController.capture_data(
                    folder_path=folder_path,
                    freq_hz=freq_hz,
                    progress_callback=progress_callback,
                    num_files=num_files
                )
                
                # 如果没有被取消，则关闭进度对话框并处理结果
                if not progress_dialog.is_cancelled():
                    # 更新进度到100%
                    progress_dialog.update_progress(
                        value=100,
                        message="数据采集完成" if self.current_language == 'zh' else "Data capture completed"
                    )
                    
                    # 短暂延迟后关闭对话框
                    self.root.after(500, progress_dialog.close)
                    
                    if success:
                        # 获取文件数量
                        try:
                            # 搜索支持的文件格式
                            supported_extensions = ['.bvsp', '.dat', '.hdfv']
                            files = []
                            for ext in supported_extensions:
                                pattern = f"*{ext}"
                                files.extend(Path(folder_path).rglob(pattern))
                            file_count = len(set([str(f) for f in files]))
                        except Exception:
                            file_count = file_count if 'file_count' in locals() else 0
                        
                        # 显示完成消息
                        file_count_text = f"{file_count} 个文件" if self.current_language == 'zh' else f"{file_count} files"

                        # 缩短路径显示，避免窗口过宽
                        folder_name = os.path.basename(folder_path)
                        parent_dir = os.path.basename(os.path.dirname(folder_path))
                        short_path = f".../{parent_dir}/{folder_name}" if parent_dir else folder_name

                        complete_msg = f"数据采集完成！\n\n共采集 {file_count_text}\n文件已保存到:\n{short_path}" if self.current_language == 'zh' else f"Data capture completed!\n\nCaptured {file_count_text}\nFiles saved to:\n{short_path}"
                        complete_title = "采集完成" if self.current_language == 'zh' else "Capture Complete"
                        
                        # 使用主线程显示消息框
                        self.root.after(1000, lambda: ScaledMessageBox.showinfo(
                            complete_title,
                            complete_msg,
                            parent=self.root,
                            language=self.current_language
                        ))
                        
                        # 播放系统提示音（如果可用）
                        try:
                            import winsound
                            winsound.MessageBeep(winsound.MB_ICONINFORMATION)
                        except:
                            pass
                        
                        # 自动加载采集的数据文件夹
                        self.root.after(1500, lambda: self.select_folder_and_load(folder_path))
                        
                        # 更新状态栏
                        status_msg = f"采集完成: {file_count_text}" if self.current_language == 'zh' else f"Capture completed: {file_count_text}"
                        self.status_label.config(text=status_msg)
                    else:
                        # 显示错误消息
                        self.root.after(0, lambda: ScaledMessageBox.showerror(
                            "错误" if self.current_language == 'zh' else "Error",
                            result_msg,
                            parent=self.root,
                            language=self.current_language
                        ))
                        
                        # 更新状态栏
                        self.status_label.config(text="采集失败" if self.current_language == 'zh' else "Capture failed")
            except Exception as e:
                # 如果出现错误，关闭进度对话框并显示错误消息
                progress_dialog.close()
                
                error_msg = f"数据采集失败: {str(e)}" if self.current_language == 'zh' else f"Data capture failed: {str(e)}"
                error_title = "错误" if self.current_language == 'zh' else "Error"
                
                # 使用主线程显示错误消息
                self.root.after(0, lambda: ScaledMessageBox.showerror(
                    error_title,
                    error_msg,
                    parent=self.root,
                    language=self.current_language
                ))
        
        # 设置取消回调函数
        def on_cancel():
            cancel_msg = "数据采集已取消" if self.current_language == 'zh' else "Data capture cancelled"
            print(cancel_msg)
            
            # 显示取消消息
            self.root.after(0, lambda: ScaledMessageBox.showinfo(
                "已取消" if self.current_language == 'zh' else "Cancelled",
                cancel_msg,
                parent=self.root,
                language=self.current_language
            ))
        
        progress_dialog.set_on_cancel(on_cancel)
        
        # 启动后台线程
        threading.Thread(target=capture_thread, daemon=True).start()
        
        # 提示用户输入中心频率
        freq_title = "输入中心频率" if self.current_language == 'zh' else "Enter Center Frequency"
        freq_prompt = "请输入中心频率 (MHz):" if self.current_language == 'zh' else "Please enter center frequency (MHz):"
        
        # 使用tkinter的simpledialog创建输入对话框
        from tkinter import simpledialog
        freq_input = simpledialog.askfloat(
            title=freq_title,
            prompt=freq_prompt,
            initialvalue=2446.0,  # 默认值为2446.0 MHz
            minvalue=1.0,         # 最小值
            maxvalue=6000.0,      # 最大值
            parent=self.root
        )
        
        # 如果用户取消输入，则退出函数
        if freq_input is None:
            return
            
        # 转换单位：MHz到Hz
        freq_hz = freq_input * 1e6
        
        # 创建进度提示对话框
        progress_title = "数据采集进度" if self.current_language == 'zh' else "Data Capture Progress"
        progress_msg = "正在采集数据..." if self.current_language == 'zh' else "Capturing data..."
        
        # 使用tkinter的Toplevel创建自定义进度对话框
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title(progress_title)
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()
        
        # 设置对话框大小和位置
        dialog_width = 400
        dialog_height = 150
        x = self.root.winfo_rootx() + (self.root.winfo_width() - dialog_width) // 2
        y = self.root.winfo_rooty() + (self.root.winfo_height() - dialog_height) // 2
        progress_dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        progress_dialog.resizable(False, False)
        
        # 添加进度消息标签
        msg_label = tk.Label(progress_dialog, text=progress_msg, pady=10)
        msg_label.pack()
        
        # 添加进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_dialog, variable=progress_var, maximum=100)
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        
        # 添加取消按钮
        cancel_button = tk.Button(progress_dialog, text="取消" if self.current_language == 'zh' else "Cancel")
        cancel_button.pack(pady=10)
        
        # 更新进度信息的函数
        def update_progress(value, message):
            if progress_dialog.winfo_exists():
                progress_var.set(value)
                msg_label.config(text=message)
                progress_dialog.update()
        
        # 取消标志
        cancel_flag = [False]
        
        # 取消按钮点击事件
        def on_cancel():
            cancel_flag[0] = True
            progress_dialog.destroy()
        
        cancel_button.config(command=on_cancel)
        
        # 更新初始进度
        update_progress(0, progress_msg)
        
        # 这段代码已被新的DataCaptureController.capture_data替代，删除重复代码
        
        # 启动后台线程
        threading.Thread(target=capture_thread, daemon=True).start()
        
        # 更新进度的循环
        def update_loop():
            if progress_dialog.winfo_exists():
                # 模拟进度更新（实际应该从采集过程获取真实进度）
                current_value = progress_var.get()
                if current_value < 90:  # 最多到90%，留10%给最后的处理
                    progress_var.set(current_value + 1)
                
                # 每100ms更新一次
                progress_dialog.after(100, update_loop)
        
        # 启动进度更新循环
        progress_dialog.after(100, update_loop)
        
        # 提示用户输入中心频率
        freq_title = "输入中心频率" if self.current_language == 'zh' else "Enter Center Frequency"
        freq_prompt = "请输入中心频率 (MHz):" if self.current_language == 'zh' else "Please enter center frequency (MHz):"
        
        # 使用tkinter的simpledialog创建输入对话框
        from tkinter import simpledialog
        freq_input = simpledialog.askfloat(
            title=freq_title,
            prompt=freq_prompt,
            initialvalue=2446.0,  # 默认值为2446.0 MHz
            minvalue=1.0,         # 最小值
            maxvalue=6000.0,      # 最大值
            parent=self.root
        )
        
        # 如果用户取消输入，则退出函数
        if freq_input is None:
            return
            
        # 转换单位：MHz到Hz
        freq_hz = freq_input * 1e6
        folder_title = "选择数据保存路径" if self.current_language == 'zh' else "Select Data Save Path"
        folder_path = filedialog.askdirectory(
            title=folder_title,
            initialdir=self.last_folder_dir
        )
        
        if not folder_path:
            return  # 用户取消了选择
        
        # 更新最后选择的文件夹路径
        self.last_folder_dir = os.path.dirname(folder_path)
        self.save_config()
        
        # 检查选择的文件夹是否存在
        if os.path.exists(folder_path):
            # 如果文件夹已存在，询问用户是否清空
            confirm_msg = "所选文件夹已存在，将被清空。是否继续？" if self.current_language == 'zh' else "Selected folder exists and will be cleared. Continue?"
            if not ScaledMessageBox.askyesno(
                "确认" if self.current_language == 'zh' else "Confirm",
                confirm_msg,
                parent=self.root,
                language=self.current_language
            ):
                return  # 用户取消操作
        
        # 输入中心频率
        freq_title = "输入中心频率" if self.current_language == 'zh' else "Enter Center Frequency"
        freq_prompt = "请输入中心频率 (MHz):" if self.current_language == 'zh' else "Please enter center frequency (MHz):"
        
        # 使用简单对话框获取频率输入
        from tkinter import simpledialog
        freq_input = simpledialog.askfloat(
            freq_title,
            freq_prompt,
            parent=self.root,
            initialvalue=2446.0
        )
        
        if freq_input is None:
            return  # 用户取消了输入
        
        # 转换MHz到Hz
        center_freq = freq_input * 1e6
        
        # 创建进度对话框
        progress_title = "数据采集进行中" if self.current_language == 'zh' else "Data Capture in Progress"
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title(progress_title)
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()
        
        # 设置进度对话框大小和位置
        dialog_width = 400
        dialog_height = 150
        x = self.root.winfo_rootx() + (self.root.winfo_width() - dialog_width) // 2
        y = self.root.winfo_rooty() + (self.root.winfo_height() - dialog_height) // 2
        progress_dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # 添加进度信息标签
        progress_label = tk.Label(
            progress_dialog, 
            text="正在采集数据，请稍候..." if self.current_language == 'zh' else "Capturing data, please wait...",
            font=self.default_font
        )
        progress_label.pack(pady=20)
        
        # 添加进度条
        progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
        progress_bar.pack(fill=tk.X, padx=20, pady=10)
        progress_bar.start(10)
        
        # 添加取消按钮
        cancel_button = tk.Button(
            progress_dialog, 
            text="取消" if self.current_language == 'zh' else "Cancel",
            font=self.default_font,
            command=progress_dialog.destroy
        )
        cancel_button.pack(pady=10)
        
        # 这段代码已被新的DataCaptureController.capture_data替代，删除重复代码
        # 为了保持兼容性，直接调用capture_complete函数
        self.capture_complete(progress_dialog, folder_path)
    
    def capture_complete(self, dialog, folder_path):
        """数据采集完成后的处理"""
        # 关闭进度对话框
        if dialog and dialog.winfo_exists():
            dialog.destroy()

        # 显示完成消息
        complete_title = "采集完成" if self.current_language == 'zh' else "Capture Complete"

        # 缩短路径显示，避免窗口过宽
        folder_name = os.path.basename(folder_path)
        parent_dir = os.path.basename(os.path.dirname(folder_path))
        short_path = f".../{parent_dir}/{folder_name}" if parent_dir else folder_name

        complete_msg = f"数据采集完成！\n\n文件已保存到:\n{short_path}" if self.current_language == 'zh' else f"Data capture completed!\n\nFiles saved to:\n{short_path}"

        # 显示采集完成消息框，用户确认后再加载数据
        def on_complete_confirmed():
            # 更新状态栏
            self.status_label.config(text=f"数据采集完成，准备加载文件...")

            # 延迟一段时间后再加载文件夹，让UI有时间响应
            self.root.after(500, lambda: self.select_folder_and_load_with_warning_check(folder_path))

        # 使用after方法延迟显示消息框，避免UI阻塞
        self.root.after(100, lambda: self._show_complete_message_and_load(
            complete_title, complete_msg, on_complete_confirmed
        ))

    def _show_complete_message_and_load(self, title, message, callback):
        """显示完成消息并在用户确认后执行回调"""
        ScaledMessageBox.showinfo(
            title,
            message,
            parent=self.root,
            language=self.current_language
        )
        # 用户确认消息框后执行回调
        callback()
    
    def capture_error(self, dialog, error_msg):
        """处理数据采集过程中的错误"""
        # 关闭进度对话框
        dialog.destroy()
        
        # 显示错误消息
        error_title = "采集错误" if self.current_language == 'zh' else "Capture Error"
        error_text = f"数据采集过程中发生错误:\n{error_msg}" if self.current_language == 'zh' else f"Error during data capture:\n{error_msg}"
        ScaledMessageBox.showerror(
            error_title,
            error_text,
            parent=self.root,
            language=self.current_language
        )
    
    def select_folder_and_load(self, folder_path):
        """
        选择并加载指定文件夹中的数据文件（异步版本）

        自动加载文件夹中的数据文件，并更新文件列表和图表显示
        使用异步处理避免UI卡顿
        """
        if not folder_path:
            return

        # 在后台线程中执行文件搜索和加载
        def load_folder_thread():
            try:
                # 更新状态栏
                self.root.after(0, lambda: self.status_label.config(text=f"{self.texts['loading']}..."))

                # 搜索支持的文件格式
                supported_extensions = ['.bvsp', '.dat', '.hdfv']
                files = []

                for ext in supported_extensions:
                    pattern = f"*{ext}"
                    # 只使用 rglob 进行递归搜索，避免重复
                    files.extend(Path(folder_path).rglob(pattern))

                # 转换为字符串路径，去重并排序
                files = sorted(list(set([str(f) for f in files])))

                if files:
                    # 在主线程中更新UI
                    def update_ui():
                        # 更新文件列表
                        self.current_files = files
                        self.current_index = 0

                        # 更新文件列表显示
                        self.update_file_list()

                        # 异步加载第一个文件
                        self.load_current_file_async()

                        # 更新状态栏
                        files_text = "个文件" if self.current_language == 'zh' else "files"
                        self.status_label.config(text=f"{self.texts['loaded']}: {len(files)} {files_text}")

                        # 注意：不在这里显示采集结果弹窗，避免重复弹窗
                        # 采集完成的弹窗已经在capture_complete函数中处理

                    self.root.after(0, update_ui)
                else:
                    # 没有找到支持的文件
                    def show_no_files():
                        # 更新状态栏
                        self.status_label.config(text=f"{self.texts['ready']}")

                        # 显示警告消息
                        ScaledMessageBox.showwarning(
                            self.texts['warning'],
                            self.texts['no_files_found'],
                            parent=self.root,
                            language=self.current_language
                        )

                    self.root.after(0, show_no_files)

            except Exception as e:
                # 在主线程中显示错误
                error_msg = f"加载文件夹失败: {str(e)}" if self.current_language == 'zh' else f"Failed to load folder: {str(e)}"
                self.root.after(0, lambda: ScaledMessageBox.showerror(
                    self.texts['error'],
                    error_msg,
                    parent=self.root,
                    language=self.current_language
                ))

        # 启动后台线程
        threading.Thread(target=load_folder_thread, daemon=True).start()

    def select_folder_and_load_with_warning_check(self, folder_path):
        """
        选择并加载指定文件夹中的数据文件，并在加载完成后检查是否需要显示数据异常警告
        """
        # 初始化待显示的警告信息
        self.pending_data_warning = None

        # 定义原始的select_folder_and_load函数完成后的回调
        def on_load_complete():
            # 检查是否有待显示的数据异常警告
            if hasattr(self, 'pending_data_warning') and self.pending_data_warning:
                warning_info = self.pending_data_warning
                self.pending_data_warning = None  # 清除警告信息

                # 延迟显示警告，确保在数据加载完成后显示
                self.root.after(1000, lambda: ScaledMessageBox.showwarning(
                    warning_info['title'],
                    warning_info['message'],
                    parent=self.root,
                    language=self.current_language
                ))

        # 先调用原始的加载函数
        self.select_folder_and_load(folder_path)

        # 延迟执行完成回调，确保加载过程有足够时间完成
        self.root.after(2000, on_load_complete)

    def _ensure_dialogs_on_top(self):
        """确保活动的对话框保持在顶层"""
        try:
            # 清理已关闭的对话框
            self.active_dialogs = [dialog for dialog in self.active_dialogs if dialog.winfo_exists()]

            # 将所有活动对话框提升到顶层
            for dialog in self.active_dialogs:
                try:
                    dialog.lift()
                    dialog.attributes('-topmost', True)
                    # 立即取消topmost，避免一直置顶
                    dialog.after(100, lambda d=dialog: d.attributes('-topmost', False))
                except:
                    pass
        except:
            pass

    def _check_existing_data_files(self, folder_path):
        """
        检查文件夹中是否存在数据文件

        Args:
            folder_path: 文件夹路径

        Returns:
            list: 现有数据文件的路径列表
        """
        existing_files = []

        if not os.path.exists(folder_path):
            return existing_files

        try:
            # 支持的数据文件扩展名
            supported_extensions = ['.bvsp', '.dat', '.hdfv']

            # 搜索文件夹中的数据文件
            for ext in supported_extensions:
                pattern = f"*{ext}"
                files = list(Path(folder_path).glob(pattern))
                existing_files.extend([str(f) for f in files])

            # 去重并排序
            existing_files = sorted(list(set(existing_files)))

        except Exception as e:
            print(f"检查现有数据文件时出错: {e}")

        return existing_files
    
    def on_view_db_error(self, error_msg):
        """查看数据库失败回调"""
        ScaledMessageBox.showerror(self.texts['error'], error_msg,
                                  parent=self.root, language=self.current_language)
    
    def show_database_window(self, db_records):
        """显示数据库结构窗口"""
        # 创建新窗口（1.5倍放大）
        db_window = tk.Toplevel(self.root)
        db_window.title(self.texts['database_structure'])
        
        # 窗口大小和位置（1.5倍放大）
        window_width = int(1000 * 1.5)   # 1500
        window_height = int(700 * 1.5)   # 1050
        screen_width = db_window.winfo_screenwidth()
        screen_height = db_window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        db_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        db_window.transient(self.root)
        db_window.grab_set()
        db_window.resizable(True, True)
        
        # 主框架
        main_frame = tk.Frame(db_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=int(15 * 1.5), pady=int(15 * 1.5))
        
        # 标题信息（1.5倍放大字体）
        info_frame = tk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, int(15 * 1.5)))
        
        title_font = self.fonts.get('title_font', ('Arial', int(12 * 1.5), 'bold'))
        info_font = self.fonts.get('status_font', ('Arial', int(10 * 1.5)))
        
        title_label = tk.Label(info_frame, text="向量数据库内容", font=title_font)
        title_label.pack(anchor='w')
        
        info_text = f"总记录数: {len(db_records)}\n数据库文件: ../config/sig-vectordB.cfg"
        info_label = tk.Label(info_frame, text=info_text, font=info_font, justify=tk.LEFT)
        info_label.pack(anchor='w', pady=(int(5 * 1.5), 0))

        # 添加说明文字
        instruction_frame = tk.Frame(main_frame)
        instruction_frame.pack(fill=tk.X, pady=(int(10 * 1.5), int(15 * 1.5)))

        instruction_text = "提示：点击行首复选框进行多选删除，点击行内容查看分析图"
        instruction_label = tk.Label(instruction_frame, text=instruction_text, font=info_font, fg='blue')
        instruction_label.pack(anchor='w')

        # 创建树状表格框架
        tree_frame = tk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview（1.5倍放大）- 添加选择列和record_id列
        columns = ('select', 'record_id', 'class_id', 'class_name', 'file_path', 'sample_rate', 'bandwidth')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=int(20 * 1.5))

        # 设置列标题
        tree.heading('select', text='选择')
        tree.heading('record_id', text='数据编号')
        tree.heading('class_id', text='类别ID')
        tree.heading('class_name', text='类别名称')
        tree.heading('file_path', text='文件路径')
        tree.heading('sample_rate', text='采样率')
        tree.heading('bandwidth', text='带宽')

        # 设置列宽（1.5倍放大）
        tree.column('select', width=int(80 * 1.5), anchor='center')
        tree.column('record_id', width=int(100 * 1.5), anchor='center')
        tree.column('class_id', width=int(100 * 1.5), anchor='center')
        tree.column('class_name', width=int(160 * 1.5), anchor='w')
        tree.column('file_path', width=int(300 * 1.5), anchor='w')
        tree.column('sample_rate', width=int(120 * 1.5), anchor='center')
        tree.column('bandwidth', width=int(120 * 1.5), anchor='center')
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 初始化选择状态字典
        db_selection_state = {}
        current_selected_item = tk.StringVar(value="")  # 当前选中的TreeView项目ID

        # 填充数据 - 添加选择列
        for i, record in enumerate(db_records):
            # 默认未选中
            db_selection_state[i] = False
            check_mark = '☐'  # 未选中的复选框

            # 在记录前添加选择状态
            record_with_select = (check_mark,) + record
            item = tree.insert('', 'end', values=record_with_select)

            # 设置交替行颜色
            if i % 2 == 0:
                tree.item(item, tags=('evenrow',))
            else:
                tree.item(item, tags=('oddrow',))
        
        # 配置交替行颜色
        tree.tag_configure('evenrow', background='#f0f0f0')
        tree.tag_configure('oddrow', background='white')
        
        # 布局TreeView和滚动条
        tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 添加TreeView点击事件处理
        def on_tree_click(event):
            """处理TreeView点击事件"""
            # 使用identify_row来获取点击的行
            clicked_item = tree.identify_row(event.y)
            if clicked_item:
                # 获取点击的列
                column = tree.identify_column(event.x)
                if column == '#1':  # 选择列 - 用于多选删除
                    # 获取行索引
                    row_index = tree.index(clicked_item)

                    # 切换多选状态
                    db_selection_state[row_index] = not db_selection_state[row_index]

                    # 更新显示
                    current_values = list(tree.item(clicked_item, 'values'))
                    current_values[0] = '☑' if db_selection_state[row_index] else '☐'
                    tree.item(clicked_item, values=current_values)

                    # 阻止TreeView的默认选择行为
                    return "break"
                else:
                    # 点击其他列 - 选中行用于查看分析图
                    # 让TreeView正常处理选中
                    current_selected_item.set(clicked_item)
                    # 更新查看分析图按钮状态
                    update_view_analysis_button()

        # 添加TreeView选择事件处理
        def on_tree_select(event):
            """处理TreeView选择事件"""
            selection = tree.selection()
            if selection:
                current_selected_item.set(selection[0])
            else:
                current_selected_item.set("")
            # 更新查看分析图按钮状态
            update_view_analysis_button()

        tree.bind('<Button-1>', on_tree_click)
        # 绑定选择事件
        tree.bind("<<TreeviewSelect>>", on_tree_select)

        # 按钮框架（1.5倍放大）
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(int(15 * 1.5), 0))

        button_font = self.fonts.get('button_font', ('Arial', int(10 * 1.5)))

        # 全选/取消全选按钮
        def toggle_select_all():
            """全选/取消全选"""
            # 检查当前是否有选中的项
            selected_count = sum(1 for selected in db_selection_state.values() if selected)
            select_all = selected_count < len(db_records)

            # 更新所有项的选择状态
            for i, item in enumerate(tree.get_children()):
                db_selection_state[i] = select_all
                current_values = list(tree.item(item, 'values'))
                current_values[0] = '☑' if select_all else '☐'
                tree.item(item, values=current_values)

        select_all_btn = tk.Button(button_frame, text="全选/取消", command=toggle_select_all,
                                  font=button_font, width=int(12 * 1.5))
        select_all_btn.pack(side=tk.LEFT, padx=(0, int(10 * 1.5)))

        # 查看分析图按钮
        def view_selected_analysis():
            """查看选中记录的分析图"""
            # 获取当前TreeView选中的项目
            selected_item_id = current_selected_item.get()

            if not selected_item_id:
                ScaledMessageBox.showwarning("警告", "请先点击选择一条记录（行会变为蓝色）", parent=db_window)
                return

            # 获取选中项的索引
            try:
                selected_index = tree.index(selected_item_id)
                if selected_index < len(db_records):
                    selected_record = db_records[selected_index]
                    self.show_record_analysis_window(selected_record, selected_index)
                else:
                    ScaledMessageBox.showwarning("警告", "选中的记录索引无效", parent=db_window)
            except Exception as e:
                ScaledMessageBox.showwarning("警告", f"获取选中记录失败: {str(e)}", parent=db_window)

        view_analysis_btn = tk.Button(button_frame, text="查看分析图", command=view_selected_analysis,
                                     font=button_font, width=int(12 * 1.5), bg='#ccffcc')
        view_analysis_btn.pack(side=tk.LEFT, padx=(0, int(10 * 1.5)))

        # 更新查看分析图按钮状态的函数
        def update_view_analysis_button():
            """更新查看分析图按钮的启用状态"""
            # 检查是否有TreeView选中项
            selected_item_id = current_selected_item.get()
            if selected_item_id:
                view_analysis_btn.config(state='normal')
            else:
                view_analysis_btn.config(state='disabled')

        # 初始状态：禁用查看分析图按钮
        view_analysis_btn.config(state='disabled')

        # 删除选中记录按钮
        def delete_selected_records():
            """删除选中的记录"""
            selected_indices = [i for i, selected in db_selection_state.items() if selected]
            if not selected_indices:
                ScaledMessageBox.showwarning("警告", "请先选择要删除的记录", parent=db_window)
                return

            # 调用删除流程（将在下一个任务中实现）
            self.delete_database_records_with_confirmation(selected_indices, db_window, tree, db_records, db_selection_state)

        delete_btn = tk.Button(button_frame, text="删除选中", command=delete_selected_records,
                              font=button_font, width=int(12 * 1.5), bg='#ffcccc')
        delete_btn.pack(side=tk.LEFT, padx=(0, int(10 * 1.5)))

        # 更新特征向量按钮
        def update_vectors():
            """更新数据库中所有记录的特征向量"""
            self.update_vectors_action(db_window)

        update_vectors_btn = tk.Button(button_frame, text="更新特征向量", command=update_vectors,
                                      font=button_font, width=int(15 * 1.5), bg='#ffffcc')
        update_vectors_btn.pack(side=tk.LEFT, padx=(0, int(10 * 1.5)))

        # 关闭按钮
        close_btn = tk.Button(button_frame, text=self.texts['close'], command=db_window.destroy,
                             font=button_font, width=int(12 * 1.5))
        close_btn.pack(side=tk.RIGHT)

    def show_record_analysis_window(self, record, record_index):
        """
        显示数据库记录的分析图窗口

        Args:
            record: 数据库记录元组 (record_id, class_id, class_name, file_path, sample_rate, bandwidth, start_sample, end_sample)
            record_index: 记录在数据库中的索引
        """
        # 添加性能分析
        import time
        start_time = time.time()
        # 创建进度对话框
        progress_window = tk.Toplevel(self.root)
        progress_window.title("加载分析图")
        progress_window.geometry("400x150")
        progress_window.transient(self.root)
        progress_window.grab_set()
        progress_window.resizable(False, False)

        # 居中显示
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() - 400) // 2
        y = (progress_window.winfo_screenheight() - 150) // 2
        progress_window.geometry(f"400x150+{x}+{y}")

        # 进度框架
        progress_frame = tk.Frame(progress_window)
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 进度标签
        progress_label = tk.Label(progress_frame, text="正在加载信号数据...",
                                 font=self.fonts.get('status_font', ('Arial', 10)))
        progress_label.pack(pady=(0, 10))

        # 进度条
        from tkinter import ttk
        progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        progress_bar.start()

        # 取消按钮
        cancelled = tk.BooleanVar(value=False)

        def cancel_loading():
            cancelled.set(True)
            progress_window.destroy()

        cancel_btn = tk.Button(progress_frame, text="取消", command=cancel_loading,
                              font=self.fonts.get('button_font', ('Arial', 10)))
        cancel_btn.pack()

        # 在后台线程中执行加载
        def load_analysis_data():
            error_occurred = False
            error_message = ""

            try:
                if cancelled.get():
                    return

                # 更新进度
                self.root.after(0, lambda: progress_label.config(text="导入模块..."))

                # 导入必要的模块
                try:
                    # 使用绝对导入避免相对导入问题
                    import sys
                    import os
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    if current_dir not in sys.path:
                        sys.path.append(current_dir)

                    from core_functions import extract_signal_from_record, validate_and_preprocess_signal, estimate_segment_info_from_record
                    import matplotlib.pyplot as plt
                    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
                    import numpy as np
                except ImportError as ie:
                    error_message = f"模块导入失败: {str(ie)}"
                    error_occurred = True

                if cancelled.get():
                    return

                if error_occurred:
                    self.root.after(0, lambda: progress_window.destroy())
                    self.root.after(0, lambda msg=error_message: ScaledMessageBox.showerror("错误", msg, parent=self.root))
                    return

                # 更新进度
                self.root.after(0, lambda: progress_label.config(text="提取信号数据..."))

                # 性能分析：提取信号数据
                step_start = time.time()
                success, full_signal_data, segment_info, metadata, error_msg = extract_signal_from_record(record)
                print(f"提取信号数据耗时: {time.time() - step_start:.3f}秒")

                if cancelled.get():
                    return

                if not success:
                    err_msg = error_msg  # 捕获错误信息
                    self.root.after(0, lambda: progress_window.destroy())
                    self.root.after(0, lambda msg=err_msg: ScaledMessageBox.showerror("错误", f"无法提取信号数据:\n{msg}", parent=self.root))
                    return

                # 更新进度
                self.root.after(0, lambda: progress_label.config(text="预处理信号..."))

                # 性能分析：验证和预处理信号
                step_start = time.time()
                success, processed_signal, processed_metadata, error_msg = validate_and_preprocess_signal(full_signal_data, metadata)
                print(f"验证和预处理信号耗时: {time.time() - step_start:.3f}秒")

                if cancelled.get():
                    return

                if not success:
                    err_msg = error_msg  # 捕获错误信息
                    self.root.after(0, lambda: progress_window.destroy())
                    self.root.after(0, lambda msg=err_msg: ScaledMessageBox.showerror("错误", f"信号预处理失败:\n{msg}", parent=self.root))
                    return

                # 更新进度
                self.root.after(0, lambda: progress_label.config(text="创建分析窗口..."))

                # 总耗时
                total_time = time.time() - start_time
                print(f"总加载耗时: {total_time:.3f}秒")

                # 在主线程中创建分析图窗口
                self.root.after(0, lambda: progress_window.destroy())
                self.root.after(0, lambda rec=record, idx=record_index, sig=processed_signal, meta=processed_metadata, seg=segment_info:
                               self.create_analysis_window(rec, idx, sig, meta, seg))

            except Exception as e:
                if not cancelled.get():
                    error_msg = str(e)  # 捕获错误信息
                    self.root.after(0, lambda: progress_window.destroy())
                    self.root.after(0, lambda msg=error_msg: ScaledMessageBox.showerror("错误", f"显示分析图时发生异常:\n{msg}", parent=self.root))

        # 启动后台线程
        import threading
        loading_thread = threading.Thread(target=load_analysis_data, daemon=True)
        loading_thread.start()

    def create_analysis_window(self, record, record_index, signal_data, metadata, segment_info):
        """
        创建分析图窗口

        Args:
            record: 数据库记录
            record_index: 记录索引
            signal_data: 处理后的完整信号数据
            metadata: 信号元数据
            segment_info: 分段信息
        """
        # 性能分析
        import time
        start_time = time.time()
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            import numpy as np

            # 创建分析窗口
            analysis_window = tk.Toplevel(self.root)

            # 窗口标题（兼容新旧格式）
            record_id = record[0]    # 数据编号
            class_id = record[1]     # 类别ID
            class_name = record[2]   # 类别名称
            file_path = record[3]    # 文件路径
            sample_rate = record[4]  # 采样率
            bandwidth = record[5]    # 带宽
            window_title = f"分析图 - {metadata.get('class_name', class_name)} (ID: {class_id})"
            analysis_window.title(window_title)

            # 窗口大小和位置
            window_width = int(1400 * 1.2)   # 1680
            window_height = int(1000 * 1.2)  # 1200
            screen_width = analysis_window.winfo_screenwidth()
            screen_height = analysis_window.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            analysis_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

            analysis_window.transient(self.root)
            analysis_window.resizable(True, True)

            # 创建主框架
            main_frame = tk.Frame(analysis_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # 信息框架
            info_frame = tk.Frame(main_frame)
            info_frame.pack(fill=tk.X, pady=(0, 15))

            # 显示记录信息
            info_font = self.fonts.get('status_font', ('Arial', 12))
            title_font = self.fonts.get('title_font', ('Arial', 14, 'bold'))

            title_label = tk.Label(info_frame, text="数据库记录分析图", font=title_font)
            title_label.pack(anchor='w')

            # 记录详细信息
            info_text = f"记录索引: {record_index} | 类别: {metadata.get('class_name', 'Unknown')} | "
            info_text += f"采样率: {metadata.get('fs', 0)/1e6:.2f} MHz | "
            info_text += f"带宽: {metadata.get('bw', 0)/1e6:.2f} MHz\n"
            info_text += f"文件: {os.path.basename(metadata.get('file_path', ''))}"

            if metadata.get('is_downsampled', False):
                info_text += f" | 抽样显示: {metadata.get('processed_length', 0):,} / {metadata.get('original_length', 0):,} 点"

            info_label = tk.Label(info_frame, text=info_text, font=info_font, justify=tk.LEFT)
            info_label.pack(anchor='w', pady=(5, 0))

            # 创建matplotlib图表
            plot_start = time.time()
            self.create_analysis_plots(main_frame, signal_data, metadata, segment_info, analysis_window)
            print(f"绘制分析图表耗时: {time.time() - plot_start:.3f}秒")
            print(f"创建分析窗口总耗时: {time.time() - start_time:.3f}秒")

        except Exception as e:
            ScaledMessageBox.showerror("错误", f"创建分析窗口失败:\n{str(e)}", parent=self.root)

    def create_analysis_plots(self, parent_frame, signal_data, metadata, segment_info, parent_window):
        """
        创建分析图表（完全复用首页绘制方法）

        Args:
            parent_frame: 父框架
            signal_data: 完整信号数据
            metadata: 信号元数据
            segment_info: 分段信息
            parent_window: 父窗口
        """
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            import numpy as np

            # 创建图表框架
            plot_frame = tk.Frame(parent_frame)
            plot_frame.pack(fill=tk.BOTH, expand=True)

            # 创建matplotlib图表 - 优化尺寸和创建方式以提高速度
            fig, axes = plt.subplots(4, 1, figsize=(8, 10), tight_layout=True)  # 更快的创建方式
            fig.suptitle(f"信号分析图 - {metadata.get('class_name', 'Unknown')}", fontsize=12, fontweight='bold')

            # 获取信号参数
            fs = metadata.get('fs', 1e6)
            fc = metadata.get('fc', 0)

            # 使用subplots创建的axes（已经创建好了）
            analysis_axes = axes

            # 设置当前信号和元数据（临时设置，用于复用首页方法）
            original_signal = getattr(self, 'current_signal', None)
            original_metadata = getattr(self, 'current_metadata', None)
            original_computing = getattr(self, 'is_computing', False)
            original_axes = getattr(self, 'axes', None)

            self.current_signal = signal_data
            self.current_metadata = metadata
            self.is_computing = False
            self.axes = analysis_axes

            try:
                # 完全复用首页的绘制逻辑
                print(f"开始绘制分析图表，信号长度: {len(signal_data)}, fs: {fs/1e6:.1f}MHz, fc: {fc/1e6:.1f}MHz")

                # 创建进度显示
                progress_text = tk.Label(plot_frame, text="正在绘制图表，请稍候...",
                                       font=self.fonts.get('status_font', ('Arial', 12)))
                progress_text.pack(pady=50)
                parent_window.update()

                # 分步骤绘制，每步更新界面
                progress_text.config(text="正在绘制时域图表...")
                parent_window.update()

                self.plot_analysis_using_homepage_methods(signal_data, fs, fc)

                # 添加分段标记（如果有分段信息）
                print(f"分段信息调试: {segment_info}")
                if segment_info and segment_info.get('has_segment', False):
                    progress_text.config(text="正在添加分段标记...")
                    parent_window.update()
                    print(f"开始添加分段标记: start={segment_info.get('start_sample')}, end={segment_info.get('end_sample')}, center_freq={segment_info.get('center_freq')}")
                    self.add_segment_markers_to_analysis_plots(segment_info, fs, fc)
                else:
                    print(f"未添加分段标记: segment_info={segment_info}, has_segment={segment_info.get('has_segment', False) if segment_info else 'None'}")

                # 移除进度显示
                progress_text.destroy()
                print("分析图表绘制完成")

            finally:
                # 恢复原始状态
                self.current_signal = original_signal
                self.current_metadata = original_metadata
                self.is_computing = original_computing
                self.axes = original_axes

            # 调整布局，增加间距避免重叠
            plt.tight_layout(rect=[0, 0.03, 1, 0.95], pad=2.0, h_pad=3.0)

            # 将matplotlib图表嵌入到tkinter中
            canvas = FigureCanvasTkAgg(fig, plot_frame)
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

            # 添加工具栏
            toolbar_frame = tk.Frame(parent_frame)
            toolbar_frame.pack(fill=tk.X, pady=(5, 0))

            toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
            toolbar.update()

            # 添加按钮框架
            button_frame = tk.Frame(parent_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 保存图片按钮
            def save_plot():
                """保存图片"""
                try:
                    from tkinter import filedialog
                    filename = filedialog.asksaveasfilename(
                        parent=parent_window,
                        title="保存分析图",
                        defaultextension=".png",
                        filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")]
                    )
                    if filename:
                        fig.savefig(filename, dpi=300, bbox_inches='tight')
                        ScaledMessageBox.showinfo("成功", f"图片已保存到:\n{filename}", parent=parent_window)
                except Exception as e:
                    ScaledMessageBox.showerror("错误", f"保存图片失败:\n{str(e)}", parent=parent_window)

            save_btn = tk.Button(button_frame, text="保存图片", command=save_plot,
                               font=self.fonts.get('button_font', ('Arial', 10)), width=12)
            save_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 关闭按钮
            close_btn = tk.Button(button_frame, text="关闭", command=parent_window.destroy,
                                font=self.fonts.get('button_font', ('Arial', 10)), width=12)
            close_btn.pack(side=tk.RIGHT)

        except Exception as e:
            import traceback
            error_detail = f"创建分析图表失败: {str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
            print(error_detail)
            ScaledMessageBox.showerror("错误", error_detail, parent=parent_window)

    def plot_analysis_using_homepage_methods(self, signal_data, fs, fc):
        """使用首页的绘制方法绘制分析图表"""
        try:
            import numpy as np

            # 获取信号参数
            N_sig = len(signal_data)
            sig = signal_data  # 复数信号

            # 1. 时域信号点值图（按首页优化策略）
            print("绘制时域信号点值图...")
            ax1 = self.axes[0]
            # 按首页的策略：最多显示500,000点，但对于分析图表进一步优化
            max_points = 100000  # 平衡显示质量和速度
            indices, step = self.create_smart_sampling_indices(N_sig, max_points)

            # 抽样信号数据
            signal_i_display = np.real(sig[indices])
            signal_q_display = np.imag(sig[indices])
            indices_1e5 = indices / 1e5

            # 使用首页相同的绘图方法
            ax1.plot(indices_1e5, signal_i_display, 'b-', linewidth=0.5, rasterized=True)
            ax1.plot(indices_1e5, signal_q_display, 'r-', linewidth=0.5, rasterized=True)
            ax1.set_title(self.texts['time_domain_points'])
            ax1.set_xlabel(self.texts['time_points_unit'])
            ax1.set_ylabel(self.texts['signal_voltage'])
            ax1.grid(True, alpha=0.3)

            # 设置y轴范围
            y_min, y_max = self.calculate_adaptive_ylim(signal_i_display, signal_q_display)
            ax1.set_ylim(y_min, y_max)
            ax1.set_xlim(indices_1e5[0], indices_1e5[-1])

            # 2. 时域信号图（完全复用首页方法）
            ax2 = self.axes[1]
            signal_time_display = indices / fs * 1000  # 时间(ms)
            signal_time_start = 0
            signal_time_end = (N_sig - 1) / fs * 1000

            ax2.plot(signal_time_display, signal_i_display, 'b-', linewidth=0.5)
            ax2.plot(signal_time_display, signal_q_display, 'r-', linewidth=0.5)
            ax2.set_title(self.texts['time_domain_signal'])
            ax2.set_xlabel(self.texts['time_ms'])
            ax2.set_ylabel(self.texts['voltage_v'])
            ax2.grid(True, alpha=0.3)
            ax2.set_ylim(y_min, y_max)
            ax2.set_xlim(signal_time_start, signal_time_end)

            # 3. 频谱图（使用首页的同步FFT方法）
            print("绘制频谱图...")
            ax3 = self.axes[2]
            freqs_mhz, fftdata = self.compute_fft_sync(sig, fs, fc)

            ax3.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)
            ax3.set_title(f'{self.texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
            ax3.set_xlabel(self.texts['frequency_mhz'])
            ax3.set_ylabel(self.texts['spectrum_value'])
            ax3.grid(True, alpha=0.3)
            ax3.set_xlim(freqs_mhz[0], freqs_mhz[-1])

            # 4. 时频图（使用首页的同步时频图方法）
            print("绘制时频图...")
            ax4 = self.axes[3]
            stft_result = self.compute_spectrogram_sync(sig, fs, fc)

            if stft_result and len(stft_result) >= 4:
                F_mhz, T_ms, Pxx_db, success = stft_result
                if success and F_mhz is not None and T_ms is not None and Pxx_db is not None:
                    # 严格按照首页的方式绘制时频图
                    # 设置显示范围：[X轴最小值, X轴最大值, Y轴最小值, Y轴最大值]
                    # 对应：[时间最小值, 时间最大值, 频率最小值, 频率最大值]
                    extent = [T_ms[0], T_ms[-1], F_mhz[0], F_mhz[-1]]

                    # 使用imshow显示时频图（与首页完全一致）
                    # 注意：Pxx_db的维度应该是[频率bins, 时间frames]，正好对应imshow的[行,列]
                    im = ax4.imshow(Pxx_db, aspect='auto', cmap='viridis',
                                   extent=extent, origin='lower',
                                   interpolation='bilinear')

                    ax4.set_title(f'{self.texts["time_frequency"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                    ax4.set_xlabel(self.texts['time_ms'])  # X轴：时间(ms)
                    ax4.set_ylabel(self.texts['frequency_mhz'])  # Y轴：频率(MHz)

                    # 设置轴范围 - 时间轴对应完整数据范围
                    if len(T_ms) > 1:
                        ax4.set_xlim(T_ms[0], T_ms[-1])
                    if len(F_mhz) > 1:
                        ax4.set_ylim(F_mhz[0], F_mhz[-1])

                    print(f"时频图绘制完成: 时间范围 {T_ms[0]:.1f}-{T_ms[-1]:.1f}ms, 频率范围 {F_mhz[0]:.1f}-{F_mhz[-1]:.1f}MHz")
                else:
                    ax4.text(0.5, 0.5, '时频图计算失败', ha='center', va='center', transform=ax4.transAxes)
            else:
                ax4.text(0.5, 0.5, '时频图数据无效', ha='center', va='center', transform=ax4.transAxes)

        except Exception as e:
            print(f"绘制分析图表失败: {e}")
            for i, ax in enumerate(self.axes):
                ax.text(0.5, 0.5, f'图表{i+1}绘制失败:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes)

    def add_segment_markers_to_analysis_plots(self, segment_info, fs, fc):
        """在分析图表中添加分段标记（与首页样式一致）"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # 获取分段信息
            start_sample = segment_info.get('start_sample', 0)
            end_sample = segment_info.get('end_sample', 0)
            class_name = segment_info.get('class_name', 'Unknown')
            center_freq = segment_info.get('center_freq', fc)
            bandwidth = segment_info.get('bandwidth', 0)

            # 计算时间信息
            start_time_ms = start_sample / fs * 1000
            end_time_ms = end_sample / fs * 1000
            duration_ms = end_time_ms - start_time_ms

            # 分段颜色（与首页一致）
            segment_color = 'yellow'

            # 1. 在时域信号点值图上标记
            ax1 = self.axes[0]
            start_pos_1e5 = start_sample / 1e5
            end_pos_1e5 = end_sample / 1e5
            ax1.axvspan(start_pos_1e5, end_pos_1e5, color=segment_color, alpha=0.3, label='选中分段')

            # 添加分段信息标注
            segment_center_1e5 = (start_pos_1e5 + end_pos_1e5) / 2
            y_min, y_max = ax1.get_ylim()
            text_y = y_max - (y_max - y_min) * 0.1
            info_text = f"{class_name}\n{center_freq/1e6:.1f}MHz\n{bandwidth/1e3:.1f}kHz\n{duration_ms:.2f}ms"
            ax1.text(segment_center_1e5, text_y, info_text, ha='center', va='top', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.4', fc=segment_color, ec='black', lw=0.5, alpha=0.85))

            # 2. 在时域信号图上标记
            ax2 = self.axes[1]
            ax2.axvspan(start_time_ms, end_time_ms, color=segment_color, alpha=0.3, label='选中分段')

            # 3. 在频谱图上标记中心频率
            ax3 = self.axes[2]
            center_freq_mhz = center_freq / 1e6
            bandwidth_mhz = bandwidth / 1e6
            freq_start = center_freq_mhz - bandwidth_mhz / 2
            freq_end = center_freq_mhz + bandwidth_mhz / 2

            # 标记频率范围
            y_min, y_max = ax3.get_ylim()
            ax3.axvspan(freq_start, freq_end, color=segment_color, alpha=0.3, label='信号带宽')
            ax3.axvline(center_freq_mhz, color='red', linestyle='--', linewidth=2, alpha=0.8, label='中心频率')

            # 4. 在时频图上标记（完全按照首页方式）
            ax4 = self.axes[3]
            # 标记时间范围
            ax4.axvline(start_time_ms, color='white', linestyle='--', linewidth=2, alpha=0.8)
            ax4.axvline(end_time_ms, color='white', linestyle='--', linewidth=2, alpha=0.8)
            # 标记频率范围
            ax4.axhline(center_freq_mhz, color='white', linestyle='--', linewidth=2, alpha=0.8)

            # 在时频图上添加文本标签（放在合适位置，避免覆盖信号）
            # 获取时频图的X轴和Y轴范围
            tf_x_min, tf_x_max = ax4.get_xlim()
            tf_y_min, tf_y_max = ax4.get_ylim()
            tf_x_range = tf_x_max - tf_x_min
            tf_y_range = tf_y_max - tf_y_min

            # 计算标签位置 - 放在时频图的右上角
            tf_text_x = tf_x_max - tf_x_range * 0.02  # 右边缘内侧
            tf_text_y = tf_y_max - tf_y_range * 0.05  # 顶部内侧

            # 添加信息文本框
            ax4.text(tf_text_x, tf_text_y, info_text,
                    ha='right', va='top', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.4', fc=segment_color, ec='black', lw=0.5, alpha=0.85))

            print(f"时频图标签位置: x={tf_text_x:.1f}, y={tf_text_y:.1f}")

            print(f"分段标记添加完成: {class_name}, {center_freq_mhz:.1f}MHz, {duration_ms:.2f}ms")

        except Exception as e:
            print(f"添加分段标记失败: {e}")

    def compute_spectrogram_fast(self, signal, fs, fc):
        """快速时频图计算（用于分析图表，优化速度）"""
        try:
            import numpy as np
            from scipy.signal import spectrogram

            # 减少信号长度以提高计算速度
            max_samples = 200000  # 最多处理20万个样本
            if len(signal) > max_samples:
                step = len(signal) // max_samples
                signal = signal[::step]
                fs_effective = fs / step
            else:
                fs_effective = fs

            # 使用较小的窗口参数以提高速度
            nperseg = min(1024, len(signal) // 10)  # 减小窗口大小
            noverlap = nperseg // 4  # 减少重叠

            # 计算时频图
            f, t, Sxx = spectrogram(signal, fs=fs_effective, nperseg=nperseg, noverlap=noverlap)

            # 转换为dB
            Sxx_db = 10 * np.log10(np.abs(Sxx) + 1e-12)

            # 转换频率到MHz（相对于中心频率）
            F_mhz = (f + fc) / 1e6

            # 转换时间到ms
            T_ms = t * 1000

            print(f"快速时频图计算完成: {Sxx_db.shape}, 窗口={nperseg}, 重叠={noverlap}")

            return F_mhz, T_ms, Sxx_db

        except Exception as e:
            print(f"快速时频图计算失败: {e}")
            return None, None, None

    def plot_homepage_time_domain_points(self, ax, signal_data, fs, metadata, segment_info):
        """绘制时域信号点值图（严格按照首页方式）"""
        try:
            import numpy as np

            # 按照首页的智能抽样方式
            max_points = 500000  # 与首页一致的最大点数
            indices, step = self.create_smart_sampling_indices(len(signal_data), max_points)

            # 抽样信号数据
            sampled_signal = signal_data[indices]
            sampled_indices = indices / 1e5  # 转换为1e5单位

            # 提取I和Q分量
            signal_i = np.real(sampled_signal)
            signal_q = np.imag(sampled_signal)

            # 绘制I和Q信号（与首页一致的样式）
            ax.plot(sampled_indices, signal_i, 'b-', linewidth=0.5, label='I分量', alpha=0.8)
            ax.plot(sampled_indices, signal_q, 'r-', linewidth=0.5, label='Q分量', alpha=0.8)

            # 标出选中的分段
            if segment_info and segment_info.get('has_segment', False):
                start_idx = segment_info.get('start_sample', 0)
                end_idx = segment_info.get('end_sample', len(signal_data))

                # 转换为1e5单位
                start_1e5 = start_idx / 1e5
                end_1e5 = end_idx / 1e5

                # 添加分段区域高亮（与首页分段标记一致）
                ax.axvspan(start_1e5, end_1e5, color='yellow', alpha=0.3, label='选中分段')

                # 添加分段边界线
                ax.axvline(start_1e5, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                ax.axvline(end_1e5, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

                # 添加分段信息文本
                segment_center = (start_1e5 + end_1e5) / 2
                y_min, y_max = ax.get_ylim()
                text_y = y_max - (y_max - y_min) * 0.1

                class_name = segment_info.get('class_name', 'Unknown')
                duration_ms = segment_info.get('duration', 0) * 1000
                info_text = f"{class_name}\n{duration_ms:.2f} ms"

                ax.text(segment_center, text_y, info_text,
                       ha='center', va='top', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.4', fc='yellow', ec='black', lw=0.5, alpha=0.85))

            # 设置标题和标签（与首页一致）
            ax.set_title('时域信号点值图', fontsize=10, pad=10)
            ax.set_xlabel('样本点数 (×1e5)')
            ax.set_ylabel('幅度')
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'时域信号点值图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_homepage_time_domain_signal(self, ax, signal_data, fs, metadata, segment_info):
        """绘制时域信号图（严格按照首页方式）"""
        try:
            import numpy as np

            # 按照首页的智能抽样方式
            max_points = 500000  # 与首页一致的最大点数
            indices, step = self.create_smart_sampling_indices(len(signal_data), max_points)

            # 抽样信号数据
            sampled_signal = signal_data[indices]

            # 创建时间轴（毫秒）- 与首页一致
            time_ms = indices / fs * 1000

            # 计算信号幅度（与首页一致）
            signal_amplitude = np.abs(sampled_signal)

            # 绘制信号幅度（与首页一致的样式）
            ax.plot(time_ms, signal_amplitude, 'b-', linewidth=0.8, alpha=0.8)

            # 标出选中的分段
            if segment_info and segment_info.get('has_segment', False):
                start_time = segment_info.get('start_time', 0) * 1000  # 转换为ms
                end_time = segment_info.get('end_time', 0) * 1000    # 转换为ms

                # 添加分段区域高亮
                ax.axvspan(start_time, end_time, color='yellow', alpha=0.3, label='选中分段')

                # 添加分段边界线
                ax.axvline(start_time, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                ax.axvline(end_time, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

            # 设置标题和标签（与首页一致）
            ax.set_title('时域信号图', fontsize=10, pad=10)
            ax.set_xlabel('时间 (ms)')
            ax.set_ylabel('幅度')
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'时域信号图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_homepage_frequency_spectrum(self, ax, signal_data, fs, fc, metadata, segment_info):
        """绘制频谱图（严格按照首页方式）"""
        try:
            import numpy as np

            # 使用首页的FFT计算方法
            freqs_mhz, fftdata = self.compute_fft_sync(signal_data, fs, fc)

            # 绘制频谱（与首页一致的样式）
            ax.plot(freqs_mhz, fftdata, 'b-', linewidth=0.8, alpha=0.8)

            # 标出选中分段的频率范围
            if segment_info and segment_info.get('has_segment', False):
                # 计算分段信号的频谱
                start_idx = segment_info.get('start_sample', 0)
                end_idx = segment_info.get('end_sample', len(signal_data))
                segment_signal = signal_data[start_idx:end_idx]

                if len(segment_signal) > 1000:  # 确保分段足够长
                    segment_freqs_mhz, segment_fftdata = self.compute_fft_sync(segment_signal, fs, fc)

                    # 绘制分段频谱（高亮显示）
                    ax.plot(segment_freqs_mhz, segment_fftdata, 'r-', linewidth=2, alpha=1.0, label='选中分段频谱')

                # 添加带宽标记
                bw = metadata.get('bw', fs)
                bw_mhz = bw / 1e6
                fc_mhz = fc / 1e6

                ax.axvline(fc_mhz - bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1, label='带宽边界')
                ax.axvline(fc_mhz + bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1)
                ax.axvline(fc_mhz, color='yellow', linestyle='-', alpha=0.8, linewidth=1, label='中心频率')

            # 设置标题和标签（与首页一致）
            ax.set_title('频谱图', fontsize=10, pad=10)
            ax.set_xlabel('频率 (MHz)')
            ax.set_ylabel('幅度')
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'频谱图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_homepage_time_frequency(self, ax, signal_data, fs, fc, metadata, segment_info):
        """绘制时频图（严格按照首页方式）"""
        try:
            import numpy as np

            # 使用首页的时频图计算方法
            F_mhz, T_ms, Pxx_db, success = self.compute_spectrogram_sync(signal_data, fs, fc)

            if success:
                # 绘制时频图（与首页一致的样式）
                im = ax.pcolormesh(T_ms, F_mhz, Pxx_db, shading='auto', cmap='viridis')

                # 标出选中分段的时间范围
                if segment_info and segment_info.get('has_segment', False):
                    start_time = segment_info.get('start_time', 0) * 1000  # 转换为ms
                    end_time = segment_info.get('end_time', 0) * 1000    # 转换为ms

                    # 添加分段时间边界线
                    ax.axvline(start_time, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                    ax.axvline(end_time, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

                    # 添加分段区域高亮
                    ax.axvspan(start_time, end_time, alpha=0.2, color='white', label='选中分段')

                # 添加带宽标记
                bw = metadata.get('bw', fs)
                bw_mhz = bw / 1e6
                fc_mhz = fc / 1e6

                ax.axhline(fc_mhz - bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1, label='带宽边界')
                ax.axhline(fc_mhz + bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1)
                ax.axhline(fc_mhz, color='yellow', linestyle='-', alpha=0.8, linewidth=1, label='中心频率')

                # 设置标题和标签（与首页一致）
                ax.set_title('时频图', fontsize=10, pad=10)
                ax.set_xlabel('时间 (ms)')
                ax.set_ylabel('频率 (MHz)')

                # 不添加颜色条（按用户要求）
            else:
                ax.text(0.5, 0.5, '时频图计算失败',
                       transform=ax.transAxes, ha='center', va='center')

        except Exception as e:
            ax.text(0.5, 0.5, f'时频图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def compute_fft_sync(self, signal_data, fs, fc):
        """同步计算FFT（基于首页的异步方法）"""
        try:
            # 直接调用首页的FFT计算逻辑
            future = self.compute_fft_async(signal_data, fs, fc)
            freqs_mhz, fftdata = future.result()  # 同步获取结果，注意顺序
            return freqs_mhz, fftdata
        except Exception as e:
            print(f"FFT计算失败: {e}")
            # 简单的FFT计算作为备用
            import numpy as np
            fft_data = np.fft.fft(signal_data)
            fft_data_shifted = np.fft.fftshift(fft_data)
            freqs = np.fft.fftfreq(len(signal_data), 1/fs)
            freqs_shifted = np.fft.fftshift(freqs)
            freqs_mhz = (freqs_shifted + fc) / 1e6
            fftdata = np.abs(fft_data_shifted) / len(signal_data)
            return freqs_mhz, fftdata

    def compute_spectrogram_sync(self, signal_data, fs, fc):
        """同步计算时频图（基于首页的异步方法）"""
        try:
            # 直接调用首页的时频图计算逻辑
            future = self.compute_spectrogram_async(signal_data, fs, fc)
            return future.result()  # 同步获取结果
        except Exception as e:
            print(f"时频图计算失败: {e}")
            return None, None, None, False

    def plot_time_domain_points(self, ax, signal_data, fs, metadata, segment_info):
        """绘制时域信号点值图，显示完整数据并标出分段"""
        try:
            import numpy as np

            # 创建时间轴（以点数为单位，转换为1e5单位）
            indices = np.arange(len(signal_data))
            indices_1e5 = indices / 1e5

            # 提取I和Q分量
            signal_i = np.real(signal_data)
            signal_q = np.imag(signal_data)

            # 绘制完整的I和Q信号
            ax.plot(indices_1e5, signal_i, 'b-', linewidth=0.5, label='I分量', alpha=0.7)
            ax.plot(indices_1e5, signal_q, 'r-', linewidth=0.5, label='Q分量', alpha=0.7)

            # 标出选中的分段
            if segment_info and segment_info.get('has_segment', False):
                start_idx = segment_info.get('start_sample', 0)
                end_idx = segment_info.get('end_sample', len(signal_data))

                # 转换为1e5单位
                start_1e5 = start_idx / 1e5
                end_1e5 = end_idx / 1e5

                # 高亮显示选中分段
                segment_i = signal_i[start_idx:end_idx]
                segment_q = signal_q[start_idx:end_idx]
                segment_indices = indices[start_idx:end_idx] / 1e5

                ax.plot(segment_indices, segment_i, 'b-', linewidth=2, label='选中分段 I', alpha=1.0)
                ax.plot(segment_indices, segment_q, 'r-', linewidth=2, label='选中分段 Q', alpha=1.0)

                # 添加分段边界线
                ax.axvline(start_1e5, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                ax.axvline(end_1e5, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

            ax.set_title('时域信号点值图')
            ax.set_xlabel('时间点数 (×10⁵)')
            ax.set_ylabel('信号幅度')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 设置y轴范围
            y_min = min(np.min(signal_i), np.min(signal_q))
            y_max = max(np.max(signal_i), np.max(signal_q))
            y_range = y_max - y_min
            ax.set_ylim(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

            # 添加详细信息标注
            signal_length = len(signal_data)
            duration_ms = signal_length / fs * 1000
            class_name = metadata.get('class_name', 'Unknown')

            info_text = f'类别: {class_name}\n'
            info_text += f'信号长度: {signal_length:,} 点\n'
            info_text += f'持续时间: {duration_ms:.2f} ms\n'
            info_text += f'采样率: {fs/1e6:.2f} MHz'

            if metadata.get('is_downsampled', False):
                info_text += f'\n抽样显示: {metadata.get("downsample_factor", 1)}:1'

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, va='top', ha='left',
                   bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                   fontsize=9)

        except Exception as e:
            ax.text(0.5, 0.5, f'时域图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_frequency_domain(self, ax, signal_data, fs, fc, metadata, segment_info):
        """绘制频谱图，显示完整数据并标出分段"""
        try:
            import numpy as np

            # 计算完整信号的FFT
            fft_data = np.fft.fft(signal_data)
            fft_data_shifted = np.fft.fftshift(fft_data)

            # 创建频率轴
            freqs = np.fft.fftfreq(len(signal_data), 1/fs)
            freqs_shifted = np.fft.fftshift(freqs)
            freqs_mhz = (freqs_shifted + fc) / 1e6  # 转换为MHz

            # 计算功率谱密度
            psd = 20 * np.log10(np.abs(fft_data_shifted) + 1e-10)

            # 绘制完整频谱
            ax.plot(freqs_mhz, psd, 'b-', linewidth=0.8, alpha=0.7, label='完整频谱')

            # 如果有分段信息，计算并显示分段的频谱
            if segment_info and segment_info.get('has_segment', False):
                start_idx = segment_info.get('start_sample', 0)
                end_idx = segment_info.get('end_sample', len(signal_data))

                # 提取分段信号
                segment_signal = signal_data[start_idx:end_idx]

                # 计算分段FFT
                segment_fft = np.fft.fft(segment_signal)
                segment_fft_shifted = np.fft.fftshift(segment_fft)

                # 创建分段频率轴
                segment_freqs = np.fft.fftfreq(len(segment_signal), 1/fs)
                segment_freqs_shifted = np.fft.fftshift(segment_freqs)
                segment_freqs_mhz = (segment_freqs_shifted + fc) / 1e6

                # 计算分段功率谱密度
                segment_psd = 20 * np.log10(np.abs(segment_fft_shifted) + 1e-10)

                # 绘制分段频谱（高亮显示）
                ax.plot(segment_freqs_mhz, segment_psd, 'r-', linewidth=2, alpha=1.0, label='选中分段频谱')

            ax.set_title('频谱图')
            ax.set_xlabel('频率 (MHz)')
            ax.set_ylabel('功率 (dB)')
            ax.grid(True, alpha=0.3)

            # 标注带宽信息
            bw = metadata.get('bw', fs)
            bw_mhz = bw / 1e6
            fc_mhz = fc / 1e6

            # 添加带宽标注
            ax.axvline(fc_mhz - bw_mhz/2, color='r', linestyle='--', alpha=0.7, label=f'带宽: {bw_mhz:.2f} MHz')
            ax.axvline(fc_mhz + bw_mhz/2, color='r', linestyle='--', alpha=0.7)
            ax.axvline(fc_mhz, color='g', linestyle='-', alpha=0.7, label=f'中心频率: {fc_mhz:.2f} MHz')

            # 添加频域分析信息
            max_power_idx = np.argmax(psd)
            max_power_freq = freqs_mhz[max_power_idx]
            max_power_db = psd[max_power_idx]

            # 计算信号统计信息
            mean_power = np.mean(psd)
            std_power = np.std(psd)

            freq_info_text = f'峰值功率: {max_power_db:.1f} dB\n'
            freq_info_text += f'峰值频率: {max_power_freq:.2f} MHz\n'
            freq_info_text += f'平均功率: {mean_power:.1f} dB\n'
            freq_info_text += f'功率标准差: {std_power:.1f} dB'

            ax.text(0.98, 0.98, freq_info_text, transform=ax.transAxes, va='top', ha='right',
                   bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                   fontsize=9)

            # 标记峰值点
            ax.plot(max_power_freq, max_power_db, 'ro', markersize=6, label=f'峰值: {max_power_db:.1f} dB')

            ax.legend()

        except Exception as e:
            ax.text(0.5, 0.5, f'频谱图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_time_frequency(self, ax, signal_data, fs, fc, metadata, segment_info):
        """绘制时频图，显示完整数据并标出分段"""
        try:
            import numpy as np
            from scipy.signal import spectrogram

            # 计算时频图参数
            signal_length = len(signal_data)
            window = min(4096, signal_length // 4)
            window = max(256, window)  # 确保窗口不会太小
            noverlap = window // 2
            nfft = window

            # 计算完整信号的时频图
            f_spec, t_spec, Pxx = spectrogram(signal_data, fs=fs, window='hann',
                                             nperseg=window, noverlap=noverlap,
                                             nfft=nfft, return_onesided=False)

            # 重新计算频率轴（MATLAB兼容）
            F = (np.arange(-window//2, window//2) * fs / window + fc) / 1e6  # MHz
            T = t_spec * 1000  # ms

            # 功率计算（MATLAB兼容）
            Pxx_shifted = np.roll(Pxx, window//2, axis=0)  # 频域移位
            Pxx_db = 10 * np.log10(Pxx_shifted + 1e-10)    # dB转换

            # 绘制时频图
            im = ax.pcolormesh(T, F, Pxx_db, shading='auto', cmap='viridis')

            ax.set_title('时频图')
            ax.set_xlabel('时间 (ms)')
            ax.set_ylabel('频率 (MHz)')

            # 添加颜色条
            try:
                import matplotlib.pyplot as plt
                plt.colorbar(im, ax=ax, label='功率 (dB)')
            except:
                pass  # 如果添加颜色条失败，继续执行

            # 标注分段信息
            bw = metadata.get('bw', fs)
            bw_mhz = bw / 1e6
            fc_mhz = fc / 1e6

            # 添加带宽范围标注
            ax.axhline(fc_mhz - bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1, label='带宽边界')
            ax.axhline(fc_mhz + bw_mhz/2, color='red', linestyle='--', alpha=0.7, linewidth=1)
            ax.axhline(fc_mhz, color='yellow', linestyle='-', alpha=0.8, linewidth=1, label='中心频率')

            # 添加分段时间范围标注
            if segment_info and segment_info.get('has_segment', False):
                start_time = segment_info.get('start_time', 0) * 1000  # 转换为ms
                end_time = segment_info.get('end_time', 0) * 1000    # 转换为ms

                # 添加分段时间边界线
                ax.axvline(start_time, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                ax.axvline(end_time, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

                # 添加分段区域高亮
                ax.axvspan(start_time, end_time, alpha=0.2, color='white', label='选中分段')

            # 添加时频分析信息
            duration_ms = T[-1] if len(T) > 0 else 0
            freq_range = F[-1] - F[0] if len(F) > 0 else 0

            # 计算能量分布统计
            max_power_idx = np.unravel_index(np.argmax(Pxx_db), Pxx_db.shape)
            max_power_time = T[max_power_idx[1]] if max_power_idx[1] < len(T) else 0
            max_power_freq = F[max_power_idx[0]] if max_power_idx[0] < len(F) else 0
            max_power_value = Pxx_db[max_power_idx]

            timefreq_info_text = f'时长: {duration_ms:.1f} ms\n'
            timefreq_info_text += f'频率范围: {freq_range:.1f} MHz\n'
            timefreq_info_text += f'峰值能量: {max_power_value:.1f} dB\n'
            timefreq_info_text += f'峰值位置: {max_power_time:.1f} ms, {max_power_freq:.1f} MHz'

            ax.text(0.02, 0.02, timefreq_info_text, transform=ax.transAxes, va='bottom', ha='left',
                   bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.9),
                   fontsize=9)

            # 标记峰值点
            ax.plot(max_power_time, max_power_freq, 'w*', markersize=10, markeredgecolor='black',
                   markeredgewidth=1, label='能量峰值')

            ax.legend(loc='upper right')

        except Exception as e:
            ax.text(0.5, 0.5, f'时频图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_power_spectral_density(self, ax, signal_data, fs, fc, metadata, segment_info):
        """绘制功率谱密度图，显示完整数据并标出分段"""
        try:
            import numpy as np

            # 计算完整信号的功率谱密度
            # 使用Welch方法计算PSD
            from scipy.signal import welch

            nperseg = min(4096, len(signal_data) // 4)
            nperseg = max(256, nperseg)

            freqs, psd = welch(signal_data, fs=fs, nperseg=nperseg,
                              return_onesided=False, scaling='density')

            # 移位和转换
            freqs_shifted = np.fft.fftshift(freqs)
            psd_shifted = np.fft.fftshift(psd)

            # 转换为MHz和dB
            freqs_mhz = (freqs_shifted + fc) / 1e6
            psd_db = 10 * np.log10(psd_shifted + 1e-10)

            # 绘制完整信号的PSD
            ax.plot(freqs_mhz, psd_db, 'b-', linewidth=1, alpha=0.7, label='完整信号PSD')

            # 如果有分段信息，计算并显示分段的PSD
            if segment_info and segment_info.get('has_segment', False):
                start_idx = segment_info.get('start_sample', 0)
                end_idx = segment_info.get('end_sample', len(signal_data))

                # 提取分段信号
                segment_signal = signal_data[start_idx:end_idx]

                if len(segment_signal) > 256:  # 确保分段足够长
                    # 计算分段PSD
                    segment_nperseg = min(nperseg, len(segment_signal) // 4)
                    segment_nperseg = max(256, segment_nperseg)

                    segment_freqs, segment_psd = welch(segment_signal, fs=fs, nperseg=segment_nperseg,
                                                      return_onesided=False, scaling='density')

                    # 移位和转换
                    segment_freqs_shifted = np.fft.fftshift(segment_freqs)
                    segment_psd_shifted = np.fft.fftshift(segment_psd)

                    # 转换为MHz和dB
                    segment_freqs_mhz = (segment_freqs_shifted + fc) / 1e6
                    segment_psd_db = 10 * np.log10(segment_psd_shifted + 1e-10)

                    # 绘制分段PSD（高亮显示）
                    ax.plot(segment_freqs_mhz, segment_psd_db, 'r-', linewidth=2, alpha=1.0, label='选中分段PSD')

            ax.set_title('功率谱密度')
            ax.set_xlabel('频率 (MHz)')
            ax.set_ylabel('功率谱密度 (dB/Hz)')
            ax.grid(True, alpha=0.3)

            # 标注信息
            bw = metadata.get('bw', fs)
            bw_mhz = bw / 1e6
            fc_mhz = fc / 1e6
            class_name = metadata.get('class_name', 'Unknown')

            # 添加带宽和中心频率标注
            ax.axvline(fc_mhz - bw_mhz/2, color='r', linestyle='--', alpha=0.7, label='带宽边界')
            ax.axvline(fc_mhz + bw_mhz/2, color='r', linestyle='--', alpha=0.7)
            ax.axvline(fc_mhz, color='g', linestyle='-', alpha=0.7, label='中心频率')

            # 计算PSD统计信息
            max_psd_idx = np.argmax(psd_db)
            max_psd_freq = freqs_mhz[max_psd_idx]
            max_psd_value = psd_db[max_psd_idx]

            # 计算带宽内的平均功率
            bw_mask = (freqs_mhz >= fc_mhz - bw_mhz/2) & (freqs_mhz <= fc_mhz + bw_mhz/2)
            if np.any(bw_mask):
                avg_power_in_bw = np.mean(psd_db[bw_mask])
                max_power_in_bw = np.max(psd_db[bw_mask])
            else:
                avg_power_in_bw = np.mean(psd_db)
                max_power_in_bw = max_psd_value

            # 添加详细的PSD分析信息
            psd_info_text = f'类别: {class_name}\n'
            psd_info_text += f'带宽: {bw_mhz:.2f} MHz\n'
            psd_info_text += f'中心频率: {fc_mhz:.2f} MHz\n'
            psd_info_text += f'峰值PSD: {max_psd_value:.1f} dB/Hz\n'
            psd_info_text += f'峰值频率: {max_psd_freq:.2f} MHz\n'
            psd_info_text += f'带宽内平均: {avg_power_in_bw:.1f} dB/Hz\n'
            psd_info_text += f'带宽内峰值: {max_power_in_bw:.1f} dB/Hz'

            ax.text(0.02, 0.98, psd_info_text,
                   transform=ax.transAxes, va='top', ha='left',
                   bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.9),
                   fontsize=9)

            # 标记峰值点
            ax.plot(max_psd_freq, max_psd_value, 'ro', markersize=6, label=f'PSD峰值')

            # 添加带宽内功率区域高亮
            if np.any(bw_mask):
                ax.fill_between(freqs_mhz, psd_db, where=bw_mask, alpha=0.2, color='green',
                               label='信号带宽')

            ax.legend()

        except Exception as e:
            ax.text(0.5, 0.5, f'功率谱密度图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')



    def delete_database_records_with_confirmation(self, selected_indices, parent_window, tree, db_records, db_selection_state):
        """
        带确认对话框的数据库记录删除流程

        Args:
            selected_indices (list): 选中的记录索引列表
            parent_window: 父窗口
            tree: TreeView组件
            db_records: 数据库记录列表
            db_selection_state: 选择状态字典
        """
        try:
            # 1. 显示删除确认对话框
            selected_count = len(selected_indices)
            confirm_msg = f"确定要删除选中的 {selected_count} 条记录吗？\n\n"

            # 显示要删除的记录信息
            if selected_count <= 5:
                for idx in selected_indices:
                    if idx < len(db_records):
                        record = db_records[idx]
                        confirm_msg += f"• 数据编号: {record[0]}, 类别ID: {record[1]}, 类别名: {record[2]}\n"
            else:
                for idx in selected_indices[:3]:
                    if idx < len(db_records):
                        record = db_records[idx]
                        confirm_msg += f"• 数据编号: {record[0]}, 类别ID: {record[1]}, 类别名: {record[2]}\n"
                confirm_msg += f"... 以及其他 {selected_count - 3} 条记录\n"

            confirm_msg += "\n此操作不可撤销！"

            if not ScaledMessageBox.askyesno("确认删除", confirm_msg, parent=parent_window):
                return

            # 2. 执行数据库删除
            def delete_thread():
                try:
                    # 导入删除函数
                    import sys
                    import os
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    parent_dir = os.path.dirname(current_dir)
                    if parent_dir not in sys.path:
                        sys.path.append(parent_dir)

                    from usrlib.usrlib import DeleteArcVectorRecords
                    from usrlib.MyHelper import check_file_dependencies, delete_source_file_safely

                    # 先收集要检查的文件路径（在删除数据库记录之前）
                    from usrlib.MyHelper import clean_database_path_string
                    file_paths_to_check = []
                    for idx in selected_indices:
                        if idx < len(db_records):
                            file_path = db_records[idx][3]  # 文件路径在第4列（索引3）
                            if file_path:
                                # 清理路径字符串，移除b'...'格式
                                clean_path = clean_database_path_string(file_path)
                                if clean_path and clean_path not in file_paths_to_check:
                                    file_paths_to_check.append(clean_path)

                    # 执行数据库删除
                    success, deleted_count, error_msg = DeleteArcVectorRecords(selected_indices)

                    if success:
                        # 4. 询问是否删除源文件
                        if file_paths_to_check:
                            self.root.after(0, lambda: self.ask_delete_source_files(
                                file_paths_to_check, parent_window, tree, db_records,
                                db_selection_state, selected_indices, deleted_count
                            ))
                        else:
                            # 没有文件需要处理，直接完成
                            self.root.after(0, lambda: self.complete_deletion_process(
                                parent_window, tree, db_records, db_selection_state,
                                selected_indices, deleted_count, []
                            ))
                    else:
                        # 删除失败
                        self.root.after(0, lambda: ScaledMessageBox.showerror(
                            "删除失败", f"数据库记录删除失败:\n{error_msg}", parent=parent_window
                        ))

                except Exception as e:
                    error_message = f"删除过程中发生错误: {str(e)}"
                    self.root.after(0, lambda: ScaledMessageBox.showerror(
                        "错误", error_message, parent=parent_window
                    ))

            # 在后台线程中执行删除
            import threading
            threading.Thread(target=delete_thread, daemon=True).start()

        except Exception as e:
            ScaledMessageBox.showerror("错误", f"删除流程启动失败: {str(e)}", parent=parent_window)

    def ask_delete_source_files(self, file_paths, parent_window, tree, db_records, db_selection_state, selected_indices, deleted_count):
        """
        询问是否删除源文件
        """
        try:
            file_count = len(file_paths)
            ask_msg = f"数据库记录已成功删除 {deleted_count} 条。\n\n"
            ask_msg += f"是否同时删除对应的 {file_count} 个源文件？\n\n"

            # 显示文件列表
            if file_count <= 5:
                for file_path in file_paths:
                    ask_msg += f"• {file_path}\n"
            else:
                for file_path in file_paths[:3]:
                    ask_msg += f"• {file_path}\n"
                ask_msg += f"... 以及其他 {file_count - 3} 个文件\n"

            ask_msg += "\n注意：如果文件被其他记录使用，将不会被删除。"

            if ScaledMessageBox.askyesno("删除源文件", ask_msg, parent=parent_window):
                # 用户选择删除源文件
                self.delete_source_files_with_dependency_check(
                    file_paths, parent_window, tree, db_records,
                    db_selection_state, selected_indices, deleted_count
                )
            else:
                # 用户选择不删除源文件
                self.complete_deletion_process(
                    parent_window, tree, db_records, db_selection_state,
                    selected_indices, deleted_count, []
                )

        except Exception as e:
            ScaledMessageBox.showerror("错误", f"文件删除询问失败: {str(e)}", parent=parent_window)

    def delete_source_files_with_dependency_check(self, file_paths, parent_window, tree, db_records, db_selection_state, selected_indices, deleted_count):
        """
        检查文件依赖并删除源文件（避免重复检查同一文件）
        """
        try:
            def file_delete_thread():
                try:
                    from usrlib.MyHelper import check_file_dependencies, delete_source_file_safely

                    deleted_files = []
                    skipped_files = []
                    error_files = []
                    processed_files = set()  # 记录已处理的文件，避免重复处理

                    for file_path in file_paths:
                        # 跳过已处理的文件
                        if file_path in processed_files:
                            continue

                        processed_files.add(file_path)

                        # 检查文件是否存在
                        if not os.path.exists(file_path):
                            error_files.append(f"{file_path}: 文件不存在")
                            continue

                        # 检查文件依赖关系
                        dependency_count, dependent_records, dep_error = check_file_dependencies(file_path)

                        if dep_error:
                            # 如果检查依赖失败，可能是数据库为空，直接尝试删除文件
                            if "无法读取数据库内容" in dep_error:
                                # 数据库为空，可以安全删除文件
                                success, deleted_items, error_msg = delete_source_file_safely(file_path, force_delete=True)
                                if success:
                                    deleted_files.extend(deleted_items)
                                else:
                                    error_files.append(f"{file_path}: {error_msg}")
                            else:
                                # 其他依赖检查错误
                                error_files.append(f"{file_path}: 依赖检查失败 - {dep_error}")
                            continue

                        if dependency_count > 0:
                            # 文件仍被其他记录依赖，跳过删除
                            skipped_files.append(f"{file_path} (被 {dependency_count} 条记录依赖)")
                        else:
                            # 文件无依赖，可以安全删除
                            success, deleted_items, error_msg = delete_source_file_safely(file_path, force_delete=False)

                            if success:
                                deleted_files.extend(deleted_items)
                            else:
                                error_files.append(f"{file_path}: {error_msg}")

                    # 在主线程中显示结果
                    self.root.after(0, lambda: self.complete_deletion_process(
                        parent_window, tree, db_records, db_selection_state,
                        selected_indices, deleted_count, deleted_files, skipped_files, error_files
                    ))

                except Exception as e:
                    error_message = f"文件删除过程中发生错误: {str(e)}"
                    self.root.after(0, lambda: ScaledMessageBox.showerror(
                        "错误", error_message, parent=parent_window
                    ))

            # 在后台线程中执行文件删除
            import threading
            threading.Thread(target=file_delete_thread, daemon=True).start()

        except Exception as e:
            ScaledMessageBox.showerror("错误", f"文件删除流程启动失败: {str(e)}", parent=parent_window)

    def complete_deletion_process(self, parent_window, tree, db_records, db_selection_state, selected_indices, deleted_count, deleted_files=None, skipped_files=None, error_files=None):
        """
        完成删除流程，显示结果并刷新界面
        """
        try:
            # 构建结果消息
            result_msg = f"删除操作完成！\n\n"
            result_msg += f"✓ 已删除数据库记录: {deleted_count} 条\n"

            if deleted_files:
                result_msg += f"✓ 已删除文件/文件夹: {len(deleted_files)} 个\n"
                for item in deleted_files[:5]:  # 最多显示5个
                    result_msg += f"  • {item}\n"
                if len(deleted_files) > 5:
                    result_msg += f"  ... 以及其他 {len(deleted_files) - 5} 个项目\n"

            if skipped_files:
                result_msg += f"\n⚠ 跳过删除的文件: {len(skipped_files)} 个\n"
                for item in skipped_files[:3]:  # 最多显示3个
                    result_msg += f"  • {item}\n"
                if len(skipped_files) > 3:
                    result_msg += f"  ... 以及其他 {len(skipped_files) - 3} 个文件\n"

            if error_files:
                result_msg += f"\n❌ 删除失败的文件: {len(error_files)} 个\n"
                for item in error_files[:3]:  # 最多显示3个
                    result_msg += f"  • {item}\n"
                if len(error_files) > 3:
                    result_msg += f"  ... 以及其他 {len(error_files) - 3} 个文件\n"

            # 显示结果对话框
            ScaledMessageBox.showinfo("删除完成", result_msg, parent=parent_window)

            # 刷新TreeView - 移除已删除的行
            for idx in sorted(selected_indices, reverse=True):
                if idx < len(tree.get_children()):
                    item = tree.get_children()[idx]
                    tree.delete(item)

            # 重新编号剩余的行并重置选择状态
            db_selection_state.clear()
            for i, item in enumerate(tree.get_children()):
                db_selection_state[i] = False
                # 更新选择状态显示
                current_values = list(tree.item(item, 'values'))
                current_values[0] = '☐'
                tree.item(item, values=current_values)

            print(f"删除操作完成: 数据库记录 {deleted_count} 条")

        except Exception as e:
            ScaledMessageBox.showerror("错误", f"完成删除流程时发生错误: {str(e)}", parent=parent_window)

    # ========== data_viewer 中的核心绘图函数 ==========
    
    def create_smart_sampling_indices(self, N_sig, max_points):
        """
        创建智能抽样索引，确保包含完整数据范围（来自data_viewer）
        
        参数:
            N_sig (int): 原始信号长度
            max_points (int): 最大显示点数
            
        返回:
            tuple: (indices, step) 抽样索引数组和步长
        """
        if N_sig <= max_points:
            return np.arange(N_sig), 1
        
        # 计算基本抽样步长
        step = N_sig // max_points
        
        # 确保包含首尾数据点的智能抽样
        # 强制包含第一个和最后一个数据点
        indices = np.arange(0, N_sig, step)
        
        # 如果最后一个索引不是N_sig-1，则添加最后一个点
        if indices[-1] != N_sig - 1:
            indices = np.append(indices, N_sig - 1)
        
        return indices, step

    def compute_fft_async(self, sig, fs, fc):
        """
        异步计算 FFT 频谱，采用 MATLAB 兼容的分段平均方法（来自data_viewer）
        
        该方法实现了自适应的 FFT 计算策略，根据信号长度选择最优的处理方式：
        
        计算策略：
        - ≤100万点：直接 FFT 计算，保持完整精度
        - 100万-400万点：智能抽样 FFT，保持频谱完整性
        - >400万点：分段平均 FFT（类似 MATLAB 的 spectrogram）
        
        Args:
            sig (np.ndarray): 输入信号（复数）
            fs (float): 采样率 (Hz)
            fc (float): 中心频率 (Hz)
            
        Returns:
            Future: 包含 (freqs_mhz, fftdata) 的 Future 对象
        """
        def _compute():
            def compute_direct_fft(sig, fs, fc):
                """直接FFT计算（小数据量）"""
                sig_len = len(sig)

                # 尝试GPU加速
                if self.gpu_available and self.gpu_backend == 'cupy':
                    try:
                        sig_gpu = self.cp.asarray(sig)
                        fftdata = self.cp.fft.fftshift(self.cp.abs(self.cp.fft.fft(sig_gpu)) / sig_len)
                        fftdata = self.cp.asnumpy(fftdata)  # 转回CPU
                        print("  使用GPU加速FFT (CuPy)")
                    except Exception as e:
                        print(f"  GPU FFT失败，回退到CPU: {e}")
                        fftdata = np.fft.fftshift(np.abs(np.fft.fft(sig)) / sig_len)
                elif self.gpu_available and self.gpu_backend == 'torch':
                    try:
                        import torch
                        sig_tensor = torch.from_numpy(sig).cuda()
                        fft_result = torch.fft.fft(sig_tensor)
                        fftdata = torch.fft.fftshift(torch.abs(fft_result) / sig_len)
                        fftdata = fftdata.cpu().numpy()
                        print("  使用GPU加速FFT (PyTorch)")
                    except Exception as e:
                        print(f"  GPU FFT失败，回退到CPU: {e}")
                        fftdata = np.fft.fftshift(np.abs(np.fft.fft(sig)) / sig_len)
                else:
                    fftdata = np.fft.fftshift(np.abs(np.fft.fft(sig)) / sig_len)

                # 生成频率轴
                freq_indices = np.arange(sig_len, dtype=np.float64)
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                freqs_mhz = nb_freqs / 1e6

                return fftdata, freqs_mhz
            
            def compute_smart_sampling_fft(sig, fs, fc):
                """智能抽样FFT（中等数据量）"""
                original_len = len(sig)
                target_len = 1048576  # 目标100万点
                step = max(1, original_len // target_len)
                
                # 均匀抽样，确保覆盖整个信号
                sampled_sig = sig[::step]
                print(f"  智能抽样: {len(sampled_sig):,}/{original_len:,} 点，步长={step}")
                
                # 对抽样信号进行FFT
                sig_len = len(sampled_sig)
                fftdata = np.fft.fftshift(np.abs(np.fft.fft(sampled_sig)) / sig_len)
                
                # 频率轴计算（基于原始采样率）
                freq_indices = np.arange(sig_len, dtype=np.float64)
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                freqs_mhz = nb_freqs / 1e6
                
                return fftdata, freqs_mhz
            
            def compute_welch_like_fft(sig, fs, fc):
                """分段平均FFT（MATLAB风格，大数据量）"""
                # MATLAB风格的分段参数
                fft_len = 65536  # 64K FFT长度，平衡分辨率和计算量
                overlap = fft_len // 2  # 50%重叠
                window = np.blackman(fft_len)  # 与MATLAB一致的窗函数
                
                # 计算分段数量
                step = fft_len - overlap
                num_segments = (len(sig) - overlap) // step
                print(f"  分段参数: FFT长度={fft_len:,}，重叠={overlap:,}，分段数={num_segments}")
                
                if num_segments <= 0:
                    # 回退到直接FFT
                    return compute_direct_fft(sig, fs, fc)
                
                # 分段FFT并平均
                accumulated_fft = np.zeros(fft_len, dtype=np.complex128)
                valid_segments = 0
                
                for i in range(num_segments):
                    start_idx = i * step
                    end_idx = start_idx + fft_len
                    
                    if end_idx <= len(sig):
                        # 提取分段并加窗
                        segment = sig[start_idx:end_idx] * window
                        # FFT计算
                        segment_fft = np.fft.fft(segment)
                        accumulated_fft += segment_fft
                        valid_segments += 1
                
                # 平均并归一化
                if valid_segments > 0:
                    averaged_fft = accumulated_fft / valid_segments
                    fftdata = np.fft.fftshift(np.abs(averaged_fft) / fft_len)
                else:
                    # 回退处理
                    return compute_smart_sampling_fft(sig, fs, fc)
                
                # 频率轴计算
                freq_indices = np.arange(fft_len, dtype=np.float64) 
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / fft_len)
                freqs_mhz = nb_freqs / 1e6
                
                print(f"  有效分段: {valid_segments}/{num_segments}")
                return fftdata, freqs_mhz
            
            original_len = len(sig)
            
            # 方案选择：根据信号长度选择最优处理方式
            if original_len <= 1048576:  # <=100万点：直接FFT
                print(f"FFT策略: 直接计算 ({original_len} 点)")
                fftdata, freqs_mhz = compute_direct_fft(sig, fs, fc)
                coverage_info = "完整数据"
            
            elif original_len <= 4194304:  # 100万-400万点：智能抽样FFT  
                print(f"FFT策略: 智能抽样 ({original_len} 点)")
                fftdata, freqs_mhz = compute_smart_sampling_fft(sig, fs, fc)
                coverage_info = "智能抽样，保持完整频谱"
            
            else:  # >400万点：分段平均FFT（MATLAB方式）
                print(f"FFT策略: 分段平均FFT ({original_len} 点)")
                fftdata, freqs_mhz = compute_welch_like_fft(sig, fs, fc)
                coverage_info = "分段平均，完整覆盖"
            
            # 统一的调试输出
            print(f"频谱计算完成:")
            print(f"  处理策略: {coverage_info}")
            print(f"  原始长度: {original_len:,} 点") 
            print(f"  采样率: {fs/1e6:.3f} MHz")
            print(f"  中心频率: {fc/1e6:.3f} MHz")
            print(f"  频率范围: {freqs_mhz[0]:.3f} ~ {freqs_mhz[-1]:.3f} MHz")
            print(f"  频率分辨率: {(freqs_mhz[1] - freqs_mhz[0]):.6f} MHz")
            print(f"  有效频谱点: {len(fftdata):,}")
            
            return freqs_mhz, fftdata
        
        return self.thread_pool.submit(_compute)

    def compute_spectrogram_async(self, sig, fs, fc):
        """
        异步计算时频图，严格按照 MATLAB 的计算方式（来自data_viewer）
        
        Args:
            sig (np.ndarray): 输入信号（复数）
            fs (float): 采样率 (Hz)
            fc (float): 中心频率 (Hz)
            
        Returns:
            Future: 包含 (F_mhz, T_ms, Pxx_db, success) 的 Future 对象
        """
        def _compute():
            # 时频图数据优化：保持质量的前提下适度优化
            stft_sig = sig
            if len(sig) > 8000000:  # 超过800万点，适度抽样
                step = len(sig) // 2000000  # 目标约200万点，保持更多细节
                stft_sig = sig[::step]
                print(f"时频图优化: 适度抽样至 {len(stft_sig)}/{len(sig)} 点，步长={step}")
            elif len(sig) > 4000000:  # 超过400万点时进行轻度优化
                step = len(sig) // 1500000  # 目标约150万点
                stft_sig = sig[::step]
                print(f"时频图优化: 轻度抽样至 {len(stft_sig)}/{len(sig)} 点，步长={step}")
            
            # 时频图计算参数 - 保持质量优先
            # 根据信号长度动态调整参数，但保持50%重叠以确保质量
            sig_len = len(stft_sig)
            if sig_len > 1000000:  # 超过100万点
                window = 2048
                noverlap = window // 2  # 保持50%重叠确保质量
            elif sig_len > 500000:  # 超过50万点
                window = 1024
                noverlap = window // 2
            elif sig_len > 100000:  # 超过10万点
                window = 512
                noverlap = window // 2
            else:
                window = 256
                noverlap = window // 2

            nfft = window

            # 数据长度检查，如果数据太少则进一步调整窗口大小
            if len(stft_sig) < window * 2:
                if len(stft_sig) > 1024:
                    window = 512
                elif len(stft_sig) > 512:
                    window = 256
                else:
                    window = 128
                noverlap = window // 4
                nfft = window
                print(f"时频图: 调整窗口大小为 {window}")

            print(f"时频图参数: 窗口={window}, 重叠={noverlap}, 信号长度={sig_len:,}")
            
            try:
                # 使用scipy的spectrogram函数，对应MATLAB的spectrogram
                f_spec, t_spec, Pxx = spectrogram(stft_sig, fs=fs, window='hann', 
                                                 nperseg=window, noverlap=noverlap, 
                                                 nfft=nfft, return_onesided=False)
                
                # MATLAB中重新计算频率轴
                F = (np.arange(-window//2, window//2) * fs / window + fc) / 1e6  # MHz
                
                # 时间轴需要根据原始信号长度进行调整
                if len(stft_sig) != len(sig):
                    # 如果使用了抽样，需要调整时间轴到原始信号的时间范围
                    time_scale = len(sig) / len(stft_sig)
                    T = t_spec * 1000 * time_scale  # 调整时间轴
                else:
                    T = t_spec * 1000  # ms
                
                # MATLAB功率计算：10*log10(circshift(P,window/2))
                Pxx_shifted = np.roll(Pxx, window//2, axis=0)  # 频域移位
                Pxx_db = 10 * np.log10(Pxx_shifted + 1e-10)    # dB转换
                
                return F, T, Pxx_db, True  # 成功标志
                
            except Exception as e:
                print(f"时频图计算失败: {e}")
                return None, None, np.abs(stft_sig), False  # 失败时返回简化数据
        
        return self.thread_pool.submit(_compute)

    def find_adaptive_frequency_ticks(self, freqs_mhz, fftdata):
        """
        快速生成自适应频率刻度（来自data_viewer）
        
        参数:
            freqs_mhz: 频率数组 (MHz)
            fftdata: FFT频谱数据（未使用，保持接口兼容）
        
        返回:
            list: 频率刻度点列表 (MHz)
        """
        try:
            freq_min = freqs_mhz[0]
            freq_max = freqs_mhz[-1] 
            freq_span = freq_max - freq_min
            
            if freq_span <= 0:
                return [freq_min]
            
            # 快速计算合适的刻度间隔和数量
            return self.generate_optimal_ticks(freq_min, freq_max, freq_span)
            
        except Exception as e:
            print(f"频率刻度计算失败: {e}")
            # 简单回退：5个均匀刻度
            return np.linspace(freqs_mhz[0], freqs_mhz[-1], 5).tolist()
    
    def generate_optimal_ticks(self, freq_min, freq_max, freq_span):
        """
        快速生成最优的均匀频率刻度（来自data_viewer）
        
        参数:
            freq_min: 最小频率 (MHz)
            freq_max: 最大频率 (MHz)
            freq_span: 频率跨度 (MHz)
            
        返回:
            list: 优化的刻度点列表
        """
        # 根据频率跨度快速确定刻度间隔
        if freq_span <= 1:
            tick_interval = 0.1
            max_ticks = 8  
        elif freq_span <= 5:
            tick_interval = 0.5
            max_ticks = 8
        elif freq_span <= 20:
            tick_interval = 2
            max_ticks = 10
        elif freq_span <= 50:
            tick_interval = 5
            max_ticks = 10
        elif freq_span <= 100:
            tick_interval = 10
            max_ticks = 10
        elif freq_span <= 200:
            tick_interval = 20
            max_ticks = 10
        else:
            # 大跨度：动态计算间隔
            tick_interval = freq_span / 8  # 固定8个刻度间隔
            max_ticks = 9  # 9个刻度点
        
        # 计算起始点（向上取整到合适的刻度）
        if tick_interval >= 1:
            start_tick = np.ceil(freq_min / tick_interval) * tick_interval
        else:
            start_tick = np.ceil(freq_min / tick_interval) * tick_interval
        
        # 生成刻度点
        ticks = []
        current_tick = start_tick
        tick_count = 0
        
        while current_tick <= freq_max and tick_count < max_ticks:
            ticks.append(round(current_tick, 2 if tick_interval < 1 else 1))
            current_tick += tick_interval
            tick_count += 1
        
        # 确保包含最大值附近的刻度
        if len(ticks) > 0 and abs(ticks[-1] - freq_max) > tick_interval * 0.5:
            if len(ticks) < max_ticks:
                ticks.append(round(freq_max, 2 if tick_interval < 1 else 1))
        
        return ticks
    
    def calculate_adaptive_ylim(self, signal_i, signal_q):
        """
        根据信号数据自适应计算Y轴范围，优先使用整百或半百刻度（来自data_viewer）
        
        参数:
            signal_i: I路信号数据
            signal_q: Q路信号数据
            
        返回:
            tuple: (y_min, y_max) Y轴范围
        """
        try:
            # 计算I、Q路信号的统计信息
            i_min, i_max = np.min(signal_i), np.max(signal_i)
            q_min, q_max = np.min(signal_q), np.max(signal_q)
            
            # 取绝对值的最大值作为参考
            abs_max = max(abs(i_min), abs(i_max), abs(q_min), abs(q_max))
            
            # 如果信号幅度很小，使用默认范围
            if abs_max < 10:
                return (-50, 50)
            
            # 计算合适的范围（留20%余量）
            range_with_margin = abs_max * 1.2
            
            # 优先选择整百刻度
            if range_with_margin <= 50:
                y_limit = 50
            elif range_with_margin <= 100:
                y_limit = 100
            elif range_with_margin <= 150:
                y_limit = 150  # 半百
            elif range_with_margin <= 200:
                y_limit = 200
            elif range_with_margin <= 250:
                y_limit = 250  # 半百
            elif range_with_margin <= 300:
                y_limit = 300
            elif range_with_margin <= 400:
                y_limit = 400
            elif range_with_margin <= 500:
                y_limit = 500
            elif range_with_margin <= 600:
                y_limit = 600
            elif range_with_margin <= 750:
                y_limit = 750  # 半百
            elif range_with_margin <= 1000:
                y_limit = 1000
            elif range_with_margin <= 1500:
                y_limit = 1500  # 半百
            elif range_with_margin <= 2000:
                y_limit = 2000
            else:
                # 对于很大的值，计算最接近的整百或半百
                if range_with_margin <= 5000:
                    # 5000以下，使用250的倍数（半百系列）
                    y_limit = int(np.ceil(range_with_margin / 250) * 250)
                else:
                    # 5000以上，使用500的倍数（整百系列）
                    y_limit = int(np.ceil(range_with_margin / 500) * 500)
            
            return (-y_limit, y_limit)
            
        except Exception as e:
            print(f"计算自适应Y轴范围失败: {e}")
            # 失败时返回默认范围
            return (-500, 500)

    # =================================================================================
    # 信道扫描相关功能
    # =================================================================================
    
    def on_display_error(self, error_msg):
        """显示错误回调"""
        print(f"显示信号失败: {error_msg}")
        self.status_label.config(text="显示失败")
    
    def on_mouse_scroll(self, event):
        """
        处理鼠标滚轮事件，实现交互式图表缩放（来自data_viewer）
        
        该方法为时域信号点值图和频谱图提供了滚轮缩放功能，增强用户交互体验。
        
        缩放特性：
        - 时域信号点值图：支持 1-50 倍横向缩放
        - 频谱图：支持 1-50 倍横向缩放
        - 以鼠标光标位置为缩放中心
        - 自动边界检测，防止超出数据范围
        
        交互操作：
        - 向上滚动：放大（缩放因子 1.2）
        - 向下滚动：缩小（缩放因子 1/1.2）
        - 缩放范围自动限制在原始数据范围内
        - 加载新数据时自动重置缩放级别
        
        Args:
            event: matplotlib 滚轮事件对象，包含鼠标位置和滚动方向
        """
        # 只在2D模式下处理
        if self.is_3d_mode or not hasattr(self, 'axes') or len(self.axes) < 3:
            return
        
        # 检查鼠标位置
        ax1 = self.axes[0]  # 时域信号点值图
        ax3 = self.axes[2]  # 频谱图
        
        if event.inaxes == ax1:
            # 时域信号点值图缩放
            self.handle_time_plot_zoom(event, ax1)
        elif event.inaxes == ax3:
            # 频谱图缩放
            self.handle_spectrum_plot_zoom(event, ax3)

    def handle_time_plot_zoom(self, event, ax):
        """
        处理时域信号点值图的缩放（来自data_viewer）
        """
        
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.time_plot_original_xlim is None:
            self.time_plot_original_xlim = ax.get_xlim()
        
        # 获取鼠标的绝对坐标位置（数据坐标）
        xlim = ax.get_xlim()
        mouse_x = event.xdata if event.xdata is not None else (xlim[0] + xlim[1]) / 2
        
        # 计算缩放因子
        zoom_factor = 1.2 if event.button == 'up' else 1.0 / 1.2
        
        # 计算新的缩放级别
        new_zoom_level = self.time_plot_zoom_level * zoom_factor
        
        # 限制缩放范围
        if new_zoom_level < self.min_zoom_level:
            new_zoom_level = self.min_zoom_level
        elif new_zoom_level > self.max_zoom_level:
            new_zoom_level = self.max_zoom_level
        
        # 如果缩放级别没有变化，则不进行任何操作
        if abs(new_zoom_level - self.time_plot_zoom_level) < 0.01:
            return
        
        self.time_plot_zoom_level = new_zoom_level
        
        # 计算新的X轴范围
        if self.time_plot_original_xlim is not None:
            orig_min, orig_max = self.time_plot_original_xlim
            orig_range = orig_max - orig_min
            
            # 确保鼠标位置在原始范围内
            mouse_x = max(orig_min, min(orig_max, mouse_x))
            
            # 计算鼠标在原始范围内的相对位置
            mouse_rel = (mouse_x - orig_min) / orig_range if orig_range > 0 else 0.5
            
            # 新的显示范围
            new_range = orig_range / self.time_plot_zoom_level
            
            # 以鼠标位置为中心计算新的范围
            new_min = mouse_x - mouse_rel * new_range
            new_max = mouse_x + (1 - mouse_rel) * new_range
            
            # 确保新范围不超出原始范围
            if new_min < orig_min:
                offset = orig_min - new_min
                new_min = orig_min
                new_max = min(orig_max, new_max + offset)
            elif new_max > orig_max:
                offset = new_max - orig_max
                new_max = orig_max
                new_min = max(orig_min, new_min - offset)
            
            # 应用新的X轴范围
            ax.set_xlim(new_min, new_max)
            
            # 重新绘制图表
            self.canvas.draw()
    
    def handle_spectrum_plot_zoom(self, event, ax):
        """
        处理频谱图的缩放（来自data_viewer）
        """
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.spectrum_plot_original_xlim is None:
            self.spectrum_plot_original_xlim = ax.get_xlim()
        
        # 获取鼠标的绝对坐标位置（数据坐标）
        xlim = ax.get_xlim()
        mouse_x = event.xdata if event.xdata is not None else (xlim[0] + xlim[1]) / 2
        
        # 计算缩放因子
        zoom_factor = 1.2 if event.button == 'up' else 1.0 / 1.2
        
        # 计算新的缩放级别
        new_zoom_level = self.spectrum_plot_zoom_level * zoom_factor
        
        # 限制缩放范围
        if new_zoom_level < self.min_zoom_level:
            new_zoom_level = self.min_zoom_level
        elif new_zoom_level > self.max_zoom_level:
            new_zoom_level = self.max_zoom_level
        
        # 如果缩放级别没有变化，则不进行任何操作
        if abs(new_zoom_level - self.spectrum_plot_zoom_level) < 0.01:
            return
        
        self.spectrum_plot_zoom_level = new_zoom_level
        
        # 计算新的X轴范围
        if self.spectrum_plot_original_xlim is not None:
            orig_min, orig_max = self.spectrum_plot_original_xlim
            orig_range = orig_max - orig_min
            
            # 确保鼠标位置在原始范围内
            mouse_x = max(orig_min, min(orig_max, mouse_x))
            
            # 计算鼠标在原始范围内的相对位置
            mouse_rel = (mouse_x - orig_min) / orig_range if orig_range > 0 else 0.5
            
            # 新的显示范围
            new_range = orig_range / self.spectrum_plot_zoom_level
            
            # 以鼠标位置为中心计算新的范围
            new_min = mouse_x - mouse_rel * new_range
            new_max = mouse_x + (1 - mouse_rel) * new_range
            
            # 确保新范围不超出原始范围
            if new_min < orig_min:
                offset = orig_min - new_min
                new_min = orig_min
                new_max = min(orig_max, new_max + offset)
            elif new_max > orig_max:
                offset = new_max - orig_max
                new_max = orig_max
                new_min = max(orig_min, new_min - offset)
            
            # 应用新的X轴范围
            ax.set_xlim(new_min, new_max)
            
            # 重新绘制图表
            self.canvas.draw()

    def add_segment_markers_to_plots(self):
        """
        在时域信号点值图、频谱图和时频图上添加信道扫描分段标记（data_viewer风格）
        
        该方法将检测到的信号分段可视化，并添加详细的文本标签：
        1. 时域信号点值图：标记分段的时间范围，并显示分段信息。
        2. 频谱图：标记分段的中心频率，并提供图例。
        3. 时频图：标记分段的中心频率和时间范围，并显示分段信息。
        """
        # 清除旧的标记
        for ax in self.axes:
            # 使用 try-except 避免在没有 'get_label' 方法的 item 上出错
            for item in list(ax.patches) + list(ax.lines) + list(ax.texts):
                try:
                    if hasattr(item, 'get_label') and 'segment_marker' in str(item.get_label()):
                        item.remove()
                except:
                    continue

        if not self.segment_results or self.is_3d_mode or not hasattr(self, 'axes') or len(self.axes) < 4:
            if hasattr(self, 'canvas'):
                self.canvas.draw()
            return
        
        # 定义颜色和获取图表引用
        colors = plt.get_cmap('tab10').colors
        ax_time_points, ax_time_signal, ax_spectrum, ax_spectrogram = self.axes
        fs = self.current_metadata.get('fs', 1e6)
        
        # 用于智能错开标签位置
        last_text_positions = {'time': {}, 'freq': {}}

        for i, segment in enumerate(self.segment_results):
            color = colors[i % len(colors)]
            start_pos, end_pos = segment['start_pos'], segment['end_pos']
            center_freq_mhz = segment['center_freq'] / 1e6
            bw_khz = segment['bandwidth'] / 1e3
            
            # 使用宽带采样率 (fs) 计算正确的时长，以匹配图表X轴
            signal_length = end_pos - start_pos + 1
            duration_ms = (signal_length / fs * 1000) if fs and fs > 0 else 0
            
            # 使用 "分段X" 格式
            legend_label = f"分段{i+1}"
            marker_label = f'segment_marker_{i}'
            
            # 构建标签文本
            info_text = f"{legend_label}\n{center_freq_mhz:.1f} MHz\n{bw_khz:.1f} kHz\n{duration_ms:.2f} ms"
            
            # 1. 在时域点值图上标记
            start_pos_1e5, end_pos_1e5 = start_pos / 1e5, end_pos / 1e5
            ax_time_points.axvspan(start_pos_1e5, end_pos_1e5, color=color, alpha=0.2, label=marker_label)
            
            # 添加文本标签 (data_viewer 风格)
            text_x = (start_pos_1e5 + end_pos_1e5) / 2
            y_min, y_max = ax_time_points.get_ylim()
            y_range = y_max - y_min
            text_y = y_max - y_range * 0.05 - (i % 4) * (y_range * 0.15) # 错开显示
            
            ax_time_points.text(text_x, text_y, info_text, 
                                ha='center', va='top', fontsize=9,  # 增大字体
                                bbox=dict(boxstyle='round,pad=0.4', fc=color, ec='black', lw=0.5, alpha=0.85),
                                label=marker_label)

            # 2. 在时域信号图上标记
            start_time_ms, end_time_ms = start_pos / fs * 1000, end_pos / fs * 1000
            ax_time_signal.axvspan(start_time_ms, end_time_ms, color=color, alpha=0.2, label=marker_label)

            # 3. 在频谱图上标记中心频率
            ax_spectrum.axvline(x=center_freq_mhz, color=color, linestyle='--', linewidth=1.5, label=legend_label)

            # 4. 在时频图上标记
            ax_spectrogram.axvline(x=start_time_ms, color=color, linestyle=':', linewidth=1.2, label=marker_label)
            ax_spectrogram.axvline(x=end_time_ms, color=color, linestyle=':', linewidth=1.2, label=marker_label)
            ax_spectrogram.axhline(y=center_freq_mhz, color=color, linestyle=':', linewidth=1.2, label=marker_label)
            
            # 在时频图上添加文本标签
            tf_text_x = (start_time_ms + end_time_ms) / 2
            tf_y_min, tf_y_max = ax_spectrogram.get_ylim()
            tf_y_range = tf_y_max - tf_y_min
            tf_text_y = tf_y_max - tf_y_range * 0.05 - (i % 3) * (tf_y_range * 0.2) # 错开显示

            ax_spectrogram.text(tf_text_x, tf_text_y, info_text, 
                                ha='center', va='top', fontsize=9, # 增大字体
                                bbox=dict(boxstyle='round,pad=0.4', fc=color, ec='black', lw=0.5, alpha=0.85),
                                label=marker_label)


        # 为频谱图创建图例
        lines_with_labels = [line for line in ax_spectrum.get_lines() if "分段" in line.get_label()]
        if lines_with_labels:
             ax_spectrum.legend(handles=lines_with_labels, loc='upper right', fontsize='small', framealpha=0.75)

        try:
            self.canvas.draw_idle()
        except Exception as e:
            print(f"绘制分段标记时出错: {e}")
            pass

    def load_model_async(self, model_path):
        """异步加载Arcface模型"""
        if not model_path:
            return
            
        self.status_label.config(text=self.texts['model_loading'])
        
        def load_model_thread():
            # 使用新的路径调用 load_arcface_model
            model = load_arcface_model(model_path=model_path)
            self.root.after(0, self.on_model_loaded, model)
            
        threading.Thread(target=load_model_thread, daemon=True).start()

    def on_model_loaded(self, model):
        """模型加载完成回调"""
        if model:
            self.arcface_model = model
            self.status_label.config(text=self.texts['model_load_success'])
            # 模型加载成功后，更新按钮状态
            self.update_add_button_state()

            # 检查是否有挂起的添加操作
            if getattr(self, 'pending_add_to_db_after_model_load', False):
                self.pending_add_to_db_after_model_load = False
                self.add_segments_to_db_action()
        else:
            self.pending_add_to_db_after_model_load = False # 失败时也要重置
            self.status_label.config(text=self.texts['model_load_failed'])
            ScaledMessageBox.showerror(
                self.texts['error'], 
                self.texts['model_load_failed'], 
                parent=self.root
            )

    def select_and_load_model(self):
        """选择并加载模型文件"""
        model_path = self.enhanced_file_dialog(
            'open',
            title="选择模型文件",
            filetypes=[("PyTorch模型", "*.pth *.pt"), ("所有文件", "*.*")],
            dialog_purpose='model'
        )
        if model_path:
            self.load_model_async(model_path)

    def update_vectors_action(self, parent_window):
        """
        处理更新特征向量的用户操作

        Args:
            parent_window: 父窗口（数据库查看窗口）
        """
        try:
            # 1. 选择新的模型文件
            model_path = self.enhanced_file_dialog(
                'open',
                title="选择用于更新特征向量的模型文件",
                filetypes=[("PyTorch模型", "*.pth *.pt"), ("所有文件", "*.*")],
                dialog_purpose='model'
            )

            if not model_path:
                return  # 用户取消选择

            # 2. 检查class_def.txt文件可用性
            class_def_available = self.check_class_def_availability()

            # 3. 询问是否同时更新类别ID
            if class_def_available:
                update_class_ids_message = (
                    "是否同时更新类别ID？\n\n"
                    "• 选择\"是\"：根据文件路径重新推断类别ID\n"
                    "• 选择\"否\"：保持现有的类别ID不变\n\n"
                    "推荐选择\"是\"以确保类别ID的准确性。"
                )

                update_class_ids = ScaledMessageBox.askyesno(
                    "更新类别ID",
                    update_class_ids_message,
                    parent=parent_window,
                    language=self.current_language
                )
            else:
                # class_def.txt不可用，提示用户并默认不更新class_id
                unavailable_message = (
                    "检测到class_def.txt文件不可用。\n\n"
                    "无法根据文件路径推断类别ID，将保持现有的类别ID不变。\n\n"
                    "如需更新类别ID，请先配置正确的pathsetting.json文件。"
                )

                ScaledMessageBox.showwarning(
                    "类别定义文件不可用",
                    unavailable_message,
                    parent=parent_window,
                    language=self.current_language
                )

                update_class_ids = False

            # 3. 显示确认对话框
            class_id_action = "重新推断类别ID" if update_class_ids else "保持现有类别ID"
            confirm_msg = (
                "确认要使用新模型更新数据库中所有记录的特征向量吗？\n\n"
                f"选择的模型文件：{model_path}\n"
                f"类别ID处理：{class_id_action}\n\n"
                "注意：\n"
                "• 此操作将重新计算所有记录的特征向量\n"
                f"• {class_id_action}\n"
                "• 操作前会自动备份数据库\n"
                "• 根据记录数量，此过程可能需要较长时间\n"
                "• 如果新模型的特征向量维度与原模型不同，将重建整个数据库"
            )

            user_choice = ScaledMessageBox.askyesno(
                "确认更新特征向量",
                confirm_msg,
                parent=parent_window,
                language=self.current_language
            )

            if not user_choice:
                return  # 用户取消操作

            # 4. 在后台线程中执行更新操作
            self.start_update_vectors_process(model_path, parent_window, update_class_ids)

        except Exception as e:
            ScaledMessageBox.showerror(
                "错误",
                f"启动更新操作失败: {str(e)}",
                parent=parent_window,
                language=self.current_language
            )

    def check_class_def_availability(self):
        """
        检查class_def.txt文件是否可用，如果不可用则提示用户选择

        Returns:
            bool: True如果文件可用，False如果不可用
        """
        try:
            # 尝试导入并调用read_path_config
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.append(parent_dir)

            from usrlib.usrlib import read_path_config

            # 尝试读取路径配置
            clsdef_dir, _, _, _, _, _, _ = read_path_config()
            clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')

            # 检查文件是否存在
            if os.path.exists(clsdef_file):
                return True
            else:
                # 文件不存在，检查本地是否有可用的class_def.txt
                local_class_def = os.path.join(current_dir, 'class_def.txt')
                if os.path.exists(local_class_def):
                    # 询问用户是否使用本地文件
                    use_local = ScaledMessageBox.askyesno(
                        "类别定义文件",
                        f"配置的class_def.txt文件不存在：\n{clsdef_file}\n\n"
                        f"检测到本地class_def.txt文件：\n{local_class_def}\n\n"
                        "是否使用本地文件？\n\n"
                        "选择\"否\"将打开文件选择对话框。",
                        parent=self.root,
                        language=self.current_language
                    )

                    if use_local:
                        # 用户选择使用本地文件，更新配置
                        self.update_class_def_path(local_class_def)
                        return True

                # 提示用户选择class_def.txt文件
                return self.prompt_user_select_class_def()

        except Exception as e:
            print(f"检查class_def.txt可用性失败: {e}")
            # 配置文件问题，提示用户选择class_def.txt文件
            return self.prompt_user_select_class_def()

    def prompt_user_select_class_def(self):
        """
        提示用户选择class_def.txt文件

        Returns:
            bool: True如果用户选择了文件，False如果取消
        """
        choice = ScaledMessageBox.askyesnocancel(
            "类别定义文件不可用",
            "无法找到class_def.txt文件。\n\n"
            "选择\"是\"：手动选择class_def.txt文件\n"
            "选择\"否\"：跳过类别ID更新功能\n"
            "选择\"取消\"：取消当前操作",
            parent=self.root,
            language=self.current_language
        )

        if choice is True:  # 用户选择手动选择文件
            class_def_file = self.enhanced_file_dialog(
                'open',
                title="选择class_def.txt文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                dialog_purpose='class_def'
            )

            if class_def_file:
                # 验证文件格式
                if self.validate_class_def_file(class_def_file):
                    self.update_class_def_path(class_def_file)
                    ScaledMessageBox.showinfo(
                        "文件选择成功",
                        f"已选择class_def.txt文件：\n{class_def_file}",
                        parent=self.root,
                        language=self.current_language
                    )
                    return True
                else:
                    ScaledMessageBox.showerror(
                        "文件格式错误",
                        "选择的文件不是有效的class_def.txt格式。",
                        parent=self.root,
                        language=self.current_language
                    )
                    return False
            else:
                return False  # 用户取消选择
        elif choice is False:  # 用户选择跳过
            return False
        else:  # 用户取消操作
            return None

    def validate_class_def_file(self, file_path):
        """
        验证class_def.txt文件格式

        Args:
            file_path: 文件路径

        Returns:
            bool: True如果格式正确，False如果格式错误
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 检查至少有一行有效数据
            valid_lines = 0
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 2:  # 至少包含class_id和class_name
                        valid_lines += 1

            return valid_lines > 0

        except Exception as e:
            print(f"验证class_def.txt文件失败: {e}")
            return False

    def update_class_def_path(self, class_def_path):
        """
        更新class_def.txt文件路径配置

        Args:
            class_def_path: 新的class_def.txt文件路径
        """
        try:
            import os
            import json

            # 获取class_def.txt所在目录
            clsdef_dir = os.path.dirname(class_def_path)

            # 查找pathsetting.json文件
            current_dir = os.path.dirname(os.path.abspath(__file__))
            pathsetting_file = os.path.join(current_dir, 'pathsetting.json')

            if os.path.exists(pathsetting_file):
                # 读取现有配置
                with open(pathsetting_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新clsdef_dir
                config['clsdef_dir'] = clsdef_dir

                # 写回配置文件
                with open(pathsetting_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)

                print(f"已更新pathsetting.json中的clsdef_dir为: {clsdef_dir}")
            else:
                # 创建新的配置文件
                config = {
                    "clsdef_dir": clsdef_dir,
                    "annotation_path_train": "",
                    "annotation_path_val": "",
                    "annotation_path_test": "",
                    "windows_path": "",
                    "linux_path": "",
                    "windows_path_local": ""
                }

                with open(pathsetting_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)

                print(f"已创建pathsetting.json文件，clsdef_dir设置为: {clsdef_dir}")

        except Exception as e:
            print(f"更新class_def.txt路径配置失败: {e}")
            ScaledMessageBox.showwarning(
                "配置更新失败",
                f"无法更新配置文件：{e}\n\n"
                "请手动更新pathsetting.json文件中的clsdef_dir路径。",
                parent=self.root,
                language=self.current_language
            )

    def start_update_vectors_process(self, model_path, parent_window, update_class_ids=False):
        """
        启动更新特征向量的后台进程

        Args:
            model_path: 模型文件路径
            parent_window: 父窗口
            update_class_ids: 是否同时更新类别ID
        """
        # 创建进度显示窗口
        progress_window = self.create_update_progress_window(parent_window)

        def update_thread():
            try:
                # 加载模型
                progress_window.update_status("正在加载模型...")

                from core_functions import load_arcface_model
                model = load_arcface_model(model_path)

                if model is None:
                    self.root.after(0, lambda: self.on_update_error(
                        "模型加载失败", progress_window, parent_window
                    ))
                    return

                # 执行更新
                from core_functions import update_database_vectors

                def progress_callback(current, total, message):
                    """进度回调函数"""
                    self.root.after(0, lambda: progress_window.update_progress(current, total, message))

                success, updated_count, failed_count, result_msg = update_database_vectors(
                    model, progress_callback, update_class_ids
                )

                # 更新完成，显示结果
                self.root.after(0, lambda: self.on_update_complete(
                    success, updated_count, failed_count, result_msg,
                    progress_window, parent_window
                ))

            except Exception as e:
                error_msg = f"更新过程中发生异常: {str(e)}"
                self.root.after(0, lambda: self.on_update_error(
                    error_msg, progress_window, parent_window
                ))

        # 启动后台线程
        threading.Thread(target=update_thread, daemon=True).start()

    def create_update_progress_window(self, parent_window):
        """
        创建更新进度显示窗口

        Args:
            parent_window: 父窗口

        Returns:
            UpdateProgressWindow: 进度窗口对象
        """
        class UpdateProgressWindow:
            def __init__(self, parent, main_app):
                self.parent = parent
                self.main_app = main_app
                self.cancelled = False

                # 创建进度窗口
                self.window = tk.Toplevel(parent)
                self.window.title("更新特征向量")
                self.window.geometry("500x300")
                self.window.transient(parent)
                self.window.grab_set()
                self.window.resizable(False, False)

                # 居中显示
                self.window.update_idletasks()
                x = (self.window.winfo_screenwidth() - 500) // 2
                y = (self.window.winfo_screenheight() - 300) // 2
                self.window.geometry(f"500x300+{x}+{y}")

                # 创建界面元素
                self.setup_ui()

                # 处理窗口关闭事件
                self.window.protocol("WM_DELETE_WINDOW", self.on_close)

            def setup_ui(self):
                """设置界面元素"""
                main_frame = tk.Frame(self.window)
                main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

                # 标题
                title_label = tk.Label(main_frame, text="正在更新数据库特征向量",
                                     font=('Arial', 14, 'bold'))
                title_label.pack(pady=(0, 20))

                # 状态标签
                self.status_label = tk.Label(main_frame, text="准备开始...",
                                           font=('Arial', 10))
                self.status_label.pack(pady=(0, 10))

                # 进度条
                from tkinter import ttk
                self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
                self.progress_bar.pack(fill=tk.X, pady=(0, 10))

                # 进度文本
                self.progress_text = tk.Label(main_frame, text="0/0",
                                            font=('Arial', 9))
                self.progress_text.pack(pady=(0, 20))

                # 详细信息文本框
                info_frame = tk.Frame(main_frame)
                info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

                self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD,
                                       font=('Arial', 9))
                info_scrollbar = tk.Scrollbar(info_frame, orient=tk.VERTICAL,
                                            command=self.info_text.yview)
                self.info_text.configure(yscrollcommand=info_scrollbar.set)

                self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                # 取消按钮
                self.cancel_btn = tk.Button(main_frame, text="取消",
                                          command=self.cancel_update,
                                          font=('Arial', 10))
                self.cancel_btn.pack()

            def update_status(self, message):
                """更新状态信息"""
                if not self.cancelled:
                    self.status_label.config(text=message)
                    self.add_info(message)

            def update_progress(self, current, total, message):
                """更新进度"""
                if not self.cancelled:
                    if total > 0:
                        progress = (current / total) * 100
                        self.progress_bar.config(value=progress)
                        self.progress_text.config(text=f"{current}/{total}")

                    self.status_label.config(text=message)
                    self.add_info(f"[{current}/{total}] {message}")

            def add_info(self, message):
                """添加信息到详细信息框"""
                if not self.cancelled:
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                    self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
                    self.info_text.see(tk.END)
                    self.window.update_idletasks()

            def cancel_update(self):
                """取消更新"""
                self.cancelled = True
                self.cancel_btn.config(state='disabled', text="正在取消...")
                self.add_info("用户请求取消操作...")

            def on_close(self):
                """处理窗口关闭"""
                if not self.cancelled:
                    self.cancel_update()

            def close(self):
                """关闭窗口"""
                try:
                    self.window.destroy()
                except:
                    pass

        return UpdateProgressWindow(parent_window, self)

    def on_update_complete(self, success, updated_count, failed_count, result_msg,
                          progress_window, parent_window):
        """
        更新完成回调

        Args:
            success: 是否成功
            updated_count: 成功更新的记录数
            failed_count: 失败的记录数
            result_msg: 结果消息
            progress_window: 进度窗口
            parent_window: 父窗口
        """
        try:
            # 关闭进度窗口
            progress_window.close()

            # 显示结果
            if success:
                title = "更新完成"
                message = f"特征向量更新成功！\n\n{result_msg}"
                ScaledMessageBox.showinfo(
                    title, message, parent=parent_window, language=self.current_language
                )

                # 刷新数据库显示
                parent_window.destroy()  # 关闭当前数据库窗口
                self.view_database()     # 重新打开数据库窗口
            else:
                title = "更新失败"
                message = f"特征向量更新失败！\n\n{result_msg}"
                ScaledMessageBox.showerror(
                    title, message, parent=parent_window, language=self.current_language
                )

        except Exception as e:
            ScaledMessageBox.showerror(
                "错误",
                f"处理更新结果时发生错误: {str(e)}",
                parent=parent_window,
                language=self.current_language
            )

    def on_update_error(self, error_msg, progress_window, parent_window):
        """
        更新错误回调

        Args:
            error_msg: 错误消息
            progress_window: 进度窗口
            parent_window: 父窗口
        """
        try:
            # 关闭进度窗口
            progress_window.close()

            # 显示错误
            ScaledMessageBox.showerror(
                "更新失败",
                error_msg,
                parent=parent_window,
                language=self.current_language
            )

        except Exception as e:
            print(f"处理更新错误时发生异常: {str(e)}")


if __name__ == "__main__":
    # 初始化日志系统
    try:
        from core_functions import setup_logging
        setup_logging()
    except Exception as e:
        print(f"初始化日志系统失败: {e}")

    # 创建主窗口
    root = tk.Tk()

    # 创建应用程序实例
    app = ChannelScanViewer(root)

    # 启动主循环
    root.mainloop()