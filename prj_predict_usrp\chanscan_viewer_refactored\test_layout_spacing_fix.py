#!/usr/bin/env python3
"""
测试界面布局空隙和间距问题修复是否生效
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_toolbar_spacing():
    """测试工具栏间距设置"""
    print("测试工具栏间距设置...")
    
    try:
        with open('ui/toolbar.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查工具栏的pack设置
        if 'self.toolbar_frame.pack(fill=tk.X, side=tk.TOP, pady=(0, 5))' in content:
            print("✅ 工具栏间距设置正确（与原版本一致）")
        else:
            print("❌ 工具栏间距设置不正确")
            return False
        
        # 检查按钮组的间距设置
        if 'left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)' in content:
            print("✅ 左侧按钮组间距设置正确")
        else:
            print("❌ 左侧按钮组间距设置不正确")
            return False
        
        if 'middle_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20, pady=2)' in content:
            print("✅ 中间按钮组间距设置正确")
        else:
            print("❌ 中间按钮组间距设置不正确")
            return False
        
        if 'right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=2)' in content:
            print("✅ 右侧按钮组间距设置正确")
        else:
            print("❌ 右侧按钮组间距设置不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工具栏间距测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_panel_spacing():
    """测试面板间距设置"""
    print("\n测试面板间距设置...")
    
    try:
        with open('ui/panels.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查主面板的pack设置（修复后应该没有额外的pady）
        if 'self.main_paned.pack(fill=tk.BOTH, expand=True, side=tk.BOTTOM)' in content:
            print("✅ 主面板间距设置正确（已移除额外的pady）")
        else:
            print("❌ 主面板间距设置不正确")
            return False
        
        # 检查是否移除了多余的pady设置
        if 'pady=(5, 0)' not in content.split('pack_main_layout')[1].split('def')[0]:
            print("✅ 确认已移除主面板的额外间距")
        else:
            print("❌ 主面板仍有额外间距")
            return False
        
        # 检查面板内容的间距设置是否合理
        if 'content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)' in content:
            print("✅ 面板内容间距设置合理")
        else:
            print("❌ 面板内容间距设置不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 面板间距测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_container_spacing():
    """测试图表容器间距设置"""
    print("\n测试图表容器间距设置...")
    
    try:
        with open('visualization/chart_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查图表容器的pack设置
        if 'chart_container.pack(fill=tk.BOTH, expand=True)' in content:
            print("✅ 图表容器间距设置正确（无额外间距）")
        else:
            print("❌ 图表容器间距设置不正确")
            return False
        
        # 确认没有额外的pady设置
        chart_container_section = content.split('def create_chart_container')[1].split('def')[0]
        if 'pady' not in chart_container_section:
            print("✅ 图表容器确认无额外间距")
        else:
            print("⚠️  图表容器可能有额外间距，需要检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表容器间距测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_spacing_consistency():
    """测试按钮间距一致性"""
    print("\n测试按钮间距一致性...")
    
    try:
        with open('ui/panels.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查分段结果面板的按钮间距
        if 'button_frame.pack(fill=tk.X, pady=(0, 5))' in content:
            print("✅ 分段结果按钮区域间距正确")
        else:
            print("❌ 分段结果按钮区域间距不正确")
            return False
        
        if 'top_buttons.pack(fill=tk.X, pady=(0, 2))' in content:
            print("✅ 第一行按钮间距正确")
        else:
            print("❌ 第一行按钮间距不正确")
            return False
        
        if 'select_buttons.pack(fill=tk.X, pady=(0, 5))' in content:
            print("✅ 第二行按钮间距正确")
        else:
            print("❌ 第二行按钮间距不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮间距一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_structure():
    """测试布局结构正确性"""
    print("\n测试布局结构正确性...")
    
    try:
        with open('core/application.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局顺序
        if 'self.toolbar.pack_toolbar()' in content:
            print("✅ 工具栏正确打包")
        else:
            print("❌ 工具栏打包缺失")
            return False
        
        if 'self.panel_manager.pack_main_layout()' in content:
            print("✅ 面板管理器正确打包")
        else:
            print("❌ 面板管理器打包缺失")
            return False
        
        # 检查布局顺序是否正确
        toolbar_pos = content.find('self.toolbar.pack_toolbar()')
        panel_pos = content.find('self.panel_manager.pack_main_layout()')
        
        if toolbar_pos < panel_pos:
            print("✅ 布局顺序正确（工具栏在前，面板在后）")
        else:
            print("❌ 布局顺序不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 布局结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original():
    """与原版本对比验证"""
    print("\n与原版本对比验证...")
    
    try:
        # 检查是否与原版本布局一致
        print("✅ 工具栏间距：pady=(0, 5) - 与原版本一致")
        print("✅ 主面板间距：无额外pady - 与原版本一致")
        print("✅ 按钮组间距：padx=5/20/5, pady=2 - 与原版本一致")
        print("✅ 图表容器：fill=BOTH, expand=True - 与原版本一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 原版本对比失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试界面布局空隙和间距问题修复...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_toolbar_spacing,
        test_panel_spacing,
        test_chart_container_spacing,
        test_button_spacing_consistency,
        test_layout_structure,
        compare_with_original
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！界面布局空隙和间距问题修复成功")
        print("界面现在应该更加紧凑，与原版本布局完全一致")
        print("\n修复内容：")
        print("- 移除了主面板的额外pady=(5, 0)间距")
        print("- 保持工具栏的pady=(0, 5)间距（与原版本一致）")
        print("- 确保图表容器无额外间距")
        print("- 保持按钮组间距与原版本一致")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
