"""
绘图渲染模块

负责具体的图表绘制和渲染，包括时域图、频域图、时频图等的绘制逻辑。
保持与原有代码完全一致的绘图优化和样式。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass


@dataclass
class SegmentInfo:
    """分段信息数据类"""
    has_segment: bool = False
    start_sample: int = 0
    end_sample: int = 0
    class_name: str = "Unknown"
    duration: float = 0.0
    center_freq: float = 0.0
    bandwidth: float = 0.0
    snr: float = 0.0


class PlotRenderer:
    """
    绘图渲染器
    
    负责具体的图表绘制和渲染，保持与原有代码完全一致的绘图方法和优化。
    """
    
    def __init__(self, signal_processor, i18n_manager):
        """
        初始化绘图渲染器
        
        Args:
            signal_processor: 信号处理器
            i18n_manager: 国际化管理器
        """
        self.signal_processor = signal_processor
        self.i18n_manager = i18n_manager
    
    def create_smart_sampling_indices(self, data_length: int, max_points: int) -> Tuple[np.ndarray, int]:
        """
        创建智能抽样索引（与首页方法一致）
        
        Args:
            data_length: 数据长度
            max_points: 最大点数
            
        Returns:
            (indices, step): 抽样索引和步长
        """
        if data_length <= max_points:
            # 数据量小，不需要抽样
            indices = np.arange(data_length)
            step = 1
        else:
            # 需要抽样
            step = data_length // max_points
            indices = np.arange(0, data_length, step)
            
            # 确保不超过最大点数
            if len(indices) > max_points:
                indices = indices[:max_points]
        
        return indices, step
    
    def plot_homepage_time_domain_points(self, ax, signal_data: np.ndarray, fs: float, 
                                       metadata: Dict[str, Any], segment_info: Optional[SegmentInfo] = None):
        """
        绘制时域信号点值图（严格按照首页方式）
        
        Args:
            ax: matplotlib坐标轴
            signal_data: 信号数据
            fs: 采样率
            metadata: 元数据
            segment_info: 分段信息
        """
        try:
            # 按照首页的智能抽样方式
            max_points = 500000  # 与首页一致的最大点数
            indices, step = self.create_smart_sampling_indices(len(signal_data), max_points)

            # 抽样信号数据
            sampled_signal = signal_data[indices]
            sampled_indices = indices / 1e5  # 转换为1e5单位

            # 提取I和Q分量
            signal_i = np.real(sampled_signal)
            signal_q = np.imag(sampled_signal)

            # 绘制I和Q信号（与原版本完全一致的样式，不显示图例）
            ax.plot(sampled_indices, signal_i, 'b-', linewidth=0.5, rasterized=True)
            ax.plot(sampled_indices, signal_q, 'r-', linewidth=0.5, rasterized=True)

            # 标出选中的分段
            if segment_info and segment_info.has_segment:
                start_idx = segment_info.start_sample
                end_idx = segment_info.end_sample

                # 转换为1e5单位
                start_1e5 = start_idx / 1e5
                end_1e5 = end_idx / 1e5

                # 添加分段区域高亮（与首页分段标记一致）
                ax.axvspan(start_1e5, end_1e5, color='yellow', alpha=0.3, label='选中分段')

                # 添加分段边界线
                ax.axvline(start_1e5, color='green', linestyle='--', linewidth=2, alpha=0.8, label='分段起始')
                ax.axvline(end_1e5, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='分段结束')

                # 添加分段信息文本
                segment_center = (start_1e5 + end_1e5) / 2
                y_min, y_max = ax.get_ylim()
                text_y = y_max - (y_max - y_min) * 0.1

                duration_ms = segment_info.duration * 1000
                info_text = f"{segment_info.class_name}\n{duration_ms:.2f} ms"

                ax.text(segment_center, text_y, info_text,
                       ha='center', va='top', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.4', fc='yellow', ec='black', lw=0.5, alpha=0.85))

            # 设置标题和标签（与首页一致）
            texts = self.i18n_manager.get_all_texts()
            ax.set_title(texts['time_domain_points'])
            ax.set_xlabel(texts['time_points_unit'])
            ax.set_ylabel(texts['signal_voltage'])
            ax.grid(True, alpha=0.3)

            # 设置y轴范围（与原版本一致）
            y_min, y_max = self.signal_processor.calculate_adaptive_ylim(signal_i, signal_q)
            ax.set_ylim(y_min, y_max)

            # 设置X轴范围和刻度（与原版本完全一致）
            N_sig = len(signal_data)
            x_max_1e5 = (N_sig - 1) / 1e5
            ax.set_xlim(0, x_max_1e5)

            # 设置刻度（与MATLAB对齐：每0.5×10^5一个刻度）
            tick_step = 0.5  # MATLAB对齐：步长为0.5
            x_ticks = np.arange(0, x_max_1e5 + tick_step, tick_step)
            ax.set_xticks(x_ticks)
            # 根据刻度值决定显示格式：整数显示为整数，小数显示为小数
            ax.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in x_ticks])

        except Exception as e:
            ax.text(0.5, 0.5, f'时域信号点值图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')
    
    def plot_homepage_time_domain_signal(self, ax, signal_data: np.ndarray, fs: float,
                                       metadata: Dict[str, Any], segment_info: Optional[SegmentInfo] = None):
        """
        绘制时域信号图（严格按照首页方式）
        
        Args:
            ax: matplotlib坐标轴
            signal_data: 信号数据
            fs: 采样率
            metadata: 元数据
            segment_info: 分段信息
        """
        try:
            # 按照首页的智能抽样方式
            max_points = 500000  # 与首页一致的最大点数
            indices, step = self.create_smart_sampling_indices(len(signal_data), max_points)

            # 抽样信号数据
            sampled_signal = signal_data[indices]
            
            # 创建时间轴（毫秒）- 与原版本一致
            time_ms = indices / fs * 1000

            # 提取I和Q分量（与原版本一致）
            signal_i = np.real(sampled_signal)
            signal_q = np.imag(sampled_signal)

            # 绘制I和Q信号（与原版本完全一致的样式）
            ax.plot(time_ms, signal_i, 'b-', linewidth=0.5)
            ax.plot(time_ms, signal_q, 'r-', linewidth=0.5)

            # 标出选中的分段
            if segment_info and segment_info.has_segment:
                start_time_ms = segment_info.start_sample / fs * 1000
                end_time_ms = segment_info.end_sample / fs * 1000

                # 添加分段区域高亮
                ax.axvspan(start_time_ms, end_time_ms, color='yellow', alpha=0.3, label='选中分段')

                # 添加分段边界线
                ax.axvline(start_time_ms, color='green', linestyle='--', linewidth=2, alpha=0.8)
                ax.axvline(end_time_ms, color='orange', linestyle='--', linewidth=2, alpha=0.8)

            # 设置标题和标签（与原版本一致）
            texts = self.i18n_manager.get_all_texts()
            ax.set_title(texts['time_domain_signal'])
            ax.set_xlabel(texts['time_ms'])
            ax.set_ylabel(texts['voltage_v'])  # 与原版本一致，使用电压单位
            ax.grid(True, alpha=0.3)

            # 设置y轴范围（与原版本一致）
            y_min, y_max = self.signal_processor.calculate_adaptive_ylim(signal_i, signal_q)
            ax.set_ylim(y_min, y_max)

            # 设置X轴范围为完整的时间范围（与原版本一致）
            N_sig = len(signal_data)
            signal_time_start = 0
            signal_time_end = (N_sig - 1) / fs * 1000  # 完整数据的时间范围(ms)
            ax.set_xlim(signal_time_start, signal_time_end)

        except Exception as e:
            ax.text(0.5, 0.5, f'时域信号图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')
    
    def plot_homepage_frequency_spectrum(self, ax, signal_data: np.ndarray, fs: float, fc: float,
                                       metadata: Dict[str, Any], segment_info: Optional[SegmentInfo] = None):
        """
        绘制频谱图（严格按照首页方式）
        
        Args:
            ax: matplotlib坐标轴
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            metadata: 元数据
            segment_info: 分段信息
        """
        try:
            # 使用首页的FFT计算方法
            freqs_mhz, fftdata = self.signal_processor.compute_fft_sync(signal_data, fs, fc)

            if len(freqs_mhz) > 0 and len(fftdata) > 0:
                # 绘制频谱（与原版本一致的样式）
                ax.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)

                # 消除X轴留白：精确设置频率范围（与原版本一致）
                ax.set_xlim(freqs_mhz[0], freqs_mhz[-1])

                # MATLAB对齐：自适应设置X轴刻度，显示关键频率点（与原版本一致）
                key_freqs = self.signal_processor.find_adaptive_frequency_ticks(freqs_mhz, fftdata)

                if key_freqs:
                    ax.set_xticks(key_freqs)
                    ax.set_xticklabels([f'{f:.1f}' if f != int(f) else f'{int(f)}' for f in key_freqs])

            # 设置标题和标签（与原版本一致）
            texts = self.i18n_manager.get_all_texts()
            ax.set_title(f'{texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
            ax.set_xlabel(texts['frequency_mhz'])
            ax.set_ylabel(texts['spectrum_value'])
            ax.grid(True, alpha=0.3)

        except Exception as e:
            ax.text(0.5, 0.5, f'频谱图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')
    
    def plot_homepage_time_frequency(self, ax, signal_data: np.ndarray, fs: float, fc: float,
                                   metadata: Dict[str, Any], segment_info: Optional[SegmentInfo] = None):
        """
        绘制时频图（严格按照首页方式）
        
        Args:
            ax: matplotlib坐标轴
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            metadata: 元数据
            segment_info: 分段信息
        """
        try:
            # 使用首页的时频图计算方法
            F_mhz, T_ms, Pxx_db, success = self.signal_processor.compute_spectrogram_sync(signal_data, fs, fc)

            if success and F_mhz is not None and T_ms is not None:
                # 绘制时频图（与原版本一致的样式，不添加颜色条）
                im = ax.imshow(Pxx_db, aspect='auto', cmap='viridis',
                              extent=[T_ms[0], T_ms[-1], F_mhz[0], F_mhz[-1]],
                              origin='lower', interpolation='bilinear')

                # 不添加颜色条（与原版本一致）

                # 标出选中分段的时间和频率范围
                if segment_info and segment_info.has_segment:
                    start_time_ms = segment_info.start_sample / fs * 1000
                    end_time_ms = segment_info.end_sample / fs * 1000
                    
                    # 添加时间范围线
                    ax.axvline(start_time_ms, color='white', linestyle='--', linewidth=2, alpha=0.8)
                    ax.axvline(end_time_ms, color='white', linestyle='--', linewidth=2, alpha=0.8)
                    
                    # 如果有频率信息，添加频率范围线
                    if segment_info.center_freq > 0:
                        center_freq_mhz = segment_info.center_freq / 1e6
                        bandwidth_mhz = segment_info.bandwidth / 1e6
                        
                        start_freq = center_freq_mhz - bandwidth_mhz / 2
                        end_freq = center_freq_mhz + bandwidth_mhz / 2
                        
                        ax.axhline(start_freq, color='white', linestyle='--', linewidth=2, alpha=0.8)
                        ax.axhline(end_freq, color='white', linestyle='--', linewidth=2, alpha=0.8)

            else:
                # 如果时频图计算失败，显示简化的幅度数据
                amplitude = np.abs(signal_data)
                time_ms = np.arange(len(amplitude)) / fs * 1000
                
                # 抽样显示
                if len(amplitude) > 10000:
                    step = len(amplitude) // 10000
                    amplitude = amplitude[::step]
                    time_ms = time_ms[::step]
                
                ax.plot(time_ms, amplitude, 'b-', linewidth=0.5)

            # 设置标题和标签（与原版本一致）
            texts = self.i18n_manager.get_all_texts()
            ax.set_title(f'{texts["time_frequency"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
            ax.set_xlabel(texts['time_ms'])  # X轴：时间(ms)
            ax.set_ylabel(texts['frequency_mhz'])  # Y轴：频率(MHz)

            # 设置轴范围（与原版本一致）
            if success and F_mhz is not None and T_ms is not None:
                # 时间轴对应完整数据范围
                if len(T_ms) > 1:
                    ax.set_xlim(T_ms[0], T_ms[-1])

                # 频率轴范围
                if len(F_mhz) > 1:
                    ax.set_ylim(F_mhz[0], F_mhz[-1])

        except Exception as e:
            ax.text(0.5, 0.5, f'时频图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_analysis_using_homepage_methods(self, fig, signal_data: np.ndarray, fs: float, fc: float,
                                           segment_info: Optional[SegmentInfo] = None):
        """
        使用首页的绘制方法绘制分析图表

        Args:
            fig: matplotlib图形对象
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            segment_info: 分段信息
        """
        try:
            # 清除现有图表
            fig.clear()

            # 创建1x4垂直子图布局（与原版本一致）
            ax1 = fig.add_subplot(4, 1, 1)  # 时域信号点值图
            ax2 = fig.add_subplot(4, 1, 2)  # 时域信号图
            ax3 = fig.add_subplot(4, 1, 3)  # 频谱图
            ax4 = fig.add_subplot(4, 1, 4)  # 时频图

            # 获取元数据
            metadata = {
                'fs': fs,
                'fc': fc,
                'length': len(signal_data),
                'duration': len(signal_data) / fs
            }

            # 绘制各个图表
            self.plot_homepage_time_domain_points(ax1, signal_data, fs, metadata, segment_info)
            self.plot_homepage_time_domain_signal(ax2, signal_data, fs, metadata, segment_info)
            self.plot_homepage_frequency_spectrum(ax3, signal_data, fs, fc, metadata, segment_info)
            self.plot_homepage_time_frequency(ax4, signal_data, fs, fc, metadata, segment_info)

            # 调整布局
            fig.tight_layout(pad=2.0)

            return True

        except Exception as e:
            print(f"绘制分析图表失败: {e}")
            return False

    def add_segment_markers_to_plots(self, axes: List, segments: List[Dict], fs: float, fc: float):
        """
        在图表上添加信道扫描分段标记

        Args:
            axes: 坐标轴列表
            segments: 分段信息列表
            fs: 采样率
            fc: 中心频率
        """
        try:
            if not axes or not segments:
                return

            for segment in segments:
                start_sample = segment.get('start_sample', 0)
                end_sample = segment.get('end_sample', 0)
                class_name = segment.get('class_name', 'Unknown')
                confidence = segment.get('confidence', 0.0)

                # 在时域图上添加标记
                if len(axes) > 0:  # 时域信号点值图
                    ax = axes[0]
                    start_1e5 = start_sample / 1e5
                    end_1e5 = end_sample / 1e5

                    # 添加分段区域
                    ax.axvspan(start_1e5, end_1e5, color='red', alpha=0.2)

                    # 添加标签
                    center_1e5 = (start_1e5 + end_1e5) / 2
                    y_min, y_max = ax.get_ylim()
                    text_y = y_max - (y_max - y_min) * 0.05

                    ax.text(center_1e5, text_y, f"{class_name}\n{confidence:.2f}",
                           ha='center', va='top', fontsize=8,
                           bbox=dict(boxstyle='round,pad=0.3', fc='red', alpha=0.7))

                # 在频谱图上添加标记
                if len(axes) > 2:  # 频谱图
                    ax = axes[2]
                    center_freq_mhz = segment.get('center_freq', fc) / 1e6
                    bandwidth_mhz = segment.get('bandwidth', 0) / 1e6

                    if bandwidth_mhz > 0:
                        start_freq = center_freq_mhz - bandwidth_mhz / 2
                        end_freq = center_freq_mhz + bandwidth_mhz / 2

                        ax.axvspan(start_freq, end_freq, color='red', alpha=0.2)

                # 在时频图上添加标记
                if len(axes) > 3:  # 时频图
                    ax = axes[3]
                    start_time_ms = start_sample / fs * 1000
                    end_time_ms = end_sample / fs * 1000

                    ax.axvline(start_time_ms, color='red', linestyle='--', linewidth=1, alpha=0.8)
                    ax.axvline(end_time_ms, color='red', linestyle='--', linewidth=1, alpha=0.8)

                    # 如果有频率信息，添加频率标记
                    center_freq_mhz = segment.get('center_freq', fc) / 1e6
                    bandwidth_mhz = segment.get('bandwidth', 0) / 1e6

                    if bandwidth_mhz > 0:
                        start_freq = center_freq_mhz - bandwidth_mhz / 2
                        end_freq = center_freq_mhz + bandwidth_mhz / 2

                        ax.axhline(start_freq, color='red', linestyle='--', linewidth=1, alpha=0.8)
                        ax.axhline(end_freq, color='red', linestyle='--', linewidth=1, alpha=0.8)

        except Exception as e:
            print(f"添加分段标记失败: {e}")

    def plot_3d_power_spectral_density(self, ax, signal_data: np.ndarray, fs: float, fc: float):
        """
        绘制3D功率谱密度图

        Args:
            ax: 3D坐标轴
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
        """
        try:
            # 计算时频图数据
            F_mhz, T_ms, Pxx_db, success = self.signal_processor.compute_spectrogram_sync(signal_data, fs, fc)

            if success and F_mhz is not None and T_ms is not None:
                # 创建网格
                T_grid, F_grid = np.meshgrid(T_ms, F_mhz)

                # 绘制3D表面
                surf = ax.plot_surface(T_grid, F_grid, Pxx_db, cmap='viridis', alpha=0.8)

                # 设置标签
                texts = self.i18n_manager.get_all_texts()
                ax.set_xlabel(texts['time_ms'])
                ax.set_ylabel(texts['frequency_mhz'])
                ax.set_zlabel(texts['power_db'])
                ax.set_title(texts['power_spectral_density_3d'])

            else:
                # 显示错误信息
                ax.text(0.5, 0.5, 0.5, '3D功率谱密度图计算失败',
                       transform=ax.transAxes, ha='center', va='center')

        except Exception as e:
            ax.text(0.5, 0.5, 0.5, f'3D功率谱密度图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def plot_filtered_psd_3d(self, ax, signal_data: np.ndarray, fs: float, fc: float):
        """
        绘制滤波后的3D功率谱密度图

        Args:
            ax: 3D坐标轴
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
        """
        try:
            # 简单的低通滤波（这里可以根据需要实现更复杂的滤波）
            from scipy.signal import butter, filtfilt

            # 设计低通滤波器
            nyquist = fs / 2
            cutoff = nyquist * 0.8  # 截止频率为奈奎斯特频率的80%
            b, a = butter(4, cutoff / nyquist, btype='low')

            # 应用滤波器
            filtered_signal = filtfilt(b, a, signal_data)

            # 计算滤波后的时频图
            F_mhz, T_ms, Pxx_db, success = self.signal_processor.compute_spectrogram_sync(filtered_signal, fs, fc)

            if success and F_mhz is not None and T_ms is not None:
                # 创建网格
                T_grid, F_grid = np.meshgrid(T_ms, F_mhz)

                # 绘制3D表面
                surf = ax.plot_surface(T_grid, F_grid, Pxx_db, cmap='plasma', alpha=0.8)

                # 设置标签
                texts = self.i18n_manager.get_all_texts()
                ax.set_xlabel(texts['time_ms'])
                ax.set_ylabel(texts['frequency_mhz'])
                ax.set_zlabel(texts['power_db'])
                ax.set_title(texts['filtered_psd_3d'])

            else:
                ax.text(0.5, 0.5, 0.5, '滤波后3D功率谱密度图计算失败',
                       transform=ax.transAxes, ha='center', va='center')

        except Exception as e:
            ax.text(0.5, 0.5, 0.5, f'滤波后3D功率谱密度图绘制失败:\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def display_signal_2d(self, fig, signal_data: np.ndarray, fs: float, fc: float, metadata: Dict[str, Any]):
        """
        显示2D信号分析图表（完整的data_viewer实现）

        Args:
            fig: matplotlib图形对象
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            metadata: 元数据
        """
        try:
            # 清除现有图表
            fig.clear()

            # 创建1x4垂直子图布局（与原版本一致）
            axes = []
            axes.append(fig.add_subplot(4, 1, 1))  # 时域信号点值图
            axes.append(fig.add_subplot(4, 1, 2))  # 时域信号图
            axes.append(fig.add_subplot(4, 1, 3))  # 频谱图
            axes.append(fig.add_subplot(4, 1, 4))  # 时频图

            # 绘制各个图表
            self.plot_homepage_time_domain_points(axes[0], signal_data, fs, metadata)
            self.plot_homepage_time_domain_signal(axes[1], signal_data, fs, metadata)
            self.plot_homepage_frequency_spectrum(axes[2], signal_data, fs, fc, metadata)
            self.plot_homepage_time_frequency(axes[3], signal_data, fs, fc, metadata)

            # 调整布局
            fig.tight_layout(pad=2.0)

            return True

        except Exception as e:
            print(f"显示2D信号分析图表失败: {e}")
            return False

    def display_signal_3d(self, fig, signal_data: np.ndarray, fs: float, fc: float):
        """
        显示3D信号分析图表

        Args:
            fig: matplotlib图形对象
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
        """
        try:
            # 清除现有图表
            fig.clear()

            # 创建2个3D子图
            ax1 = fig.add_subplot(2, 1, 1, projection='3d')
            ax2 = fig.add_subplot(2, 1, 2, projection='3d')

            # 绘制3D图表
            self.plot_3d_power_spectral_density(ax1, signal_data, fs, fc)
            self.plot_filtered_psd_3d(ax2, signal_data, fs, fc)

            # 调整布局
            fig.tight_layout(pad=2.0)

            return True

        except Exception as e:
            print(f"显示3D信号分析图表失败: {e}")
            return False
