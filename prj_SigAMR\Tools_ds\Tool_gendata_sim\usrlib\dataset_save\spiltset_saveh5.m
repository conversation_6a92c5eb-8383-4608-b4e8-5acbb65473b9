function spiltset_saveh5(alldata_file, train_ratio, valid_ratio, test_ratio, output_file)
%==============划分训练集、验证集和测试集，并另存为.h5文件=======

% alldata_file: 待划分数据集文件
% train_ratio: 训练集比例
% valid_ratio: 验证集比例
% test_ratio: 测试集比例
% output_file: 输出文件路径


%% 1. 读取HDF5数据集
% 读取数据
filename = alldata_file;
data = h5read(filename, '/signal_data'); % I/Q数据 [样本数×2×128]
labels = h5read(filename, '/labels'); % 标签 [样本数×1]
snr_values = h5read(filename, '/SNRs'); % SNR值 [样本数×1]

% 读取属性
info = h5info(filename);
mod_type_cell = info.Attributes(1).Value; % 调制类型列表
fs = info.Attributes(2).Value; % 采样率
snr_levels = info.Attributes(3).Value; % 整个信噪比范围
fc = info.Attributes(4).Value; % 中心频率
% 转换数据维度为[样本数×128×2]
% X = permute(X, [2, 1, 3]);

fprintf('数据集加载完成，总样本数: %d\n', size(data,1));
disp('调制类型:');
disp(mod_type_cell(:));

total_size = length(labels);

train_ratio = train_ratio;  % 训练集比例
valid_ratio = valid_ratio;  % 验证集比例
test_ratio = test_ratio;   % 测试集比例
rng(42);           % 设置随机种子保证可重复性

% # 计算各数据集的大小
train_size = (total_size * train_ratio);
valid_size = (total_size * valid_ratio);
test_size = (total_size * test_ratio);

% 获取所有唯一的调制类型和SNR值
unique_mods = unique(labels)';
unique_snrs = unique(snr_values)';

% 初始化索引存储
train_idx = [];
val_idx = [];
test_idx = [];
%% 2. 分层划分数据集
% 对每个调制类型和SNR组合进行分层抽样
for mod = unique_mods
    for snr = unique_snrs
        % 找到当前mod和snr组合的所有索引
        idx_origin = find(labels == mod & snr_values == snr);
        
        idx = idx_origin(randperm(length(idx_origin)));
        idx_train = idx(1:train_size/length(unique_snrs)/length(unique_mods));
        idx_val = idx(1:valid_size/length(unique_snrs)/length(unique_mods));
        idx_test = idx(1:test_size/length(unique_snrs)/length(unique_mods));
   
        train_idx = [train_idx; idx_train];
        val_idx = [val_idx; idx_val];
        test_idx = [test_idx; idx_test];

    end
end

% 根据索引获取数据
X_train = data(train_idx, :, :);
Y_train = labels(train_idx);
Z_train = snr_values(train_idx);

X_val = data(val_idx, :, :);
Y_val = labels(val_idx);
Z_val = snr_values(val_idx);

X_test = data(test_idx, :, :);
Y_test = labels(test_idx);
Z_test = snr_values(test_idx);

fprintf('\n数据集划分结果:\n');
fprintf('训练集: %d 样本 (%.1f%%)\n', length(train_idx), length(train_idx)/length(labels)*100);
fprintf('验证集: %d 样本 (%.1f%%)\n', length(val_idx), length(val_idx)/length(labels)*100);
fprintf('测试集: %d 样本 (%.1f%%)\n', length(test_idx), length(test_idx)/length(labels)*100);

moddatasve_h5file(fullfile(output_file, 'Train_sim.h5'), X_train, Y_train, Z_train, mod_type_cell, fs, fc, snr_levels)
moddatasve_h5file(fullfile(output_file,'Valid_sim.h5'), X_val, Y_val,Z_val, mod_type_cell, fs, fc, snr_levels)
moddatasve_h5file(fullfile(output_file,'Test_sim.h5'), X_test, Y_test, Z_test, mod_type_cell, fs, fc,snr_levels)
end