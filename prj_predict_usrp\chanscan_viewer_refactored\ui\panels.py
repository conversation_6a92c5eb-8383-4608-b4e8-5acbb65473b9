"""
面板管理模块

负责左侧面板的创建和管理，包括文件列表、文件信息、分段结果等面板。
严格保持与原有代码相同的面板大小、布局和功能。
"""

import tkinter as tk
import tkinter.ttk as ttk
from typing import Dict, Callable, Optional, Any


class PanelManager:
    """
    面板管理器
    
    负责左侧面板的创建和管理，保持与原有代码完全一致的面板配置。
    """
    
    def __init__(self, parent: tk.Widget, style_manager, i18n_manager):
        """
        初始化面板管理器
        
        Args:
            parent: 父容器
            style_manager: 样式管理器
            i18n_manager: 国际化管理器
        """
        self.parent = parent
        self.style_manager = style_manager
        self.i18n_manager = i18n_manager
        
        # 面板相关属性
        self.main_paned = None
        self.left_paned = None
        self.panels = {}
        self.panel_collapsed = {}
        
        # UI组件引用
        self.file_listbox = None
        self.info_text = None
        self.segments_tree = None
        self.add_to_db_button = None
        self.skip_button = None
        self.segment_selected = {}
        
        # 回调函数字典
        self.callbacks = {}
        
        # 创建主布局
        self.create_main_layout()
    
    def create_main_layout(self):
        """创建主要布局"""
        # 创建主要的水平分隔器，但不立即pack，等工具栏创建后再pack
        self.main_paned = tk.PanedWindow(self.parent, orient=tk.HORIZONTAL, sashrelief=tk.RAISED)

        # 左侧面板（文件列表、文件信息、分段结果）
        self.create_left_panel()

        return self.main_paned

    def pack_main_layout(self):
        """打包主布局（在工具栏创建后调用）"""
        if self.main_paned:
            # 确保面板在工具栏下方，不覆盖工具栏
            self.main_paned.pack(fill=tk.BOTH, expand=True, side=tk.BOTTOM, pady=(5, 0))
    
    def create_left_panel(self):
        """创建左侧面板"""
        # 左侧容器
        left_container = tk.Frame(self.main_paned)
        self.main_paned.add(left_container, width=450, minsize=350)
        
        # 创建可调节高度的面板系统
        self.create_resizable_panels(left_container)
    
    def create_resizable_panels(self, parent):
        """创建可调节高度的面板系统"""
        # 创建垂直分隔器
        self.left_paned = tk.PanedWindow(parent, orient=tk.VERTICAL, sashrelief=tk.RAISED)
        self.left_paned.pack(fill=tk.BOTH, expand=True)
        
        # 获取文本
        texts = self.i18n_manager.get_all_texts()
        
        # 文件列表面板
        self.create_collapsible_panel('file_list', texts['file_list'], 200, self.create_file_list_content)
        
        # 文件信息面板
        self.create_collapsible_panel('file_info', texts['file_info'], 150, self.create_file_info_content)
        
        # 分段结果面板
        self.create_collapsible_panel('segment_results', texts['segment_results'], 300, self.create_segment_results_content)
    
    def create_collapsible_panel(self, panel_id: str, title: str, height: int, create_content_func: Callable):
        """
        创建可折叠的面板
        
        Args:
            panel_id: 面板ID
            title: 面板标题
            height: 面板高度
            create_content_func: 内容创建函数
        """
        # 面板容器
        panel_frame = tk.Frame(self.left_paned, relief=tk.RAISED, bd=1)
        self.left_paned.add(panel_frame, height=height, minsize=100)
        
        # 标题栏
        title_frame = tk.Frame(panel_frame, bg='#f0f0f0', relief=tk.RAISED, bd=1)
        title_frame.pack(fill=tk.X)
        
        # 标题文本
        large_font = self.style_manager.get_font('large')
        title_label = tk.Label(title_frame, text=title, font=large_font, bg='#f0f0f0')
        title_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 折叠/展开按钮
        default_font = self.style_manager.get_font('default')
        collapse_btn = tk.Button(title_frame, text="−", font=default_font, width=3,
                                command=lambda: self.toggle_panel_collapse(panel_id))
        collapse_btn.pack(side=tk.RIGHT, padx=2, pady=1)
        
        # 内容区域
        content_frame = tk.Frame(panel_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # 调用内容创建函数
        create_content_func(content_frame)
        
        # 存储面板引用
        self.panels[panel_id] = {
            'panel_frame': panel_frame,
            'title_label': title_label,
            'collapse_btn': collapse_btn,
            'content_frame': content_frame,
            'original_height': height
        }
        
        # 初始化折叠状态
        self.panel_collapsed[panel_id] = False
    
    def create_file_list_content(self, parent):
        """创建文件列表内容"""
        # 文件列表框
        list_frame = tk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建带滚动条的列表框
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        small_font = self.style_manager.get_font('small')
        self.file_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, font=small_font)
        self.file_listbox.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.file_listbox.yview)
        
        # 绑定选择事件
        self.file_listbox.bind('<<ListboxSelect>>', self._get_callback('on_file_select'))
    
    def create_file_info_content(self, parent):
        """创建文件信息内容"""
        # 创建文本显示区域
        small_font = self.style_manager.get_font('small')
        self.info_text = tk.Text(parent, wrap=tk.WORD, font=small_font, state=tk.DISABLED)
        
        # 添加滚动条
        info_scrollbar = tk.Scrollbar(parent, command=self.info_text.yview)
        self.info_text.config(yscrollcommand=info_scrollbar.set)
        
        # 布局
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_segment_results_content(self, parent):
        """创建分段结果内容"""
        # 根据语言设置按钮宽度
        current_language = self.i18n_manager.current_language
        if current_language == 'zh':
            btn_widths = {'clear': 8, 'add_db': 12, 'skip': 10, 'select': 6, 'delete': 8}
        else:
            btn_widths = {'clear': 8, 'add_db': 15, 'skip': 10, 'select': 10, 'delete': 12}
        
        # 获取文本和字体
        texts = self.i18n_manager.get_all_texts()
        small_font = self.style_manager.get_font('small')
        
        # 顶部按钮区域
        button_frame = tk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 操作按钮 - 第一行
        top_buttons = tk.Frame(button_frame)
        top_buttons.pack(fill=tk.X, pady=(0, 2))
        
        tk.Button(top_buttons, text=texts['clear_results'], command=self._get_callback('clear_segment_results'),
                 font=small_font, width=btn_widths['clear']).pack(side=tk.LEFT, padx=2)
        
        self.add_to_db_button = tk.Button(top_buttons, text=texts['add_to_db'], command=self._get_callback('add_segments_to_db_action'),
                 font=small_font, width=btn_widths['add_db'], bg='#2196F3', fg='white', state=tk.DISABLED)
        self.add_to_db_button.pack(side=tk.LEFT, padx=2)
        
        self.skip_button = tk.Button(top_buttons, text=texts['skip_file'], command=self._get_callback('skip_to_next_file_action'),
                 font=small_font, width=btn_widths['skip'], bg='#FF9800', fg='white')
        self.skip_button.pack(side=tk.LEFT, padx=2)
        
        # 选择管理按钮 - 第二行
        select_buttons = tk.Frame(button_frame)
        select_buttons.pack(fill=tk.X, pady=(0, 5))
        
        tk.Button(select_buttons, text=texts['select_all'], command=self._get_callback('select_all_segments'),
                 font=small_font, width=btn_widths['select']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(select_buttons, text=texts['deselect_all'], command=self._get_callback('deselect_all_segments'),
                 font=small_font, width=btn_widths['select']).pack(side=tk.LEFT, padx=2)
        
        tk.Button(select_buttons, text=texts['delete_selected'], command=self._get_callback('delete_selected_segments'),
                 font=small_font, width=btn_widths['delete'], bg='#f44336', fg='white').pack(side=tk.LEFT, padx=2)
        
        # 分段结果 - 使用 TreeView 替代 Text
        tree_frame = tk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建TreeView
        columns = ('select', 'segment_id', 'position', 'center_freq', 'bandwidth', 'duration', 'snr')
        self.segments_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=10)

        # 配置TreeView字体 - 增大字体以便更好地查看
        tree_font, tree_font_size, row_height = self.style_manager.get_tree_font_config()

        # 创建并应用TreeView样式
        style = ttk.Style()
        style.configure("Segments.Treeview",
                       font=tree_font,
                       rowheight=row_height)
        style.configure("Segments.Treeview.Heading",
                       font=(tree_font[0], tree_font_size, 'bold'))

        # 应用样式到TreeView
        self.segments_tree.configure(style="Segments.Treeview")
        
        # 设置列标题
        self._setup_tree_headings(current_language)
        
        # 设置列宽 - 根据字体大小调整，使内容更易读
        self._setup_tree_columns(tree_font_size)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.segments_tree.yview)
        self.segments_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.segments_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.segments_tree.bind('<Button-1>', self._get_callback('on_segment_click'))
        self.segments_tree.bind('<Double-1>', self._get_callback('on_segment_double_click'))
        self.segments_tree.bind('<Button-3>', self._get_callback('on_segment_right_click'))  # 右键菜单
        
        # 存储分段选择状态
        self.segment_selected = {}
    
    def _setup_tree_headings(self, language: str):
        """设置TreeView列标题"""
        if language == 'zh':
            self.segments_tree.heading('select', text='选择')
            self.segments_tree.heading('segment_id', text='编号')
            self.segments_tree.heading('position', text='位置')
            self.segments_tree.heading('center_freq', text='中心频率(MHz)')
            self.segments_tree.heading('bandwidth', text='带宽(MHz)')
            self.segments_tree.heading('duration', text='持续时间(ms)')
            self.segments_tree.heading('snr', text='信噪比(dB)')
        else:
            self.segments_tree.heading('select', text='Select')
            self.segments_tree.heading('segment_id', text='ID')
            self.segments_tree.heading('position', text='Position')
            self.segments_tree.heading('center_freq', text='Center Freq(MHz)')
            self.segments_tree.heading('bandwidth', text='Bandwidth(MHz)')
            self.segments_tree.heading('duration', text='Duration(ms)')
            self.segments_tree.heading('snr', text='SNR(dB)')
    
    def _setup_tree_columns(self, tree_font_size: int):
        """设置TreeView列宽"""
        font_scale = tree_font_size / 12  # 基于12号字体的缩放比例
        self.segments_tree.column('select', width=int(80 * font_scale))
        self.segments_tree.column('segment_id', width=int(80 * font_scale))
        self.segments_tree.column('position', width=int(160 * font_scale))
        self.segments_tree.column('center_freq', width=int(140 * font_scale))
        self.segments_tree.column('bandwidth', width=int(130 * font_scale))
        self.segments_tree.column('duration', width=int(130 * font_scale))
        self.segments_tree.column('snr', width=int(110 * font_scale))
    
    def toggle_panel_collapse(self, panel_id: str):
        """切换面板折叠状态"""
        if panel_id not in self.panels:
            return
        
        panel = self.panels[panel_id]
        is_collapsed = self.panel_collapsed.get(panel_id, False)
        
        if is_collapsed:
            # 展开面板
            panel['content_frame'].pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
            panel['collapse_btn'].config(text="−")
            self.panel_collapsed[panel_id] = False
        else:
            # 折叠面板
            panel['content_frame'].pack_forget()
            panel['collapse_btn'].config(text="+")
            self.panel_collapsed[panel_id] = True
    
    def _get_callback(self, callback_name: str) -> Callable:
        """获取回调函数"""
        return self.callbacks.get(callback_name, lambda *args: None)
    
    def set_callback(self, callback_name: str, callback: Callable):
        """设置回调函数"""
        self.callbacks[callback_name] = callback
    
    def set_callbacks(self, callbacks: Dict[str, Callable]):
        """批量设置回调函数"""
        self.callbacks.update(callbacks)
    
    def add_right_panel(self, panel_widget: tk.Widget, minsize: int = 600):
        """
        添加右侧面板
        
        Args:
            panel_widget: 右侧面板组件
            minsize: 最小尺寸
        """
        if self.main_paned:
            self.main_paned.add(panel_widget, minsize=minsize)
    
    def get_main_paned(self) -> tk.PanedWindow:
        """获取主分隔器"""
        return self.main_paned
    
    def get_left_paned(self) -> tk.PanedWindow:
        """获取左侧分隔器"""
        return self.left_paned
    
    def get_panel(self, panel_id: str) -> Optional[Dict[str, Any]]:
        """获取指定面板"""
        return self.panels.get(panel_id)
    
    def update_texts(self):
        """更新面板文本（用于语言切换）"""
        # 更新面板标题
        texts = self.i18n_manager.get_all_texts()
        
        if 'file_list' in self.panels:
            self.panels['file_list']['title_label'].config(text=texts['file_list'])
        if 'file_info' in self.panels:
            self.panels['file_info']['title_label'].config(text=texts['file_info'])
        if 'segment_results' in self.panels:
            self.panels['segment_results']['title_label'].config(text=texts['segment_results'])
        
        # 更新TreeView列标题
        if self.segments_tree:
            self._setup_tree_headings(self.i18n_manager.current_language)
