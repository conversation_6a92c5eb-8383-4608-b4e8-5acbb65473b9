"""
信号处理模块

负责信号的处理、分析和计算，包括FFT、时频图、频谱分析等功能。
保持所有原有的绘图优化和计算方法。
"""

import numpy as np
import concurrent.futures
from typing import Tuple, Optional, Any, Dict
from dataclasses import dataclass


@dataclass
class SignalMetadata:
    """信号元数据"""
    fs: float  # 采样率
    fc: float  # 中心频率
    length: int  # 信号长度
    duration: float  # 持续时间
    is_complex: bool = True  # 是否为复数信号


class SignalProcessor:
    """
    信号处理器
    
    负责信号的各种处理和分析，保持与原有代码完全一致的计算方法和优化。
    """
    
    def __init__(self, max_workers: int = 4):
        """
        初始化信号处理器
        
        Args:
            max_workers: 线程池最大工作线程数
        """
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.gpu_available = False
        self.gpu_backend = None
        
        # 检查GPU可用性
        self._check_gpu_availability()
    
    def _check_gpu_availability(self):
        """检查GPU加速可用性"""
        try:
            import cupy
            self.gpu_available = True
            self.gpu_backend = 'cupy'
            print("GPU加速可用 (CuPy)")
        except ImportError:
            try:
                import torch
                if torch.cuda.is_available():
                    self.gpu_available = True
                    self.gpu_backend = 'torch'
                    print("GPU加速可用 (PyTorch)")
            except ImportError:
                pass
        
        if not self.gpu_available:
            print("GPU加速不可用，使用CPU计算")
    
    def compute_fft_async(self, sig: np.ndarray, fs: float, fc: float) -> concurrent.futures.Future:
        """
        异步计算 FFT 频谱，采用 MATLAB 兼容的分段平均方法
        
        该方法实现了自适应的 FFT 计算策略，根据信号长度选择最优的处理方式：
        - 小数据量（<100万点）：直接FFT
        - 中等数据量（100万-500万点）：智能抽样FFT
        - 大数据量（>500万点）：分段平均FFT（MATLAB风格）
        
        Args:
            sig: 输入信号（复数）
            fs: 采样率 (Hz)
            fc: 中心频率 (Hz)
            
        Returns:
            Future: 包含 (freqs_mhz, fftdata) 的 Future 对象
        """
        def _compute():
            def compute_direct_fft(sig, fs, fc):
                """直接FFT计算（小数据量）"""
                sig_len = len(sig)

                # 尝试GPU加速
                if self.gpu_available and self.gpu_backend == 'cupy':
                    try:
                        import cupy as cp
                        sig_gpu = cp.asarray(sig)
                        fft_result = cp.fft.fft(sig_gpu)
                        fft_result = cp.asnumpy(fft_result)
                    except Exception:
                        fft_result = np.fft.fft(sig)
                elif self.gpu_available and self.gpu_backend == 'torch':
                    try:
                        import torch
                        sig_tensor = torch.from_numpy(sig).cuda()
                        fft_result = torch.fft.fft(sig_tensor).cpu().numpy()
                    except Exception:
                        fft_result = np.fft.fft(sig)
                else:
                    fft_result = np.fft.fft(sig)

                # 计算频率轴
                freqs = np.fft.fftfreq(sig_len, 1/fs)
                freqs_shifted = np.fft.fftshift(freqs)
                freqs_mhz = (freqs_shifted + fc) / 1e6

                # 计算功率谱密度并移位
                fftdata = np.abs(np.fft.fftshift(fft_result))**2
                fftdata = 10 * np.log10(fftdata + 1e-12)

                return fftdata, freqs_mhz
            
            def compute_smart_sampling_fft(sig, fs, fc):
                """智能抽样FFT（中等数据量）"""
                original_len = len(sig)
                target_len = 1048576  # 目标100万点
                step = max(1, original_len // target_len)
                
                # 智能抽样
                sampled_sig = sig[::step]
                effective_fs = fs / step
                
                # 计算FFT
                fft_result = np.fft.fft(sampled_sig)
                freqs = np.fft.fftfreq(len(sampled_sig), 1/effective_fs)
                freqs_shifted = np.fft.fftshift(freqs)
                freqs_mhz = (freqs_shifted + fc) / 1e6
                
                fftdata = np.abs(np.fft.fftshift(fft_result))**2
                fftdata = 10 * np.log10(fftdata + 1e-12)
                
                return fftdata, freqs_mhz
            
            def compute_welch_like_fft(sig, fs, fc):
                """分段平均FFT（MATLAB风格，大数据量）"""
                # MATLAB风格的分段参数
                fft_len = 65536  # 64K FFT长度，平衡分辨率和计算量
                overlap = fft_len // 2  # 50%重叠
                window = np.blackman(fft_len)  # 与MATLAB一致的窗函数
                
                # 计算分段数量
                step = fft_len - overlap
                num_segments = (len(sig) - overlap) // step
                
                if num_segments < 1:
                    # 信号太短，回退到直接FFT
                    return compute_direct_fft(sig, fs, fc)
                
                # 初始化累积器
                psd_accumulator = np.zeros(fft_len)
                
                # 分段处理
                for i in range(num_segments):
                    start_idx = i * step
                    end_idx = start_idx + fft_len
                    
                    if end_idx > len(sig):
                        break
                    
                    # 提取分段并加窗
                    segment = sig[start_idx:end_idx] * window
                    
                    # 计算FFT
                    fft_segment = np.fft.fft(segment)
                    psd_segment = np.abs(fft_segment)**2
                    
                    # 累积功率谱
                    psd_accumulator += psd_segment
                
                # 平均化
                psd_avg = psd_accumulator / num_segments
                
                # 计算频率轴
                freqs = np.fft.fftfreq(fft_len, 1/fs)
                freqs_shifted = np.fft.fftshift(freqs)
                freqs_mhz = (freqs_shifted + fc) / 1e6
                
                # 转换为dB并移位
                fftdata = 10 * np.log10(np.fft.fftshift(psd_avg) + 1e-12)
                
                return fftdata, freqs_mhz
            
            # 根据信号长度选择计算策略
            sig_len = len(sig)
            
            if sig_len <= 1000000:  # 100万点以下
                fftdata, freqs_mhz = compute_direct_fft(sig, fs, fc)
            elif sig_len <= 5000000:  # 100万-500万点
                fftdata, freqs_mhz = compute_smart_sampling_fft(sig, fs, fc)
            else:  # 500万点以上
                fftdata, freqs_mhz = compute_welch_like_fft(sig, fs, fc)
            
            return freqs_mhz, fftdata
        
        return self.thread_pool.submit(_compute)
    
    def compute_spectrogram_async(self, sig: np.ndarray, fs: float, fc: float) -> concurrent.futures.Future:
        """
        异步计算时频图，严格按照 MATLAB 的计算方式
        
        Args:
            sig: 输入信号（复数）
            fs: 采样率 (Hz)
            fc: 中心频率 (Hz)
            
        Returns:
            Future: 包含 (F_mhz, T_ms, Pxx_db, success) 的 Future 对象
        """
        def _compute():
            # 时频图数据优化：保持质量的前提下适度优化
            stft_sig = sig
            if len(sig) > 8000000:  # 超过800万点，适度抽样
                step = len(sig) // 2000000  # 目标约200万点，保持更多细节
                stft_sig = sig[::step]
                effective_fs = fs / step
            else:
                effective_fs = fs
            
            try:
                from scipy.signal import spectrogram
                
                # MATLAB兼容的参数设置
                nperseg = min(2048, len(stft_sig) // 8)  # 窗长度
                noverlap = nperseg // 2  # 50%重叠
                nfft = max(nperseg, 2048)  # FFT长度
                
                # 计算时频图
                f, t, Sxx = spectrogram(
                    stft_sig, 
                    fs=effective_fs,
                    window='blackman',  # 与MATLAB一致
                    nperseg=nperseg,
                    noverlap=noverlap,
                    nfft=nfft,
                    return_onesided=False,  # 双边谱
                    scaling='density'
                )
                
                # 移位到以fc为中心
                f_shifted = np.fft.fftshift(f)
                Sxx_shifted = np.fft.fftshift(Sxx, axes=0)
                
                # 转换单位
                F_mhz = (f_shifted + fc) / 1e6  # 转换为MHz
                T_ms = t * 1000  # 转换为ms
                
                # 转换为dB
                Pxx_db = 10 * np.log10(Sxx_shifted + 1e-12)
                
                return F_mhz, T_ms, Pxx_db, True
                
            except Exception as e:
                print(f"时频图计算失败: {e}")
                # 返回简化的幅度数据
                amplitude = np.abs(stft_sig)
                return None, None, amplitude, False
        
        return self.thread_pool.submit(_compute)
    
    def compute_fft_sync(self, signal_data: np.ndarray, fs: float, fc: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        同步计算FFT
        
        Args:
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            
        Returns:
            (freqs_mhz, fftdata): 频率和FFT数据
        """
        try:
            future = self.compute_fft_async(signal_data, fs, fc)
            freqs_mhz, fftdata = future.result()
            return freqs_mhz, fftdata
        except Exception as e:
            print(f"FFT计算失败: {e}")
            # 返回空数据
            freqs_mhz = np.array([])
            fftdata = np.array([])
            return freqs_mhz, fftdata
    
    def compute_spectrogram_sync(self, signal_data: np.ndarray, fs: float, fc: float) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], np.ndarray, bool]:
        """
        同步计算时频图
        
        Args:
            signal_data: 信号数据
            fs: 采样率
            fc: 中心频率
            
        Returns:
            (F_mhz, T_ms, Pxx_db, success): 频率、时间、功率谱密度和成功标志
        """
        try:
            future = self.compute_spectrogram_async(signal_data, fs, fc)
            return future.result()
        except Exception as e:
            print(f"时频图计算失败: {e}")
            amplitude = np.abs(signal_data)
            return None, None, amplitude, False

    def compute_spectrogram_fast(self, signal: np.ndarray, fs: float, fc: float) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], np.ndarray]:
        """
        快速时频图计算（用于分析图表，优化速度）

        Args:
            signal: 信号数据
            fs: 采样率
            fc: 中心频率

        Returns:
            (F_mhz, T_ms, Pxx_db): 频率、时间、功率谱密度
        """
        try:
            import numpy as np
            from scipy.signal import spectrogram

            # 快速计算参数
            nperseg = min(1024, len(signal) // 16)  # 更小的窗长度
            noverlap = nperseg // 4  # 25%重叠，减少计算量
            nfft = nperseg

            # 数据抽样以提高速度
            if len(signal) > 1000000:  # 超过100万点
                step = len(signal) // 500000  # 目标50万点
                signal = signal[::step]
                fs = fs / step

            # 计算时频图
            f, t, Sxx = spectrogram(
                signal,
                fs=fs,
                window='hann',  # 使用更快的窗函数
                nperseg=nperseg,
                noverlap=noverlap,
                nfft=nfft,
                return_onesided=False,
                scaling='density'
            )

            # 移位和单位转换
            f_shifted = np.fft.fftshift(f)
            Sxx_shifted = np.fft.fftshift(Sxx, axes=0)

            F_mhz = (f_shifted + fc) / 1e6
            T_ms = t * 1000
            Pxx_db = 10 * np.log10(Sxx_shifted + 1e-12)

            return F_mhz, T_ms, Pxx_db

        except Exception as e:
            print(f"快速时频图计算失败: {e}")
            return None, None, np.abs(signal)

    def find_adaptive_frequency_ticks(self, freqs_mhz: np.ndarray, fftdata: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        快速生成自适应频率刻度

        Args:
            freqs_mhz: 频率数组 (MHz)
            fftdata: FFT数据

        Returns:
            (tick_positions, tick_labels): 刻度位置和标签
        """
        try:
            # 频率范围
            freq_min, freq_max = freqs_mhz.min(), freqs_mhz.max()
            freq_range = freq_max - freq_min

            # 根据频率范围确定刻度间隔
            if freq_range > 1000:  # 大于1GHz
                tick_interval = 100  # 100MHz间隔
            elif freq_range > 100:  # 100MHz-1GHz
                tick_interval = 50   # 50MHz间隔
            elif freq_range > 50:   # 50-100MHz
                tick_interval = 10   # 10MHz间隔
            elif freq_range > 10:   # 10-50MHz
                tick_interval = 5    # 5MHz间隔
            else:                   # 小于10MHz
                tick_interval = 1    # 1MHz间隔

            # 生成刻度
            start_tick = np.ceil(freq_min / tick_interval) * tick_interval
            end_tick = np.floor(freq_max / tick_interval) * tick_interval
            tick_positions = np.arange(start_tick, end_tick + tick_interval, tick_interval)

            # 限制刻度数量
            if len(tick_positions) > 10:
                tick_positions = tick_positions[::2]  # 减半

            # 生成标签
            tick_labels = [f"{tick:.0f}" for tick in tick_positions]

            return tick_positions, np.array(tick_labels)

        except Exception as e:
            print(f"生成频率刻度失败: {e}")
            # 返回简单的刻度
            tick_positions = np.linspace(freqs_mhz.min(), freqs_mhz.max(), 5)
            tick_labels = [f"{tick:.1f}" for tick in tick_positions]
            return tick_positions, np.array(tick_labels)

    def get_signal_statistics(self, signal: np.ndarray, metadata: SignalMetadata) -> Dict[str, Any]:
        """
        获取信号统计信息

        Args:
            signal: 信号数据
            metadata: 信号元数据

        Returns:
            信号统计信息字典
        """
        try:
            stats = {
                'length': len(signal),
                'duration_ms': len(signal) / metadata.fs * 1000,
                'sample_rate_mhz': metadata.fs / 1e6,
                'center_frequency_mhz': metadata.fc / 1e6,
                'is_complex': metadata.is_complex,
                'mean_amplitude': np.mean(np.abs(signal)),
                'max_amplitude': np.max(np.abs(signal)),
                'min_amplitude': np.min(np.abs(signal)),
                'std_amplitude': np.std(np.abs(signal)),
                'dynamic_range_db': 20 * np.log10(np.max(np.abs(signal)) / (np.min(np.abs(signal)) + 1e-12))
            }

            if metadata.is_complex:
                stats.update({
                    'mean_real': np.mean(signal.real),
                    'mean_imag': np.mean(signal.imag),
                    'std_real': np.std(signal.real),
                    'std_imag': np.std(signal.imag)
                })

            return stats

        except Exception as e:
            print(f"计算信号统计信息失败: {e}")
            return {
                'length': len(signal),
                'duration_ms': 0,
                'sample_rate_mhz': 0,
                'center_frequency_mhz': 0,
                'is_complex': False
            }

    def validate_signal_data(self, signal: np.ndarray, metadata: SignalMetadata) -> Tuple[bool, str]:
        """
        验证信号数据的有效性

        Args:
            signal: 信号数据
            metadata: 信号元数据

        Returns:
            (is_valid, error_message): 验证结果和错误信息
        """
        try:
            # 检查信号是否为空
            if signal is None or len(signal) == 0:
                return False, "信号数据为空"

            # 检查信号长度
            if len(signal) < 10:
                return False, f"信号数据过短: {len(signal)} 个采样点"

            # 检查是否包含NaN或无穷大
            if np.any(np.isnan(signal)) or np.any(np.isinf(signal)):
                return False, "信号数据包含NaN或无穷大值"

            # 检查采样率
            if metadata.fs <= 0:
                return False, f"无效的采样率: {metadata.fs}"

            # 检查信号幅度
            max_amplitude = np.max(np.abs(signal))
            if max_amplitude == 0:
                return False, "信号幅度为零"

            # 检查动态范围
            min_amplitude = np.min(np.abs(signal))
            if min_amplitude > 0:
                dynamic_range = max_amplitude / min_amplitude
                if dynamic_range > 1e12:  # 动态范围过大可能有问题
                    return False, f"信号动态范围过大: {dynamic_range:.2e}"

            return True, "信号数据有效"

        except Exception as e:
            return False, f"验证信号数据时发生错误: {e}"

    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'thread_pool'):
            self.thread_pool.shutdown(wait=True)
