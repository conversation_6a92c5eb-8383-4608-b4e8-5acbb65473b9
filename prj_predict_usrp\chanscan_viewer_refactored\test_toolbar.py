#!/usr/bin/env python3
"""
测试工具栏显示

用于调试工具栏显示问题
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_toolbar():
    """测试工具栏显示"""
    print("开始测试工具栏...")
    
    try:
        # 创建根窗口
        root = tk.Tk()
        root.title("工具栏测试")
        root.geometry("800x600")
        
        # 导入必要的模块
        from utils.i18n import I18nManager
        from ui.styles import StyleManager
        from ui.toolbar import Toolbar
        
        # 创建管理器
        i18n_manager = I18nManager('zh')
        style_manager = StyleManager('zh')
        
        # 创建工具栏
        print("创建工具栏...")
        toolbar = Toolbar(root, style_manager, i18n_manager)
        
        # 检查工具栏框架是否存在
        if hasattr(toolbar, 'toolbar_frame') and toolbar.toolbar_frame:
            print("✅ 工具栏框架已创建")
            
            # 手动pack工具栏
            print("手动pack工具栏...")
            toolbar.pack_toolbar()
            
            # 检查工具栏是否有子组件
            children = toolbar.toolbar_frame.winfo_children()
            print(f"工具栏子组件数量: {len(children)}")
            
            if children:
                print("✅ 工具栏有子组件")
                for i, child in enumerate(children):
                    print(f"  子组件 {i}: {child}")
            else:
                print("❌ 工具栏没有子组件")
        else:
            print("❌ 工具栏框架未创建")
        
        # 添加一个测试标签来验证布局
        test_label = tk.Label(root, text="这是测试标签，应该在工具栏下方", bg="yellow")
        test_label.pack(fill=tk.X, pady=10)
        
        print("启动GUI...")
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_toolbar()
