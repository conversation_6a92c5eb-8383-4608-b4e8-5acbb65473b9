#!/usr/bin/env python3
"""
分页文件列表管理器模块

提供文件列表的分页显示功能，包括智能模式切换、页面计算、缓存管理等核心功能。
复用现有的缓存机制设计模式，参考computation_cache的实现。
"""

import math
from typing import List, Optional, Tuple


class PaginatedFileListManager:
    """
    分页文件列表管理器
    
    实现文件列表的分页逻辑，包括智能模式切换、页面计算、缓存管理等核心功能。
    当文件数量超过阈值时自动启用分页模式，小文件数量时保持原有行为。
    """
    
    def __init__(self, threshold: int = 100, page_size: int = 100):
        """
        初始化分页文件列表管理器
        
        Args:
            threshold: 启用分页的文件数量阈值，默认100
            page_size: 每页显示的文件数量，默认100
        """
        # 分页配置
        self.file_list_threshold = threshold
        self.page_size = page_size
        
        # 当前状态
        self.current_page = 0
        self.all_files = []
        self.pagination_enabled = False
        
        # 缓存机制（参考现有的computation_cache设计）
        self.page_cache = {}
        self.max_cached_pages = 3  # 最多缓存3个页面，与现有cache_max_size一致
        
        # 统计信息
        self.total_pages = 0
        self.total_files = 0
    
    def should_enable_pagination(self, file_count: int) -> bool:
        """
        判断是否应该启用分页模式
        
        Args:
            file_count: 文件总数
            
        Returns:
            bool: 是否应该启用分页
        """
        return file_count > self.file_list_threshold
    
    def set_files(self, files: List[str]) -> None:
        """
        设置文件列表并重置分页状态
        
        Args:
            files: 文件路径列表
        """
        self.all_files = files
        self.total_files = len(files)
        self.current_page = 0
        
        # 清空缓存
        self.page_cache.clear()
        
        # 更新分页状态
        self.pagination_enabled = self.should_enable_pagination(self.total_files)
        
        if self.pagination_enabled:
            self.total_pages = math.ceil(self.total_files / self.page_size)
            print(f"启用分页模式: 文件数量 {self.total_files:,} > {self.file_list_threshold:,}")
            print(f"分页配置: 每页{self.page_size}个文件，共{self.total_pages}页")
        else:
            self.total_pages = 1
    
    def get_current_page_files(self) -> List[str]:
        """
        获取当前页的文件列表
        
        Returns:
            List[str]: 当前页的文件路径列表
        """
        if not self.pagination_enabled:
            return self.all_files
        
        return self._get_page_files(self.current_page)
    
    def _get_page_files(self, page_num: int) -> List[str]:
        """
        获取指定页面的文件列表（带缓存）
        
        Args:
            page_num: 页面编号（从0开始）
            
        Returns:
            List[str]: 指定页面的文件路径列表
        """
        # 检查缓存
        cache_key = f"page_{page_num}_{self.total_files}"
        if cache_key in self.page_cache:
            return self.page_cache[cache_key]
        
        # 计算页面范围
        start = page_num * self.page_size
        end = min(start + self.page_size, self.total_files)
        
        # 获取页面文件
        page_files = self.all_files[start:end]
        
        # 缓存管理：如果缓存已满，删除最旧的缓存
        if len(self.page_cache) >= self.max_cached_pages:
            # 删除第一个（最旧的）缓存项
            oldest_key = next(iter(self.page_cache))
            del self.page_cache[oldest_key]
        
        # 添加到缓存
        self.page_cache[cache_key] = page_files
        
        return page_files
    
    def next_page(self) -> bool:
        """
        切换到下一页
        
        Returns:
            bool: 是否成功切换到下一页
        """
        if not self.pagination_enabled:
            return False
        
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            return True
        return False
    
    def prev_page(self) -> bool:
        """
        切换到上一页
        
        Returns:
            bool: 是否成功切换到上一页
        """
        if not self.pagination_enabled:
            return False
        
        if self.current_page > 0:
            self.current_page -= 1
            return True
        return False
    
    def goto_page(self, page_num: int) -> bool:
        """
        跳转到指定页面
        
        Args:
            page_num: 目标页面编号（从0开始）
            
        Returns:
            bool: 是否成功跳转
        """
        if not self.pagination_enabled:
            return False
        
        if 0 <= page_num < self.total_pages:
            self.current_page = page_num
            return True
        return False
    
    def get_page_info(self) -> Tuple[int, int, int, int]:
        """
        获取分页信息
        
        Returns:
            Tuple[int, int, int, int]: (当前页号(1-based), 总页数, 当前页起始文件索引(1-based), 当前页结束文件索引(1-based))
        """
        if not self.pagination_enabled:
            return (1, 1, 1, self.total_files)
        
        current_page_1based = self.current_page + 1
        start_file_1based = self.current_page * self.page_size + 1
        end_file_1based = min(start_file_1based + self.page_size - 1, self.total_files)
        
        return (current_page_1based, self.total_pages, start_file_1based, end_file_1based)
    
    def find_file_page(self, file_path: str) -> Optional[int]:
        """
        查找指定文件所在的页面
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[int]: 文件所在的页面编号（从0开始），如果文件不存在则返回None
        """
        try:
            file_index = self.all_files.index(file_path)
            if self.pagination_enabled:
                return file_index // self.page_size
            else:
                return 0
        except ValueError:
            return None
    
    def get_total_pages(self) -> int:
        """
        获取总页数
        
        Returns:
            int: 总页数
        """
        return self.total_pages
    
    def get_total_files(self) -> int:
        """
        获取文件总数
        
        Returns:
            int: 文件总数
        """
        return self.total_files
    
    def is_pagination_enabled(self) -> bool:
        """
        检查是否启用了分页模式
        
        Returns:
            bool: 是否启用分页模式
        """
        return self.pagination_enabled
    
    def clear_cache(self) -> None:
        """
        清空页面缓存
        """
        self.page_cache.clear()
    
    def get_cache_info(self) -> Tuple[int, int]:
        """
        获取缓存信息
        
        Returns:
            Tuple[int, int]: (当前缓存页面数, 最大缓存页面数)
        """
        return (len(self.page_cache), self.max_cached_pages)
