"""
自定义消息框模块

提供支持多语言和字体缩放的自定义消息框功能。
"""

import tkinter as tk
import tkinter.font as tkFont
import platform


class ScaledMessageBox:
    """
    自定义的放大弹窗类，支持多语言和 1.5 倍字体放大
    """
    
    @staticmethod
    def get_scaled_font(language='zh'):
        system = platform.system()
        if system == 'Windows':
            font_family = 'Microsoft YaHei' if language == 'zh' else 'Segoe UI'
        elif system == 'Linux':
            if language == 'zh':
                # Linux中文字体候选列表，移除Microsoft YaHei避免警告
                chinese_candidates = [
                    'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
                    'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
                    'Liberation Sans', 'DejaVu Sans'
                ]
                # 检查可用字体
                available_fonts = tkFont.families()
                font_family = 'DejaVu Sans'  # 默认值
                for font in chinese_candidates:
                    if font in available_fonts:
                        font_family = font
                        break
            else:
                font_family = 'DejaVu Sans'
        else:
            font_family = 'Liberation Sans'  # 其他系统使用更通用的字体
        
        base_size = int(9 * 1.5)
        return tkFont.Font(family=font_family, size=base_size, weight='normal')

    @staticmethod
    def _calculate_text_size(text, font):
        """
        计算文本在给定字体下的显示尺寸

        Args:
            text (str): 要计算的文本
            font (tkinter.font.Font): 字体对象

        Returns:
            tuple: (width, height) 文本的宽度和高度
        """
        # 创建临时的根窗口来测量文本
        temp_root = tk.Tk()
        temp_root.withdraw()  # 隐藏窗口

        try:
            # 创建临时标签来测量文本尺寸
            temp_label = tk.Label(temp_root, text=text, font=font, wraplength=0)
            temp_label.update_idletasks()

            # 获取文本尺寸
            width = temp_label.winfo_reqwidth()
            height = temp_label.winfo_reqheight()

            # 对于多行文本，计算最长行的宽度
            if '\n' in text:
                lines = text.split('\n')
                max_line_width = 0
                for line in lines:
                    if line.strip():  # 忽略空行
                        line_label = tk.Label(temp_root, text=line, font=font)
                        line_label.update_idletasks()
                        line_width = line_label.winfo_reqwidth()
                        max_line_width = max(max_line_width, line_width)
                width = max_line_width

            # 添加额外的缓冲空间，确保文本完全显示
            width = int(width * 1.1)  # 增加10%的缓冲空间

            return width, height

        finally:
            temp_root.destroy()

    @staticmethod
    def showinfo(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'info', parent, language)
    
    @staticmethod
    def showwarning(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'warning', parent, language)
    
    @staticmethod
    def showerror(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'error', parent, language)
    
    @staticmethod
    def askokcancel(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'okcancel', parent, language)
    
    @staticmethod
    def askyesno(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'yesno', parent, language)

    @staticmethod
    def askyesnocancel(title, message, parent=None, language='zh'):
        return ScaledMessageBox._show_dialog(title, message, 'yesnocancel', parent, language)

    @staticmethod
    def _show_dialog(title, message, dialog_type, parent=None, language='zh'):
        # 如果是错误信息，同时输出到命令行
        if dialog_type == 'error':
            print(f"\n=== 错误信息 ===")
            print(f"标题: {title}")
            print(f"详情: {message}")
            print("=" * 50)

        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.resizable(True, True)  # 允许调整大小
        dialog.grab_set()

        # 确保对话框始终在最顶层
        dialog.attributes('-topmost', True)
        dialog.lift()
        dialog.focus_force()

        # 将对话框添加到活动对话框列表中（如果parent是主窗口）
        if parent and hasattr(parent, 'active_dialogs'):
            parent.active_dialogs.append(dialog)

        # 获取字体信息用于计算文本尺寸
        font = ScaledMessageBox.get_scaled_font(language)

        # 计算文本实际需要的尺寸
        text_width, _ = ScaledMessageBox._calculate_text_size(message, font)

        # 设置最小和最大宽度
        min_width = 300
        max_width = int(dialog.winfo_screenwidth() * 0.8)  # 屏幕宽度的80%

        # 智能计算边距：根据文本长度调整
        icon_width = 80   # 图标和间距的宽度

        # 根据文本宽度动态调整边距
        if text_width < 200:
            # 短文本：使用较小的边距
            padding_width = 80
        elif text_width < 400:
            # 中等文本：使用中等边距
            padding_width = 100
        else:
            # 长文本：使用较大的边距
            padding_width = 120

        required_width = text_width + icon_width + padding_width

        # 应用宽度限制
        width = max(min_width, min(required_width, max_width))

        # 对于短消息，避免窗口过宽
        if text_width < 150 and width > 450:
            width = 450

        # 计算高度
        base_height = 200 if dialog_type == 'error' else 150
        height = int(base_height * 1.5)

        # 根据消息行数动态调整高度
        msg_lines = message.count('\n') + 1
        if msg_lines > 3:
            height += int(25 * (msg_lines - 3) * 1.5)

        # 如果文本宽度超过了最大宽度，需要增加高度来容纳换行
        if text_width > (max_width - icon_width - padding_width):
            # 估算需要的额外行数
            extra_lines = text_width // (max_width - icon_width - padding_width)
            height += int(25 * extra_lines * 1.5)

        if parent:
            parent_x = parent.winfo_rootx()
            parent_y = parent.winfo_rooty()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
        else:
            x = (dialog.winfo_screenwidth() - width) // 2
            y = (dialog.winfo_screenheight() - height) // 2

        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # font变量已在前面定义，这里只需要定义button_font
        button_font = ScaledMessageBox.get_scaled_font(language)

        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # 图标框架
        icon_frame = tk.Frame(main_frame)
        icon_frame.pack(side=tk.LEFT, padx=(0, 15))

        system = platform.system()

        if dialog_type == 'info':
            icon_text = "[i]" if system == 'Linux' else "ℹ️"
            icon_color = "#1E90FF"
        elif dialog_type == 'warning':
            icon_text = "/!\\" if system == 'Linux' else "⚠️"
            icon_color = "#FF8C00"
        elif dialog_type == 'error':
            icon_text = "[X]" if system == 'Linux' else "❌"
            icon_color = "#DC143C"
        else:
            icon_text = "[?]" if system == 'Linux' else "❓"
            icon_color = "#4682B4"

        if system == 'Linux':
            icon_font = ('monospace', int(20 * 1.5), 'bold')
        else:
            icon_font = ('Arial', int(24 * 1.5))

        icon_label = tk.Label(icon_frame, text=icon_text, font=icon_font, fg=icon_color)
        icon_label.pack()

        # 消息文本
        msg_frame = tk.Frame(main_frame)
        msg_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 对于长消息或错误消息，使用Text控件和滚动条
        if len(message) > 200 or dialog_type == 'error' or message.count('\n') > 5:
            # 创建Text控件和滚动条
            text_frame = tk.Frame(msg_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            msg_text = tk.Text(text_frame, font=font, wrap=tk.WORD,
                              yscrollcommand=scrollbar.set, state=tk.NORMAL,
                              height=min(15, max(5, message.count('\n') + 3)))
            msg_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.config(command=msg_text.yview)

            msg_text.insert(tk.END, message)
            msg_text.config(state=tk.DISABLED)
        else:
            # 对于短消息，使用Label
            msg_label = tk.Label(msg_frame, text=message, font=font, wraplength=int(350 * 1.5),
                               justify=tk.LEFT, anchor='w')
            msg_label.pack(fill=tk.BOTH, expand=True)

        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=(0, 20))

        result = [None]

        def on_ok():
            result[0] = True
            dialog.destroy()

        def on_cancel():
            result[0] = False
            dialog.destroy()

        def on_yes():
            result[0] = True
            dialog.destroy()

        def on_no():
            result[0] = False
            dialog.destroy()

        def on_cancel_yesnocancel():
            result[0] = None
            dialog.destroy()

        if language == 'zh':
            ok_text, cancel_text, yes_text, no_text = "确定", "取消", "是", "否"
        else:
            ok_text, cancel_text, yes_text, no_text = "OK", "Cancel", "Yes", "No"

        if dialog_type in ['info', 'warning', 'error']:
            ok_btn = tk.Button(button_frame, text=ok_text, command=on_ok, font=button_font, width=12)
            ok_btn.pack(side=tk.RIGHT)
            result[0] = True
        elif dialog_type == 'okcancel':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)

            ok_btn = tk.Button(btn_container, text=ok_text, command=on_ok, font=button_font, width=10)
            ok_btn.pack(side=tk.RIGHT, padx=(0, 10))

            cancel_btn = tk.Button(btn_container, text=cancel_text, command=on_cancel, font=button_font, width=10)
            cancel_btn.pack(side=tk.RIGHT)

            result[0] = False
        elif dialog_type == 'yesno':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)

            yes_btn = tk.Button(btn_container, text=yes_text, command=on_yes, font=button_font, width=10)
            yes_btn.pack(side=tk.RIGHT, padx=(0, 10))

            no_btn = tk.Button(btn_container, text=no_text, command=on_no, font=button_font, width=10)
            no_btn.pack(side=tk.RIGHT)

            result[0] = False
        elif dialog_type == 'yesnocancel':
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)

            yes_btn = tk.Button(btn_container, text=yes_text, command=on_yes, font=button_font, width=8)
            yes_btn.pack(side=tk.RIGHT, padx=(0, 8))

            no_btn = tk.Button(btn_container, text=no_text, command=on_no, font=button_font, width=8)
            no_btn.pack(side=tk.RIGHT, padx=(0, 8))

            cancel_btn = tk.Button(btn_container, text=cancel_text, command=on_cancel_yesnocancel, font=button_font, width=8)
            cancel_btn.pack(side=tk.RIGHT)

            result[0] = None

        def on_escape(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            elif dialog_type == 'yesnocancel':
                on_cancel_yesnocancel()
            else:
                on_cancel()
        dialog.bind('<Escape>', on_escape)

        def on_enter(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            elif dialog_type == 'okcancel':
                on_ok()
            elif dialog_type == 'yesno':
                on_yes()
            elif dialog_type == 'yesnocancel':
                on_yes()
        dialog.bind('<Return>', on_enter)

        if dialog_type in ['info', 'warning', 'error']:
            ok_btn.focus_set()
        elif dialog_type == 'okcancel':
            ok_btn.focus_set()
        elif dialog_type == 'yesno':
            yes_btn.focus_set()
        elif dialog_type == 'yesnocancel':
            yes_btn.focus_set()

        dialog.wait_window()
        return result[0]
