#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查tkinter实际能识别的字体名称
用于调试字体检查问题
"""

import platform

def check_tkinter_fonts():
    """检查tkinter字体"""
    if platform.system() != 'Linux':
        print("此脚本仅适用于Linux系统")
        return
    
    try:
        import tkinter as tk
        import tkinter.font as tkFont
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        available_fonts = list(tkFont.families())
        print(f"tkinter可识别的字体总数: {len(available_fonts)}")
        
        # 检查预期的中文字体
        expected_fonts = [
            'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN',
            'Liberation Sans', 'DejaVu Sans'
        ]
        
        print("\n=== 预期字体检查 ===")
        found_expected = []
        for font in expected_fonts:
            if font in available_fonts:
                print(f"✓ {font}")
                found_expected.append(font)
            else:
                print(f"✗ {font}")
        
        # 搜索包含关键词的字体
        keywords = ['wenquanyi', 'noto', 'cjk', 'droid', 'liberation', 'dejavu', 'zen', 'hei']
        print(f"\n=== 搜索包含关键词的字体 ===")
        found_related = []
        for font in available_fonts:
            font_lower = font.lower()
            for keyword in keywords:
                if keyword in font_lower:
                    found_related.append(font)
                    break
        
        if found_related:
            print("找到相关字体:")
            for font in sorted(set(found_related)):
                print(f"  - {font}")
        else:
            print("未找到相关字体")
        
        # 显示所有字体（前50个）
        print(f"\n=== 所有可用字体（前50个）===")
        for i, font in enumerate(sorted(available_fonts)[:50]):
            print(f"  {i+1:2d}. {font}")
        
        if len(available_fonts) > 50:
            print(f"  ... 还有 {len(available_fonts) - 50} 个字体")
        
        # 销毁窗口
        root.destroy()
        
        # 总结
        print(f"\n=== 总结 ===")
        print(f"预期字体找到: {len(found_expected)}/{len(expected_fonts)}")
        print(f"相关字体找到: {len(set(found_related))}")
        
        if found_expected:
            print(f"建议使用: {found_expected[0]}")
        elif found_related:
            print(f"可以使用: {found_related[0]}")
        else:
            print("需要进一步调试")
        
        return found_expected, found_related
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()
        return [], []

if __name__ == "__main__":
    check_tkinter_fonts()
