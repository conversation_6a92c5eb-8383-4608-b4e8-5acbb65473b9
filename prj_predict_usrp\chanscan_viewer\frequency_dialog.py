#!/usr/bin/env python3
"""
频率输入对话框模块

提供用于创建和验证频率输入的对话框功能
"""

import tkinter as tk
from tkinter import simpledialog, messagebox

class FrequencyInputDialog:
    """
    频率输入对话框类
    
    提供创建频率输入对话框和验证频率输入的功能
    """
    
    @staticmethod
    def create_dialog(parent, language='zh', initial_value=2446.0):
        """
        创建频率输入对话框（居中显示）

        Args:
            parent: 父窗口
            language: 语言设置，'zh'为中文，其他为英文
            initial_value: 默认频率值，单位MHz

        Returns:
            float or None: 用户输入的频率值(MHz)，如果用户取消则返回None
        """
        # 设置标题和提示文本
        freq_title = "输入中心频率" if language == 'zh' else "Enter Center Frequency"
        freq_prompt = "请输入中心频率 (MHz):" if language == 'zh' else "Please enter center frequency (MHz):"

        # 创建自定义的居中对话框
        return FrequencyInputDialog._create_centered_dialog(
            parent, freq_title, freq_prompt, initial_value, language
        )

    @staticmethod
    def _create_centered_dialog(parent, title, prompt, initial_value, language):
        """创建居中显示的频率输入对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.resizable(False, False)
        dialog.grab_set()  # 模态对话框

        # 设置窗口大小
        width = 400
        height = 200

        # 居中显示
        if parent:
            parent_x = parent.winfo_rootx()
            parent_y = parent.winfo_rooty()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
        else:
            x = (dialog.winfo_screenwidth() - width) // 2
            y = (dialog.winfo_screenheight() - height) // 2

        dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 存储结果
        result = [None]

        # 创建主框架
        main_frame = tk.Frame(dialog, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 添加提示标签
        prompt_label = tk.Label(main_frame, text=prompt, font=('Arial', 12))
        prompt_label.pack(pady=(0, 15))

        # 添加输入框
        entry_var = tk.StringVar(value=str(initial_value))
        entry = tk.Entry(main_frame, textvariable=entry_var, font=('Arial', 12), width=20)
        entry.pack(pady=(0, 20))
        entry.focus_set()
        entry.select_range(0, tk.END)

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack()

        # 确定按钮
        def on_ok():
            try:
                value = float(entry_var.get())
                if 1.0 <= value <= 6000.0:
                    result[0] = value
                    dialog.destroy()
                else:
                    error_msg = "频率必须在1.0到6000.0 MHz之间" if language == 'zh' else "Frequency must be between 1.0 and 6000.0 MHz"
                    tk.messagebox.showerror("范围错误" if language == 'zh' else "Range Error", error_msg, parent=dialog)
            except ValueError:
                error_msg = "请输入有效的数值" if language == 'zh' else "Please enter a valid number"
                tk.messagebox.showerror("输入错误" if language == 'zh' else "Input Error", error_msg, parent=dialog)

        # 取消按钮
        def on_cancel():
            dialog.destroy()

        ok_text = "确定" if language == 'zh' else "OK"
        cancel_text = "取消" if language == 'zh' else "Cancel"

        ok_button = tk.Button(button_frame, text=ok_text, command=on_ok, font=('Arial', 10), width=8)
        ok_button.pack(side=tk.LEFT, padx=(0, 10))

        cancel_button = tk.Button(button_frame, text=cancel_text, command=on_cancel, font=('Arial', 10), width=8)
        cancel_button.pack(side=tk.LEFT)

        # 绑定回车键
        entry.bind('<Return>', lambda e: on_ok())
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 等待对话框关闭
        dialog.wait_window()

        return result[0]
    
    @staticmethod
    def validate_frequency(freq_mhz, parent=None, language='zh'):
        """
        验证频率输入是否有效
        
        Args:
            freq_mhz: 输入的频率值，单位MHz
            parent: 父窗口，用于显示错误消息
            language: 语言设置，'zh'为中文，其他为英文
            
        Returns:
            tuple: (是否有效, 转换后的频率值(Hz))
        """
        # 检查是否为None（用户取消）
        if freq_mhz is None:
            return False, None
        
        # 检查是否为数值
        try:
            freq_mhz = float(freq_mhz)
        except (ValueError, TypeError):
            if parent:
                error_title = "输入错误" if language == 'zh' else "Input Error"
                error_msg = "请输入有效的数值" if language == 'zh' else "Please enter a valid number"
                
                # 导入消息框模块（如果在类外部定义）
                try:
                    from tkinter import messagebox
                    messagebox.showerror(error_title, error_msg, parent=parent)
                except ImportError:
                    # 如果无法导入，则使用标准的tkinter消息框
                    import tkinter as tk
                    tk.messagebox.showerror(error_title, error_msg, parent=parent)
            return False, None
        
        # 检查范围（1MHz到6GHz）
        if freq_mhz < 1.0 or freq_mhz > 6000.0:
            if parent:
                error_title = "范围错误" if language == 'zh' else "Range Error"
                error_msg = "频率必须在1.0到6000.0 MHz之间" if language == 'zh' else "Frequency must be between 1.0 and 6000.0 MHz"
                
                try:
                    from tkinter import messagebox
                    messagebox.showerror(error_title, error_msg, parent=parent)
                except ImportError:
                    import tkinter as tk
                    tk.messagebox.showerror(error_title, error_msg, parent=parent)
            return False, None
        
        # 转换单位：MHz到Hz
        freq_hz = freq_mhz * 1e6
        
        return True, freq_hz


class FileCountInputDialog:
    """
    文件个数输入对话框类

    提供创建文件个数输入对话框和验证输入的功能
    """

    @staticmethod
    def create_dialog(parent, language='zh', initial_value=10):
        """
        创建文件个数输入对话框（居中显示）

        Args:
            parent: 父窗口
            language: 语言设置，'zh'为中文，其他为英文
            initial_value: 默认文件个数

        Returns:
            int or None: 用户输入的文件个数，如果用户取消则返回None
        """
        # 设置标题和提示文本
        count_title = "输入采集文件个数" if language == 'zh' else "Enter Number of Files to Capture"
        count_prompt = "请输入要采集的文件个数 (1-100):" if language == 'zh' else "Please enter number of files to capture (1-100):"

        # 创建自定义的居中对话框
        return FileCountInputDialog._create_centered_dialog(
            parent, count_title, count_prompt, initial_value, language
        )

    @staticmethod
    def _create_centered_dialog(parent, title, prompt, initial_value, language):
        """
        创建居中的输入对话框

        Args:
            parent: 父窗口
            title: 对话框标题
            prompt: 提示文本
            initial_value: 初始值
            language: 语言设置

        Returns:
            int or None: 用户输入的值，如果取消则返回None
        """
        # 创建顶级窗口
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.resizable(False, False)
        dialog.grab_set()  # 模态对话框

        # 设置窗口大小
        dialog_width = 350
        dialog_height = 150

        # 计算居中位置
        if parent:
            parent_x = parent.winfo_rootx()
            parent_y = parent.winfo_rooty()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()

            x = parent_x + (parent_width - dialog_width) // 2
            y = parent_y + (parent_height - dialog_height) // 2
        else:
            # 如果没有父窗口，则在屏幕中央显示
            screen_width = dialog.winfo_screenwidth()
            screen_height = dialog.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2

        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        # 存储结果的变量
        result = [None]

        # 创建主框架
        main_frame = tk.Frame(dialog, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # 提示标签
        prompt_label = tk.Label(main_frame, text=prompt, font=('Arial', 10))
        prompt_label.pack(pady=(0, 10))

        # 输入框
        entry_var = tk.StringVar(value=str(initial_value))
        entry = tk.Entry(main_frame, textvariable=entry_var, font=('Arial', 10), width=20, justify='center')
        entry.pack(pady=(0, 15))
        entry.select_range(0, tk.END)  # 选中所有文本
        entry.focus()  # 设置焦点

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack()

        def on_ok():
            try:
                value = int(entry_var.get().strip())
                if 1 <= value <= 100:
                    result[0] = value
                    dialog.destroy()
                else:
                    error_title = "输入错误" if language == 'zh' else "Input Error"
                    error_msg = "文件个数必须在1到100之间" if language == 'zh' else "Number of files must be between 1 and 100"
                    messagebox.showerror(error_title, error_msg, parent=dialog)
                    entry.select_range(0, tk.END)
                    entry.focus()
            except ValueError:
                error_title = "输入错误" if language == 'zh' else "Input Error"
                error_msg = "请输入有效的数字" if language == 'zh' else "Please enter a valid number"
                messagebox.showerror(error_title, error_msg, parent=dialog)
                entry.select_range(0, tk.END)
                entry.focus()

        def on_cancel():
            dialog.destroy()

        # 确定按钮
        ok_text = "确定" if language == 'zh' else "OK"
        ok_button = tk.Button(button_frame, text=ok_text, command=on_ok, width=8)
        ok_button.pack(side='left', padx=(0, 10))

        # 取消按钮
        cancel_text = "取消" if language == 'zh' else "Cancel"
        cancel_button = tk.Button(button_frame, text=cancel_text, command=on_cancel, width=8)
        cancel_button.pack(side='left')

        # 绑定回车键
        entry.bind('<Return>', lambda e: on_ok())
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 等待对话框关闭
        dialog.wait_window()

        return result[0]

    @staticmethod
    def validate_file_count(file_count, parent=None, language='zh'):
        """
        验证文件个数输入

        Args:
            file_count: 要验证的文件个数
            parent: 父窗口（用于显示错误消息）
            language: 语言设置，'zh'为中文，其他为英文

        Returns:
            tuple: (是否有效, 验证后的文件个数)
        """
        # 检查是否为None
        if file_count is None:
            return False, None

        # 检查是否为有效数字
        try:
            file_count = int(file_count)
        except (ValueError, TypeError):
            if parent:
                error_title = "输入错误" if language == 'zh' else "Input Error"
                error_msg = "请输入有效的数字" if language == 'zh' else "Please enter a valid number"

                try:
                    from tkinter import messagebox
                    messagebox.showerror(error_title, error_msg, parent=parent)
                except ImportError:
                    import tkinter as tk
                    tk.messagebox.showerror(error_title, error_msg, parent=parent)
            return False, None

        # 检查范围（1到100）
        if file_count < 1 or file_count > 100:
            if parent:
                error_title = "范围错误" if language == 'zh' else "Range Error"
                error_msg = "文件个数必须在1到100之间" if language == 'zh' else "Number of files must be between 1 and 100"

                try:
                    from tkinter import messagebox
                    messagebox.showerror(error_title, error_msg, parent=parent)
                except ImportError:
                    import tkinter as tk
                    tk.messagebox.showerror(error_title, error_msg, parent=parent)
            return False, None

        return True, file_count